package cn.com.vau.common.storage

import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.view.share.ShareSettingData
import cn.com.vau.data.account.KycFunctionPermissionObj
import cn.com.vau.data.ib.InvitationsObj
import cn.com.vau.data.init.ServerBaseUrlBean
import cn.com.vau.data.profile.TelegramH5ResBean
import cn.com.vau.data.strategy.StrategyBean
import cn.com.vau.util.GsonUtil
import cn.com.vau.util.json
import com.google.gson.reflect.TypeToken
import com.upex.common.drawTools.DrawToolShape
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

object SpManager {
    /** region ------退出登录清理开始------- */

    /**
     * 神策 -> 邮箱加密字段
     */
    fun putSensorsDataEmailEventId(emailEventID: String) {
        StorageUtil.logoutClearStorage
            .encode(StorageConstants.SENSORS_DATA_EMAIL_EVENT_ID, emailEventID)
    }

    fun getSensorsDataEmailEventId(default: String = ""): String {
        return StorageUtil.logoutClearStorage.decodeString(StorageConstants.SENSORS_DATA_EMAIL_EVENT_ID, default)
    }

    /**
     * 登录返回的crmUserId
     */
    fun putCrmUserId(crmUserId: String) {   // TODO felix 和用户相关的为什么没放到用户存储
        StorageUtil.logoutClearStorage.encode(StorageConstants.CRM_USER_ID, crmUserId)
    }

    @JvmStatic
    fun getCrmUserId(default: String = ""): String {
        return StorageUtil.logoutClearStorage.decodeString(StorageConstants.CRM_USER_ID, default)
    }

    /**
     * 策略草稿列表本地存储的key
     */
    fun putStrategyListDraft(list: MutableList<StrategyBean>) {
        StorageUtil.logoutClearStorage.encode(
            StorageConstants.STRATEGY_LIST_DRAFT,
            GsonUtil.buildGson().toJson(list)
        )
    }

    fun getStrategyListDraft(): MutableList<StrategyBean>? {
        val jsonStr = StorageUtil.logoutClearStorage.decodeString(StorageConstants.STRATEGY_LIST_DRAFT)
        val token = object : TypeToken<MutableList<StrategyBean>>() {}
        return runCatching { GsonUtil.buildGson().fromJson(jsonStr, token.type) as? MutableList<StrategyBean> }.getOrNull()
    }

    fun clearStrategyListDraftKey() {
        StorageUtil.logoutClearStorage.removeKey(StorageConstants.STRATEGY_LIST_DRAFT)
    }

    /**
     * 创建策略 审核模式 是否提示 可以选择自动拒绝
     */
    fun putStrategyAutoReject(isAgree: Boolean) {
        StorageUtil.logoutClearStorage.encode(StorageConstants.STRATEGY_AUTO_REJECT, isAgree)
    }

    fun getStrategyAutoReject(default: Boolean = false): Boolean {
        return StorageUtil.logoutClearStorage
            .decodeBoolean(StorageConstants.STRATEGY_AUTO_REJECT, default)
    }

    /**
     * 手势密码
     */
    fun putPatternUnlock(password: String) {
        StorageUtil.logoutClearStorage.encode(StorageConstants.PATTERN_UNLOCK, password)
    }

    fun getPatternUnlock(default: String = ""): String {
        return StorageUtil.logoutClearStorage.decodeString(StorageConstants.PATTERN_UNLOCK, default)
    }

    /**
     * 解锁时长
     */
    fun putUnlockTime(time: Int) {
        StorageUtil.logoutClearStorage.encode(StorageConstants.UNLOCK_TIME, time)
    }

    fun getUnlockTime(default: Int = 0): Int {
        return StorageUtil.logoutClearStorage.decodeInt(StorageConstants.UNLOCK_TIME, default)
    }

    /**
     * 是否已解锁
     */
    fun putAppLockOrder(lock: Boolean) {
        StorageUtil.logoutClearStorage.encode(StorageConstants.APP_LOCK_ORDER, lock)
    }

    fun getAppLockOrder(default: Boolean = false): Boolean {
        return StorageUtil.logoutClearStorage.decodeBoolean(StorageConstants.APP_LOCK_ORDER, default)
    }

    /**
     *  0未设置 1手势 2指纹
     */
    fun putSecurityOpenSetState(state: Int) {
        StorageUtil.logoutClearStorage.encode(StorageConstants.SECURITY_OPEN_SET_STATE, state)
    }

    fun getSecurityOpenSetState(default: Int = 0): Int {
        return StorageUtil.logoutClearStorage
            .decodeInt(StorageConstants.SECURITY_OPEN_SET_STATE, default)
    }

    /**
     * 解锁设置 0无解锁 1启动  2交易，订单，我的
     */
    fun putSecuritySetState(state: Int) {
        StorageUtil.logoutClearStorage.encode(StorageConstants.SECURITY_SET_STATE, state)
    }

    fun getSecuritySetState(default: Int = 0): Int {
        return StorageUtil.logoutClearStorage.decodeInt(StorageConstants.SECURITY_SET_STATE, default)
    }

    /**
     * sumsub  是否已经同意人脸识别相关功能
     */
    fun putSumsubAgree(agree: Boolean) {
        StorageUtil.logoutClearStorage.encode(StorageConstants.SUMSUB_AGREE, agree)
    }

    fun getSumsubAgree(default: Boolean = false): Boolean {
        return StorageUtil.logoutClearStorage.decodeBoolean(StorageConstants.SUMSUB_AGREE, default)
    }

    /**
     * 价格提醒创建页面 提醒弹窗是否展示
     */
    fun putPriceAlertNotificationDialogShow(isShow: Boolean) {
        StorageUtil.logoutClearStorage
            .encode(StorageConstants.PRICE_ALERT_NOTIFICATION_ENABLE_TIPS, isShow)
    }

    fun getPriceAlertNotificationDialogShow(default: Boolean = false): Boolean {
        return StorageUtil.logoutClearStorage
            .decodeBoolean(StorageConstants.PRICE_ALERT_NOTIFICATION_ENABLE_TIPS, default)
    }

    /**
     * ib账户分享页面，保存上次选中的账户号
     */
    fun putInvitationLastSelectAccount(account: String) {
        StorageUtil.logoutClearStorage
            .encode(StorageConstants.INVITATION_LAST_SELECT_ACCOUNT, account)
    }

    fun getInvitationLastSelectAccount(default: String = ""): String {
        return StorageUtil.logoutClearStorage
            .decodeString(StorageConstants.INVITATION_LAST_SELECT_ACCOUNT, default)
    }

    /**
     * 是否展示跟单相关页面
     */
    fun putShowStEntrance(isShowSt: Boolean) {
        StorageUtil.logoutClearStorage.encode(StorageConstants.SHOW_ST_ENTRANCE, isShowSt)
    }

    fun getShowStEntrance(default: Boolean = false): Boolean {
        return StorageUtil.logoutClearStorage.decodeBoolean(StorageConstants.SHOW_ST_ENTRANCE, default)
    }

    /**
     * * 保存监管
     * * 三个品牌所有监管值说明
     * * 1：ASIC，2：CIMA，8：VFSC，11：FSA，12：SVG，13：FCA，14：VFSC2
     * * 其中AU用户监管有：ASIC，VFSC，FCA，VFSC2
     * * PU用户监管有：SVG，FSA
     * * VT用户监管有：CIMA
     * * VJP用户监管有：SVG
     */
    fun putSuperviseNum(superviseNum: String) {
        StorageUtil.logoutClearStorage.encode(StorageConstants.SUPERVISE_NUM, superviseNum)
    }

    fun getSuperviseNum(default: String = ""): String {
        return StorageUtil.logoutClearStorage.decodeString(StorageConstants.SUPERVISE_NUM, default)
    }

    /**
     * 是否是VFSC/VFSC2监管
     */
    fun isV1V2(): Boolean {
        return getSuperviseNum() == "8" || getSuperviseNum() == "14"
    }

    /**
     * 保存交易权限
     */
    fun putTradePermission(obj: KycFunctionPermissionObj) {
        StorageUtil.logoutClearStorage.encode(StorageConstants.TRADE_PERMISSION, GsonUtil.toJson(obj))
    }

    /**
     * 获取交易权限
     */
    fun getTradePermission(): KycFunctionPermissionObj? {
        val jsonStr = StorageUtil.logoutClearStorage.decodeString(StorageConstants.TRADE_PERMISSION, "")
        if (jsonStr.isNotEmpty()) {
            return GsonUtil.fromJson(jsonStr, KycFunctionPermissionObj::class.java)
        }
        return null
    }

    /**
     * 交易杠杆
     */
    fun putLeverageTrade(leverageTrade: String) {
        StorageUtil.logoutClearStorage.encode(StorageConstants.LEVERAGE_TRADE, leverageTrade)
    }

    /**
     * 交易杠杆
     */
    fun getLeverageTrade(default: String = ""): String {
        return StorageUtil.logoutClearStorage.decodeString(StorageConstants.LEVERAGE_TRADE, default)
    }

    /**
     * 接口返回的开户状态
     */
    fun putSkipTypeOpenAccount(skipType: Int) {
        StorageUtil.logoutClearStorage.encode(StorageConstants.SKIP_TYPE_OPEN_ACCOUNT, skipType)
    }

    fun getSkipTypeOpenAccount(default: Int = 0): Int {
        return StorageUtil.logoutClearStorage.decodeInt(StorageConstants.SKIP_TYPE_OPEN_ACCOUNT, default)
    }

    /**
     * telegram登录h5授权完成回调数据
     */
    fun putTelegramH5Data(json: String) {
        StorageUtil.logoutClearStorage.encode(StorageConstants.TELEGRAM_AUTHORIZE_DATA, json)
    }

    fun getTelegramH5Data(): TelegramH5ResBean? {
        val jsonStr = StorageUtil.logoutClearStorage.decodeString(StorageConstants.TELEGRAM_AUTHORIZE_DATA, "")
        if (jsonStr.isNotEmpty()) {
            return GsonUtil.fromJson(jsonStr, TelegramH5ResBean::class.java)
        }
        return null
    }

    /**
     * 切换行情显示模式     0:Classic    1:Buy/Sell
     */
    fun putTradeSwitchMode(mode: Int) {
        StorageUtil.logoutClearStorage.encode(StorageConstants.TRADE_SWITCH_MODE, mode)
    }

    fun getTradeSwitchMode(default: Int = 0): Int {
        return StorageUtil.logoutClearStorage.decodeInt(StorageConstants.TRADE_SWITCH_MODE, default)
    }

    /**
     * 按行情涨跌幅排序     0:不排序    1:箭头向上(按涨幅由低到高)    2:箭头向下(按涨幅由高到低)
     */
    fun putTradeSortRose(rose: Int) {
        StorageUtil.logoutClearStorage.encode(StorageConstants.TRADE_SORT_ROSE, rose)
    }

    fun getTradeSortRose(default: Int = 0): Int {
        return StorageUtil.logoutClearStorage.decodeInt(StorageConstants.TRADE_SORT_ROSE, default)
    }

    fun getShareSetting(prefix: String, default: String = ""): ShareSettingData? {
        val json = StorageUtil.logoutClearStorage.decodeString("${prefix}${StorageConstants.SHARE_SETTING}", default)
        return runCatching { GsonUtil.buildGson().fromJson(json, ShareSettingData::class.java) }.getOrNull()
    }

    fun putShareSetting(prefix: String, data: ShareSettingData) {
        StorageUtil.logoutClearStorage.encode("${prefix}${StorageConstants.SHARE_SETTING}", data.json)
    }

    /**
     * 退出登录时调用清理数据
     */
    fun logoutClear() {
        StorageUtil.logoutClear()
    }

    /**------ endregion 退出登录清理结束 ------- */

    /**------ region 切换账户清理开始 ------- */

    /**
     * in app 的消息是否已经展示过
     */
    fun putMessageInAppIsShow(canShow: Boolean) {
        StorageUtil.switchAccountClearStorage
            .encode(StorageConstants.MESSAGE_IN_APP_IS_SHOW, canShow)
    }

    fun getMessageInAppIsShow(default: Boolean = false): Boolean {
        return StorageUtil.switchAccountClearStorage
            .decodeBoolean(StorageConstants.MESSAGE_IN_APP_IS_SHOW, default)
    }

    /**
     * 是否已经切换语言
     */
    fun putInternationalizationSwitch(switch: Boolean) {
        StorageUtil.switchAccountClearStorage.encode(StorageConstants.INTERNATIONALIZATION_SWITCH, switch)
    }

    /**
     * 是否已经切换语言
     */
    fun getInternationalizationSwitch(default: Boolean = false): Boolean {
        return StorageUtil.switchAccountClearStorage.decodeBoolean(StorageConstants.INTERNATIONALIZATION_SWITCH, default)
    }

    /**
     * 用户手机号登录的 手机号 或者 Facebook 三方登录获取到的手机号， 目前暂时没有Facebook登录的流程
     */
    fun putUserTel(userTel: String) {
        StorageUtil.switchAccountClearStorage.encode(StorageConstants.USER_TEL, userTel)
    }

    fun getUserTel(default: String = ""): String {
        return StorageUtil.switchAccountClearStorage.decodeString(StorageConstants.USER_TEL, default)
    }

    fun putRefereeInfo(json: String) {
        StorageUtil.switchAccountClearStorage.encode(StorageConstants.REFEREES_INFO, json)
    }

    suspend fun getRefereeInfo(): InvitationsObj? =
        withContext(Dispatchers.Default) {
            val jsonStr = StorageUtil.switchAccountClearStorage.decodeString(StorageConstants.REFEREES_INFO, "")
            if (jsonStr.isNotEmpty()) {
                return@withContext GsonUtil.fromJson(jsonStr, InvitationsObj::class.java)
            }
            return@withContext null
        }

    /**
     * ib账号 分享请求的默认数据
     */
    fun putRefereeInfoIB(json: String) {
        StorageUtil.switchAccountClearStorage.encode(StorageConstants.REFEREES_INFO_IB, json)
    }

    suspend fun getRefereeInfoIB(): InvitationsObj? =
        withContext(Dispatchers.Default) {
            val jsonStr = StorageUtil.switchAccountClearStorage.decodeString(StorageConstants.REFEREES_INFO_IB, "")
            if (jsonStr.isNotEmpty()) {
                return@withContext GsonUtil.fromJson(jsonStr, InvitationsObj::class.java)
            }
            return@withContext null
        }

    /**
     * 切换账户时调用清理数据
     */
    fun switchAccountClear() {
        StorageUtil.switchAccountClear()
    }
    /** endregion------切换账户清理结束------- */

    /** region ------不需要清理开始------- */

    /**
     * WebSocket推送过来的未读状态
     */
    fun putRedPointState(hasNew: Boolean) {
        StorageUtil.getNeverClear().saveData(StorageConstants.RED_POINT_STATE, hasNew)
    }

    fun getRedPointState(default: Boolean = false): Boolean {
        return StorageUtil.getNeverClear().getData(StorageConstants.RED_POINT_STATE, default)
    }

    /**
     * StWebSocket推送过来的未读状态
     */
    fun putPointRemindPromoShow(isShow: Boolean) {
        StorageUtil.getNeverClear().saveData(StorageConstants.POINT_REMIND_PROMO_SHOW, isShow)
    }

    fun getPointRemindPromoShow(default: Boolean = false): Boolean {
        return StorageUtil.getNeverClear().getData(StorageConstants.POINT_REMIND_PROMO_SHOW, default)
    }

    /**
     * 国家代码
     */
    fun putCountryCode(countryCode: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.COUNTRY_CODE, countryCode)
    }

    fun getCountryCode(default: String = ""): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.COUNTRY_CODE, default)
    }

    /**
     * 国家区号
     */
    fun putCountryNum(countryNum: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.COUNTRY_NUM, countryNum)
    }

    fun getCountryNum(default: String = ""): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.COUNTRY_NUM, default)
    }

    /**
     * 国家名字
     */
    fun putCountryName(countryName: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.COUNTRY_NAME, countryName)
    }

    fun getCountryName(default: String = ""): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.COUNTRY_NAME, default)
    }

    /**
     * 注册邀请码
     */
    fun putInviteCodeRegister(inviteCode: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.INVITE_CODE_REGISTER, inviteCode)
    }

    fun getInviteCodeRegister(default: String = ""): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.INVITE_CODE_REGISTER, default)
    }

    /**
     * 信号源 id （ 分享链接带注册邀请码，如未登录会使用注册邀请码 ）
     */
    fun putIdSignalSource(idSignalSource: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.ID_SIGNAL_SOURCE, idSignalSource)
    }

    fun getIdSignalSource(default: String = ""): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.ID_SIGNAL_SOURCE, default)
    }

    /**
     * appsFlyer来的链接
     */
    fun putUrlH5(urlH5: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.URL_H5, urlH5)
    }

    fun getUrlH5(default: String = ""): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.URL_H5, default)
    }

    /**
     * 风控sessionID，每次启动app获取保存到缓存，请求接口时带到header里
     */
    fun putTmxSessionId(tmxSessionId: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.TMX_SESSION_ID, tmxSessionId)
    }

    fun getTmxSessionId(default: String = ""): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.TMX_SESSION_ID, default)
    }

    /**
     * 启动图片
     */
    fun putAppStartUpImageUrl(appStartUpImageUrl: String) {
        StorageUtil.getNeverClear()
            .saveData(StorageConstants.APP_START_UP_IMAGE_URL, appStartUpImageUrl)
    }

    fun getAppStartUpImageUrl(default: String = ""): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.APP_START_UP_IMAGE_URL, default)
    }

    /**
     * 是否跟随系统主题
     */
    fun putStyleFollowSystem(isFollow: Boolean) {
        StorageUtil.getNeverClear().saveData(StorageConstants.STYLE_FOLLOW_SYSTEM, isFollow)
    }

    fun getStyleFollowSystem(default: Boolean = false): Boolean {
        return StorageUtil.getNeverClear().getData(StorageConstants.STYLE_FOLLOW_SYSTEM, default)
    }

    /**
     * app主题
     */
    fun putStyleState(styleState: Int) {
        StorageUtil.getNeverClear().saveData(StorageConstants.STYLE_STATE, styleState)
    }

    @JvmStatic
    fun getStyleState(default: Int = 0): Int {
        return StorageUtil.getNeverClear().getData(StorageConstants.STYLE_STATE, default)
    }

    /**
     * 资产卡片默认展开（Demo、Live、跟单账户Manual-Trading的订单页）
     */
    fun putOrderAssetCardExpandsByDefault(orderAssetCardExpandsByDefault: Boolean) {
        StorageUtil.getNeverClear().saveData(
            StorageConstants.ASSET_CARD_EXPANDS_BY_DEFAULT,
            orderAssetCardExpandsByDefault
        )
    }

    fun getOrderAssetCardExpandsByDefault(default: Boolean = true): Boolean {
        return StorageUtil.getNeverClear()
            .getData(StorageConstants.ASSET_CARD_EXPANDS_BY_DEFAULT, default)
    }

    /**
     * app是否首次启动
     */
    fun putAppStartFirst(appStartFirst: Boolean) {
        StorageUtil.getNeverClear().saveData(StorageConstants.APP_START_FIRST, appStartFirst)
    }

    fun getAppStartFirst(default: Boolean = true): Boolean {
        return StorageUtil.getNeverClear().getData(StorageConstants.APP_START_FIRST, default)
    }

    /**
     * apk是否首次安装
     */
    fun putInstallApkFirst(installApkFirst: Boolean) {
        StorageUtil.getNeverClear().saveData(StorageConstants.INSTALL_APK_FIRST, installApkFirst)
    }

    fun getInstallApkFirst(default: Boolean = true): Boolean {
        return StorageUtil.getNeverClear().getData(StorageConstants.INSTALL_APK_FIRST, default)
    }

    /**
     * app flyer 中获取的 渠道 信息 以及推广过来的数据
     */
    fun putCxd(cxd: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.CXD, cxd)
    }

    fun getCxd(default: String = ""): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.CXD, default)
    }

    fun putCid(cid: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.CID, cid)
    }

    fun getCid(default: String = ""): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.CID, default)
    }

    fun putRaf(raf: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.RAF, raf)
    }

    fun getRaf(default: String = ""): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.RAF, default)
    }

    fun putLs(ls: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.LS, ls)
    }

    fun getLs(default: String = ""): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.LS, default)
    }

    fun putCp(cp: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.CP, cp)
    }

    fun getCp(default: String): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.CP, default)
    }

    fun putAgentAccountNum(agentAccountNum: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.AGENTACCOUNTNUM, agentAccountNum)
    }

    fun getAgentAccountNum(default: String = ""): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.AGENTACCOUNTNUM, default)
    }

    fun putLivestream(livestream: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.LIVESTREAM, livestream)
    }

    fun getLivestream(default: String = ""): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.LIVESTREAM, default)
    }

    /**
     * 设备唯一标识
     */
    fun putUuid(uuid: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.UUID, uuid)
    }

    fun getUuid(default: String = ""): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.UUID, default)
    }

    fun putGoogleAdvertisingId(googleAdvertisingId: String?) {
        StorageUtil.getNeverClear()
            .saveData(StorageConstants.GOOGLE_ADVERTISING_ID, googleAdvertisingId)
    }

    @JvmStatic
    @JvmOverloads
    fun getGoogleAdvertisingId(default: String = ""): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.GOOGLE_ADVERTISING_ID, default)
    }

    fun putAppsFlyerId(appsFlyerId: String?) {
        StorageUtil.getNeverClear().saveData(StorageConstants.APPSFLYER_ID, appsFlyerId)
    }

    @JvmStatic
    @JvmOverloads
    fun getAppsFlyerId(default: String = ""): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.APPSFLYER_ID, default)
    }

    fun putTokenFcm(tokenFcm: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.TOKEN_FCM, tokenFcm)
    }

    fun getTokenFcm(default: String = ""): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.TOKEN_FCM, default)
    }

    fun putChannelType(channelType: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.CHANNEL_TYPE, channelType)
    }

    @JvmStatic
    fun getChannelType(default: String = ""): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.CHANNEL_TYPE, default)
    }

    fun putFirebaseAppInstanceIdCache(firebaseAppInstanceIdCache: String) {
        StorageUtil.getNeverClear()
            .saveData(StorageConstants.FIREBASE_APP_INSTANCE_ID_CACHE, firebaseAppInstanceIdCache)
    }

    @JvmStatic
    fun getFirebaseAppInstanceIdCache(default: String = ""): String {
        return StorageUtil.getNeverClear()
            .getData(StorageConstants.FIREBASE_APP_INSTANCE_ID_CACHE, default)
    }

    /**
     * 拼接key,需传前缀
     */
    fun putTradingViewSettingData(prefix: String, tradingViewSettingData: String) {
        StorageUtil.getNeverClear().saveData(
            "${prefix}${StorageConstants.TRADING_VIEW_SETTING_DATA}",
            tradingViewSettingData
        )
    }

    fun getTradingViewSettingData(prefix: String, default: String = ""): String {
        return StorageUtil.getNeverClear()
            .getData("${prefix}${StorageConstants.TRADING_VIEW_SETTING_DATA}", default)
    }

    fun putKlineViewSettingData(prefix: String, klineViewSettingData: String) {
        StorageUtil.getNeverClear().saveData(
            "${prefix}${StorageConstants.KLINE_VIEW_SETTING_DATA}",
            klineViewSettingData
        )
    }

    fun getKlineViewSettingData(prefix: String, default: String = ""): String {
        return StorageUtil.getNeverClear()
            .getData("${prefix}${StorageConstants.KLINE_VIEW_SETTING_DATA}", default)
    }

    fun putKlineSettingData(prefix: String, klineSettingData: String) {
        StorageUtil.getNeverClear().saveData(
            "${prefix}${StorageConstants.KLINE_SETTING_DATA}",
            klineSettingData
        )
    }

    fun getKlineSettingData(prefix: String, default: String = ""): String {
        return StorageUtil.getNeverClear()
            .getData("${prefix}${StorageConstants.KLINE_SETTING_DATA}", default)
    }

    fun putTradingViewDrawingData(prefix: String, tradingViewDrawingData: String) {
        StorageUtil.getNeverClear().saveData(
            "${prefix}${StorageConstants.TRADING_VIEW_DRAWING_DATA}",
            tradingViewDrawingData
        )
    }

    fun getTradingViewDrawingData(prefix: String, default: String = ""): String {
        return StorageUtil.getNeverClear()
            .getData("${prefix}${StorageConstants.TRADING_VIEW_DRAWING_DATA}", default)
    }

    fun putUser2faBindEd(prefix: String, user2faBindEd: Boolean) {
        StorageUtil.getNeverClear().saveData(
            "${prefix}${StorageConstants.USER_TFA_BINDED}",
            user2faBindEd
        )
    }

    fun getUser2faBindEd(prefix: String, default: Boolean): Boolean {
        return StorageUtil.getNeverClear()
            .getData("${prefix}${StorageConstants.USER_TFA_BINDED}", default)
    }

    @JvmStatic
    fun putSelectTradingViewMode(selectTradingViewMode: Boolean) {
        StorageUtil.getNeverClear().saveData(StorageConstants.SELECT_TRADING_VIEW_MODE, selectTradingViewMode)
    }

    @JvmStatic
    fun getSelectTradingViewMode(default: Boolean): Boolean {
        return StorageUtil.getNeverClear().getData(StorageConstants.SELECT_TRADING_VIEW_MODE, default)
    }

    fun putAccountManageCopyTradingUrl(accountManageCopyTradingUrl: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.ACCOUNT_MANAGE_COPY_TRADING_URL, accountManageCopyTradingUrl)
    }

    fun getAccountManageCopyTradingUrl(default: String): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.ACCOUNT_MANAGE_COPY_TRADING_URL, default)
    }

    /**
     * 新用户进入时显示的弹窗，仅显示一次，本地保存，
     */
    fun putNewUserDialog(newUserDialog: Boolean) {
        StorageUtil.getNeverClear().saveData(StorageConstants.NEW_USER_DIALOG, newUserDialog)
    }

    fun getNewUserDialog(default: Boolean): Boolean {
        return StorageUtil.getNeverClear().getData(StorageConstants.NEW_USER_DIALOG, default)
    }

    /**
     * 服务端双域名配置
     */
    fun putServerBaseUrlTest(serverBaseUrlTest: ServerBaseUrlBean) {
        StorageUtil.getNeverClear().saveData(StorageConstants.SERVER_BASE_URL_TEST, GsonUtil.buildGson().toJson(serverBaseUrlTest))
    }

    fun getServerBaseUrlTest(): ServerBaseUrlBean? {
        val dataStr = StorageUtil.getNeverClear().getData(StorageConstants.SERVER_BASE_URL_TEST, "")
        return runCatching { GsonUtil.buildGson().fromJson(dataStr, ServerBaseUrlBean::class.java) }.getOrNull()
    }

    fun putServerBaseUrlProd(serverBaseUrl: ServerBaseUrlBean) {
        StorageUtil.getNeverClear().saveData(StorageConstants.SERVER_BASE_URL_PROD, GsonUtil.buildGson().toJson(serverBaseUrl))
    }

    fun getServerBaseUrlProd(): ServerBaseUrlBean? {
        val dataStr = StorageUtil.getNeverClear().getData(StorageConstants.SERVER_BASE_URL_PROD, "")
        return runCatching { GsonUtil.buildGson().fromJson(dataStr, ServerBaseUrlBean::class.java) }.getOrNull()
    }

    /**
     * app的当前语言的position
     */
    @JvmStatic
    fun putLanguageSelect(select: Int) {
//        StorageUtil.getNeverClear().saveData(StorageConstants.LANGUAGE_SELECT, select)
        StorageUtil.getLanguageSP().saveLanguage(StorageConstants.LANGUAGE_SELECT, select)
    }

    @JvmStatic
    fun getLanguageSelect(): Int {
//        return StorageUtil.getNeverClear().getData(StorageConstants.LANGUAGE_SELECT, default)
        return StorageUtil.getLanguageSP().getSelectLanguage(StorageConstants.LANGUAGE_SELECT)
    }

    /**
     * 切换测试环境线路Index
     */
    fun putSwitchHttpUrlIndex(prefix: String, index: Int) {
        StorageUtil.getNeverClear().saveData(prefix + StorageConstants.SWITCH_TEST_HTTP_URL_INDEX, index)
    }

    fun getSwitchHttpUrlIndex(prefix: String, default: Int): Int {
        return StorageUtil.getNeverClear().getData(prefix + StorageConstants.SWITCH_TEST_HTTP_URL_INDEX, default)
    }

    /**
     * 切换测试环境线路Index
     */
    fun putCustomH5Url(url: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.CUSTOM_H5_URL, url)
    }

    fun getCustomH5Url(default: String = ""): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.CUSTOM_H5_URL, default)
    }

    /**
     * 策略成功下单时保存其模式 下次下单时优先选择
     */
    fun putStrategyOrderCopyMode(strategyOrderCopyMode: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.STRATEGY_ORDER_COPY_MODE, strategyOrderCopyMode)
    }

    fun getStrategyOrderCopyMode(default: String): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.STRATEGY_ORDER_COPY_MODE, default)
    }

    /**
     * 策略成功下单时保存其0滑点开关状态 下次下单时优先选择 如未存储 默认打开状态
     */
    fun putStrategyOrderSlippageProtectionStatus(status: Boolean) {
        StorageUtil.getNeverClear().saveData(StorageConstants.STRATEGY_ORDER_SLIPPAGE_PROTECTION_STATUS, status)
    }

    fun getStrategyOrderSlippageProtectionStatus(): Boolean {
        return StorageUtil.getNeverClear().getData(StorageConstants.STRATEGY_ORDER_SLIPPAGE_PROTECTION_STATUS, true)
    }

    /**
     * 未登录状态
     */
    fun putExitStatus(exitStatus: Boolean) {
        StorageUtil.getNeverClear().saveData(StorageConstants.EXIT_STATUS, exitStatus)
    }

    fun getExitStatus(default: Boolean): Boolean {
        return StorageUtil.getNeverClear().getData(StorageConstants.EXIT_STATUS, default)
    }

    /**
     * 未登录导航
     */
    fun putLogoutGuideNum(logoutGuideNum: Int) {
        StorageUtil.getNeverClear().saveData(StorageConstants.LOGOUT_GUIDE_NUM, logoutGuideNum)
    }

    fun getLogoutGuideNum(default: Int): Int {
        return StorageUtil.getNeverClear().getData(StorageConstants.LOGOUT_GUIDE_NUM, default)
    }

    fun putLogoutGuideDay(logoutGuideDay: Int) {
        StorageUtil.getNeverClear().saveData(StorageConstants.LOGOUT_GUIDE_DAY, logoutGuideDay)
    }

    fun getLogoutGuideDay(default: Int): Int {
        return StorageUtil.getNeverClear().getData(StorageConstants.LOGOUT_GUIDE_DAY, default)
    }

    /**
     * K线图类型
     */
    @JvmStatic
    fun putChartTypePosition(chartTypePosition: Int) {
        StorageUtil.getNeverClear().saveData(StorageConstants.CHART_TYPE_POSITION, chartTypePosition)
    }

    @JvmStatic
    fun getChartTypePosition(default: Int): Int {
        return StorageUtil.getNeverClear().getData(StorageConstants.CHART_TYPE_POSITION, default)
    }

    @JvmStatic
    fun putChartTypeText(chartTypeText: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.CHART_TYPE_TEXT, chartTypeText)
    }

    @JvmStatic
    fun getChartTypeText(default: String): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.CHART_TYPE_TEXT, default)
    }

    /**
     * 语言语种
     */
    fun putLanguageLang(langLanguage: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.LANGUAGE_LANG, langLanguage)
    }

    fun getLanguageLang(default: String = ""): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.LANGUAGE_LANG, default)
    }

    /**
     * 语言地区
     */
    fun putLanguageRegion(langCountry: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.LANGUAGE_REGION, langCountry)
    }

    fun getLanguageRegion(default: String = ""): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.LANGUAGE_REGION, default)
    }

    /**
     * 显示在app端的语言名称
     */
    fun putLanguageShowName(showName: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.LANGUAGE_SHOW_NAME, showName)
    }

    fun getLanguageShowName(default: String = ""): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.LANGUAGE_SHOW_NAME, default)
    }

    /**
     * 是否开启通知
     */
    fun putNoticeNotificationDialogShow(isShow: Boolean) {
        StorageUtil.getNeverClear().saveData(StorageConstants.NOTICE_NOTIFICATION_DIALOG_SHOW, isShow)
    }

    fun getNoticeNotificationDialogShow(default: Boolean): Boolean {
        return StorageUtil.getNeverClear().getData(StorageConstants.NOTICE_NOTIFICATION_DIALOG_SHOW, default)
    }

    /**
     * 存储Zendesk初始化已使用的ChannelKey
     */
    fun putZendeskChannelKey(channelKey: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.ZENDESK_INITIALIZED_CHANNELKEY, channelKey)
    }

    fun getZendeskChannelKey(default: String = ""): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.ZENDESK_INITIALIZED_CHANNELKEY, default)
    }

    /**
     * 设备硬件标识
     */
    fun putDeviceId(deviceId: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.DEVICE_ID, deviceId)
    }

    fun getDeviceId(default: String): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.DEVICE_ID, default)
    }

    /**
     * 交易行情引导图
     */
    fun putTradeQuotesGuide() {
        StorageUtil.getNeverClear().saveData(StorageConstants.TRADE_QUOTES_GUIDE, true)
    }

    fun getTradeQuotesGuide(): Boolean {
        return StorageUtil.getNeverClear().getData(StorageConstants.TRADE_QUOTES_GUIDE, false)
    }

    /**
     * 下单单位存储
     * 交易单位，1 手数，2 金额
     */
    fun putOpenPositionUnit(unit: String) {
        StorageUtil.getNeverClear().saveData(StorageConstants.OPEN_POSITION_UNIT, unit)
    }

    fun getOpenPositionUnit(default: String = "1"): String {
        return StorageUtil.getNeverClear().getData(StorageConstants.OPEN_POSITION_UNIT, default)
    }

    /**
     * 存储K线选择产品引导图状态（仅限3.60.0版本）
     */
    fun putKlineSymbolGuide() {
        StorageUtil.getNeverClear().saveData(StorageConstants.KLINE_GUIDE_SYMBOL, true)
    }

    fun getKlineSymbolGuide(): Boolean {
        return StorageUtil.getNeverClear().getData(StorageConstants.KLINE_GUIDE_SYMBOL, false)
    }

    fun putDrawToolShape(bean: DrawToolShape) {
        val json = GsonUtil.toJson(bean)
        StorageUtil.getNeverClear().saveData(StorageConstants.KLINE_DRAW_TOOLS_CONFIG, json)
    }

    fun getDrawToolShape(): DrawToolShape? {
        val jsonStr = StorageUtil.getNeverClear().getData(StorageConstants.KLINE_DRAW_TOOLS_CONFIG, "")
        if (jsonStr.isNotEmpty()) {
            return GsonUtil.fromJson(jsonStr, DrawToolShape::class.java)
        }
        return null
    }

    fun putViewPosition(x: Int, y: Int) {
        StorageUtil.getNeverClear().saveData(StorageConstants.KLINE_DRAW_TOOLS_POSITION, "$x,$y")
    }

    fun getViewPosition(): Pair<Int, Int>? {
        val positionStr = StorageUtil.getNeverClear().getData(StorageConstants.KLINE_DRAW_TOOLS_POSITION, "")
        return if (positionStr.isNotEmpty() && positionStr.contains(",")) {
            try {
                val array = positionStr.split(",")
                Pair(array[0].toInt(), array[1].toInt())
            } catch (e: Exception) {
                null
            }
        } else {
            null
        }
    }

    fun putKlineScale(scale: Float) {
        StorageUtil.getNeverClear().saveData(StorageConstants.KLINE_SCALE, scale)
    }

    fun getKlineScale(): Float {
        return StorageUtil.getNeverClear().getData(StorageConstants.KLINE_SCALE, 1f)
    }

    fun putKlineShowDraw(isShowDraw: Boolean) {
        StorageUtil.getNeverClear().saveData(StorageConstants.KLINE_SHOW_DRAW, isShowDraw)
    }

    fun getKlineShowDraw(): Boolean {
        return StorageUtil.getNeverClear().getData(StorageConstants.KLINE_SHOW_DRAW, true)
    }

    /**
     * 策略详情页回报率选择的周期（DAY ｜ WEEK ｜ MONTH）
     */
    fun putReturnRateCycle(index: Int) {
        StorageUtil.getNeverClear().saveData(StorageConstants.RETURN_RATE_CYCLE, index)
    }

    fun getReturnRateCycle(): Int {
        return StorageUtil.getNeverClear().getData(StorageConstants.RETURN_RATE_CYCLE, 0)
    }

    /**
     * 在同一账号本地记录下单或更新时用户设置的"等比例占用保证金"模式的倍数值
     */
    fun putFormulaModeValueByUser(value: String) {
        val userId = UserDataUtil.userId()
        StorageUtil.getNeverClear().saveData("${userId}_${StorageConstants.FORMULA_COPY_MODE_VALUE}", value)
    }

    fun getFormulaModeValueByUser(): String {
        val userId = UserDataUtil.userId()
        return StorageUtil.getNeverClear().getData("${userId}_${StorageConstants.FORMULA_COPY_MODE_VALUE}", "")
    }

    /**
     * 保存最近一次切换的产品
     */
    fun putTradeProductSymbol(symbol: String) {
        val map = loadTradeProductSymbolMap()
        map[UserDataUtil.accountCd()] = symbol
        StorageUtil.getNeverClear().saveData(StorageConstants.TRADE_PRODUCT_SYMBOL, GsonUtil.toJson(map))
    }

    /**
     * 获取最近一次切换的产品
     */
    fun getTradeProductSymbol(): String {
        val map = loadTradeProductSymbolMap()
        return map[UserDataUtil.accountCd()] ?: ""
    }

    private fun loadTradeProductSymbolMap(): MutableMap<String, String> {
        return runCatching {
            val jsonString = StorageUtil.getNeverClear().getData(StorageConstants.TRADE_PRODUCT_SYMBOL, "")
            if (jsonString.isEmpty()) {
                return@runCatching mutableMapOf()
            }
            val typeToken = object : TypeToken<MutableMap<String, String>>() {}.type
            GsonUtil.fromJson<MutableMap<String, String>>(jsonString, typeToken)
        }.getOrElse {
            mutableMapOf()
        }
    }

    /**
     * 是否启用 系统截屏分享弹窗功能
     */
    fun putScreenshotShareEnable(enable: Boolean) {
        StorageUtil.getNeverClear().saveData(StorageConstants.SCREENSHOT_SHARE_ENABLE, enable)
    }

    fun getScreenshotShareEnable(): Boolean {
        return StorageUtil.getNeverClear().getData(StorageConstants.SCREENSHOT_SHARE_ENABLE, true)
    }

    /** endregion------不需要清理结束------- */

    /** region ------业务临时存储的数据开始------- */

    /**
     * 入金选择渠道时临时存储的入金金额
     */
    fun putDepositResetPayMethod(inputStr: String) {
        StorageUtil.localStorage.encode(StorageConstants.DEPOSIT_RESET_PAY_METHOD, inputStr)
    }

    fun getDepositResetPayMethod(default: String = ""): String {
        return StorageUtil.localStorage.decodeString(StorageConstants.DEPOSIT_RESET_PAY_METHOD, default)
    }

    /**
     * 搜索过的历史产品名
     */
    fun putSearchHistoryKey(searchHistoryKey: String) {
        StorageUtil.localStorage
            .encode(StorageConstants.SP_KEY_SEARCH_HISTORY, searchHistoryKey)
    }

    fun getSearchHistoryKey(default: String = ""): String {
        return StorageUtil.localStorage.decodeString(StorageConstants.SP_KEY_SEARCH_HISTORY, default)
    }

    /**
     * 短信验证码id
     */
    fun putSmsCodeId(smsCodeId: String?) {
        StorageUtil.localStorage.encode(StorageConstants.SMS_CODE_ID, smsCodeId)
    }

    fun getSmsCodeId(default: String = ""): String {
        return StorageUtil.localStorage.decodeString(StorageConstants.SMS_CODE_ID, default)
    }

    /**
     * 分享
     */
    fun putShareTitle(shareTitle: String) {
        StorageUtil.localStorage.encode(StorageConstants.SHARE_TITLE, shareTitle)
    }

    fun getShareTitle(default: String = ""): String {
        return StorageUtil.localStorage.decodeString(StorageConstants.SHARE_TITLE, default)
    }

    /**
     * App切到后台时的时间戳
     */
    fun putSecurityCodeTime(securityCodeTime: Long) {
        StorageUtil.localStorage.encode(StorageConstants.SECURITY_CODE_TIME, securityCodeTime)
    }

    fun getSecurityCodeTime(default: Long = 0L): Long {
        return StorageUtil.localStorage.decodeLong(StorageConstants.SECURITY_CODE_TIME, default)
    }

    /**
     *  注册源
     */

    fun putResourceCodeRegister(codeRegister: String) {
        StorageUtil.localStorage.encode(StorageConstants.RESOURCE_CODE_REGISTER, codeRegister)
    }

    fun getResourceCodeRegister(default: String = ""): String {
        return StorageUtil.localStorage.decodeString(StorageConstants.RESOURCE_CODE_REGISTER, default)
    }

    /** endregion ------业务临时存储的数据结束------- */

    /** region -----产品交易量存储开始   ----*/

    /**
     * 存储产品下单时使用的交易手数
     * @param key 产品名称
     * @param value 手数
     */
    fun putProductLots(key: String, value: String) {
        StorageUtil.productLotsStorage.encode(key, value)
    }

    /**
     * 获取产品下单时使用的交易手数
     * @param key 产品名称
     * @param default 默认手数，一般是产品的最小可开手数
     */
    fun getProductLots(key: String, default: String): String {
        return StorageUtil.productLotsStorage.decodeString(key, default)
    }

    /** endregion -----产品交易量存储结束  ----*/

}