package cn.com.vau.common.greendao.common;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import cn.com.vau.common.greendao.dbUtils.StAccountInfoDetail;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "ST_ACCOUNT_INFO_DETAIL".
*/
public class StAccountInfoDetailDao extends AbstractDao<StAccountInfoDetail, Long> {

    public static final String TABLENAME = "ST_ACCOUNT_INFO_DETAIL";

    /**
     * Properties of entity StAccountInfoDetail.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "_id");
        public final static Property AccountId = new Property(1, String.class, "accountId", false, "ACCOUNT_ID");
        public final static Property StToken = new Property(2, String.class, "stToken", false, "ST_TOKEN");
        public final static Property ServerAccountId = new Property(3, String.class, "serverAccountId", false, "SERVER_ACCOUNT_ID");
        public final static Property MasterPortfolioId = new Property(4, String.class, "masterPortfolioId", false, "MASTER_PORTFOLIO_ID");
        public final static Property FollowPortfolioId = new Property(5, String.class, "followPortfolioId", false, "FOLLOW_PORTFOLIO_ID");
        public final static Property NickName = new Property(6, String.class, "nickName", false, "NICK_NAME");
        public final static Property ServerId = new Property(7, String.class, "serverId", false, "SERVER_ID");
        public final static Property CurrencyType = new Property(8, String.class, "currencyType", false, "CURRENCY_TYPE");
        public final static Property IsSignal = new Property(9, String.class, "isSignal", false, "IS_SIGNAL");
        public final static Property CreatedTime = new Property(10, String.class, "createdTime", false, "CREATED_TIME");
        public final static Property IsStarSignal = new Property(11, Boolean.class, "isStarSignal", false, "IS_STAR_SIGNAL");
        public final static Property StUserId = new Property(12, String.class, "stUserId", false, "ST_USER_ID");
    }


    public StAccountInfoDetailDao(DaoConfig config) {
        super(config);
    }
    
    public StAccountInfoDetailDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"ST_ACCOUNT_INFO_DETAIL\" (" + //
                "\"_id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"ACCOUNT_ID\" TEXT," + // 1: accountId
                "\"ST_TOKEN\" TEXT," + // 2: stToken
                "\"SERVER_ACCOUNT_ID\" TEXT," + // 3: serverAccountId
                "\"MASTER_PORTFOLIO_ID\" TEXT," + // 4: masterPortfolioId
                "\"FOLLOW_PORTFOLIO_ID\" TEXT," + // 5: followPortfolioId
                "\"NICK_NAME\" TEXT," + // 6: nickName
                "\"SERVER_ID\" TEXT," + // 7: serverId
                "\"CURRENCY_TYPE\" TEXT," + // 8: currencyType
                "\"IS_SIGNAL\" TEXT," + // 9: isSignal
                "\"CREATED_TIME\" TEXT," + // 10: createdTime
                "\"IS_STAR_SIGNAL\" INTEGER," + // 11: isStarSignal
                "\"ST_USER_ID\" TEXT);"); // 12: stUserId
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"ST_ACCOUNT_INFO_DETAIL\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, StAccountInfoDetail entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String accountId = entity.getAccountId();
        if (accountId != null) {
            stmt.bindString(2, accountId);
        }
 
        String stToken = entity.getStToken();
        if (stToken != null) {
            stmt.bindString(3, stToken);
        }
 
        String serverAccountId = entity.getServerAccountId();
        if (serverAccountId != null) {
            stmt.bindString(4, serverAccountId);
        }
 
        String masterPortfolioId = entity.getMasterPortfolioId();
        if (masterPortfolioId != null) {
            stmt.bindString(5, masterPortfolioId);
        }
 
        String followPortfolioId = entity.getFollowPortfolioId();
        if (followPortfolioId != null) {
            stmt.bindString(6, followPortfolioId);
        }
 
        String nickName = entity.getNickName();
        if (nickName != null) {
            stmt.bindString(7, nickName);
        }
 
        String serverId = entity.getServerId();
        if (serverId != null) {
            stmt.bindString(8, serverId);
        }
 
        String currencyType = entity.getCurrencyType();
        if (currencyType != null) {
            stmt.bindString(9, currencyType);
        }
 
        String isSignal = entity.getIsSignal();
        if (isSignal != null) {
            stmt.bindString(10, isSignal);
        }
 
        String createdTime = entity.getCreatedTime();
        if (createdTime != null) {
            stmt.bindString(11, createdTime);
        }
 
        Boolean isStarSignal = entity.getIsStarSignal();
        if (isStarSignal != null) {
            stmt.bindLong(12, isStarSignal ? 1L: 0L);
        }
 
        String stUserId = entity.getStUserId();
        if (stUserId != null) {
            stmt.bindString(13, stUserId);
        }
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, StAccountInfoDetail entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String accountId = entity.getAccountId();
        if (accountId != null) {
            stmt.bindString(2, accountId);
        }
 
        String stToken = entity.getStToken();
        if (stToken != null) {
            stmt.bindString(3, stToken);
        }
 
        String serverAccountId = entity.getServerAccountId();
        if (serverAccountId != null) {
            stmt.bindString(4, serverAccountId);
        }
 
        String masterPortfolioId = entity.getMasterPortfolioId();
        if (masterPortfolioId != null) {
            stmt.bindString(5, masterPortfolioId);
        }
 
        String followPortfolioId = entity.getFollowPortfolioId();
        if (followPortfolioId != null) {
            stmt.bindString(6, followPortfolioId);
        }
 
        String nickName = entity.getNickName();
        if (nickName != null) {
            stmt.bindString(7, nickName);
        }
 
        String serverId = entity.getServerId();
        if (serverId != null) {
            stmt.bindString(8, serverId);
        }
 
        String currencyType = entity.getCurrencyType();
        if (currencyType != null) {
            stmt.bindString(9, currencyType);
        }
 
        String isSignal = entity.getIsSignal();
        if (isSignal != null) {
            stmt.bindString(10, isSignal);
        }
 
        String createdTime = entity.getCreatedTime();
        if (createdTime != null) {
            stmt.bindString(11, createdTime);
        }
 
        Boolean isStarSignal = entity.getIsStarSignal();
        if (isStarSignal != null) {
            stmt.bindLong(12, isStarSignal ? 1L: 0L);
        }
 
        String stUserId = entity.getStUserId();
        if (stUserId != null) {
            stmt.bindString(13, stUserId);
        }
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public StAccountInfoDetail readEntity(Cursor cursor, int offset) {
        StAccountInfoDetail entity = new StAccountInfoDetail( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // accountId
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // stToken
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // serverAccountId
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // masterPortfolioId
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // followPortfolioId
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // nickName
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7), // serverId
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8), // currencyType
            cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9), // isSignal
            cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10), // createdTime
            cursor.isNull(offset + 11) ? null : cursor.getShort(offset + 11) != 0, // isStarSignal
            cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12) // stUserId
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, StAccountInfoDetail entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setAccountId(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setStToken(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setServerAccountId(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setMasterPortfolioId(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setFollowPortfolioId(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setNickName(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setServerId(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
        entity.setCurrencyType(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
        entity.setIsSignal(cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9));
        entity.setCreatedTime(cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10));
        entity.setIsStarSignal(cursor.isNull(offset + 11) ? null : cursor.getShort(offset + 11) != 0);
        entity.setStUserId(cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(StAccountInfoDetail entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(StAccountInfoDetail entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(StAccountInfoDetail entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
