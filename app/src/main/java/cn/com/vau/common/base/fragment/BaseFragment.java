package cn.com.vau.common.base.fragment;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import cn.com.vau.R;
import cn.com.vau.common.base.BaseFuncIml;
import cn.com.vau.common.view.FragmentUserVisibleController;
import cn.com.vau.common.view.dialog.CommonProcessDialog;
import cn.com.vau.util.ViewExtKt;
import io.reactivex.disposables.CompositeDisposable;

/**
 * Created by Administrator on 2016/12/31.
 * base
 */
public class BaseFragment extends Fragment implements BaseFuncIml, View.OnClickListener, FragmentUserVisibleController.UserVisibleCallback {

    private View mContentView;
    private ViewGroup container;
    private CommonProcessDialog loadNetDialog;

    private CompositeDisposable mRxManager;
    private final FragmentUserVisibleController userVisibleController;

//    public boolean isBaseUIVisible = false;
//    private boolean isBaseViewCreated = false;

    public BaseFragment() {
        userVisibleController = new FragmentUserVisibleController(this, this);
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        this.container = container;
        return this.mContentView = getActivity().getLayoutInflater().inflate(getContentView(), container, false);
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        userVisibleController.activityCreated();
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

//        isBaseViewCreated = true;

        lazyInit();

    }

    private void lazyInit() {

//        if (!isBaseViewCreated || !isBaseUIVisible) return;
//
//        isBaseViewCreated = false;

        initParam();
        initView();
        initFont();
        initData();
        registerObserves();
        initListener();

    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

    public int getContentView() {
        return 0;
    }

    public CompositeDisposable getRxManager() {
        if (mRxManager == null)
            mRxManager = new CompositeDisposable();
        return mRxManager;
    }

    @Override
    public void initParam() {
    }

    @Override
    public void initData() {
    }

    public void registerObserves() {
    }

    @Override
    public void initView() {
    }

    @Override
    public void initFont() {
    }

    @Override
    public void initListener() {
    }

    @Override
    public void onClick(View view) {
    }

    public void openActivity(Class<? extends Activity> toActivity) {
        openActivity(toActivity, null);
    }

    public void openActivity(Class<? extends Activity> toActivity, Bundle parameter, boolean isAnim) {
        if (getContext() == null) return;
        ViewExtKt.noRepeat(ViewExtKt.FAST_CLICK_INTERVAL, () -> {
            Intent intent = new Intent(getContext(), toActivity);
            if (parameter != null) {
                intent.putExtras(parameter);
            }
            startActivity(intent);
            if (isAnim && getActivity() != null) {
                getActivity().overridePendingTransition(R.anim.activity_in_right, R.anim.activity_out_left);
            }
            return null;
        });
    }

    public void openActivity(Class<? extends Activity> toActivity, Bundle parameter) {
        openActivity(toActivity, parameter, false);
    }

    public void openActivity(Class<?> cls, Bundle bundle, int requestCode) {
        if (getContext() == null) return;
        ViewExtKt.noRepeat(ViewExtKt.FAST_CLICK_INTERVAL, () -> {
            Intent intent = new Intent();
            intent.setClass(getContext(), cls);
            if (bundle != null) {
                intent.putExtras(bundle);
            }
            startActivityForResult(intent, requestCode);
            return null;
        });
    }

    public void showNetDialog() {
        if (loadNetDialog == null && getContext() != null) {
            loadNetDialog = new CommonProcessDialog(requireContext());
        }
        if (!loadNetDialog.isShowing()) {
            loadNetDialog.show();
        }
    }

    public void hideNetDialog() {
        if (loadNetDialog != null && loadNetDialog.isShowing())
            loadNetDialog.dismiss();
    }

    @Override
    public void onResume() {
        super.onResume();
        userVisibleController.resume();
    }

    @Override
    public void onPause() {
        super.onPause();
        userVisibleController.pause();
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        userVisibleController.setUserVisibleHint(!hidden);
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        userVisibleController.setUserVisibleHint(isVisibleToUser);
    }

    public void setWaitingShowToUser(boolean waitingShowToUser) {
        userVisibleController.setWaitingShowToUser(waitingShowToUser);
    }

    public boolean isWaitingShowToUser() {
        return userVisibleController.isWaitingShowToUser();
    }

    @Override
    public boolean isVisibleToUser() {
        return userVisibleController.isVisibleToUser();
    }

    @Override
    public void callSuperSetUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
    }

    @Override
    public void onVisibleToUserChanged(boolean isVisibleToUser, boolean invokeInResumeOrPause) {
//        if (isVisibleToUser) {
//            isBaseUIVisible = true;
//            lazyInit();
//        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mRxManager != null) mRxManager.clear();
        hideNetDialog();
    }

    public Boolean isDestroyed() {
        return getActivity() == null || getActivity().isDestroyed() || getActivity().isFinishing() || !isAdded() || isRemoving() || isDetached() || getContext() == null;
    }
}
