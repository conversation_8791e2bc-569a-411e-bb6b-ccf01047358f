package cn.com.vau.common.http.ws

import android.os.*
import android.util.Log
import cn.com.vau.common.application.InitHelper
import cn.com.vau.common.application.LinkStateManager
import cn.com.vau.common.application.VauApplication
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.event.KycSyncData
import cn.com.vau.common.event.TokenErrorData
import cn.com.vau.common.greendao.dbUtils.*
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.http.HttpUrl.Md5Salt
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.HandlerUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.common.view.timeSelection.PickerDateUtil.currentTimeMillisToString
import cn.com.vau.data.enums.*
import cn.com.vau.data.init.*
import cn.com.vau.util.*
import cn.com.vau.util.AppUtil.getVersionName
import cn.com.vau.util.base64.GZIPUtil
import cn.com.vau.util.opt.PerfTraceUtil
import com.google.gson.Gson
import com.neovisionaries.ws.client.*
import org.greenrobot.eventbus.EventBus
import java.io.IOException

class WsManager {

    private val initTag = "ApplicationInit"
    private val logSwitch = false    // 日志开关

    private val mGson: Gson by lazy { Gson() }
    private val CONNECT_TIMEOUT = 5000

    private val heartbeatInterval = 5L * 1000L // 心跳间隔
    private var heartbeatOffset = 0 // 心跳标识（大于*次收不到心跳会主动断开上传日志）

    private var startTimeMillisWs: Long = 0
    private var ws: WebSocket? = null

    private var mStatus = EnumWsState.CONNECT_FAIL

    private val handlerUtil by lazy { HandlerUtil() }
    private var reconnectHandler: Handler? = null
    private val thread by lazy { HandlerThread("WS-threadHandler") }

    companion object {
        private var instance: WsManager? = null
        fun getInstance(): WsManager {
            if (instance == null) {
                instance = WsManager()
            }
            return instance!!
        }
    }

    init {
        thread.start()
        reconnectHandler = Handler(thread.looper) {
            when (it.what) {
                // 自动重连 || 手动重连(重置)
                1 -> try {
                    createAndConnect()
                    log("---- WsManager ---- init {} ---- 重连")
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
            false
        }
    }

    private fun createAndConnect() {
        if (ws == null || WebSocketState.CLOSED == ws?.state || WebSocketState.CLOSING == ws?.state) {
            ws = WebSocketFactory().createSocket(HttpUrl.WebSocketUrl, CONNECT_TIMEOUT).setFrameQueueSize(5)
                .setMissingCloseFrameAllowed(false)
                .addListener(WsListener())
                .setPingInterval(heartbeatInterval)
                .connectAsynchronously() // 异步连接
        }
    }

    private fun connect() {
        log("---- WsManager ---- connect() ---- 开始链接")
        try {
            // 初始化连接
            createAndConnect()
            mStatus = EnumWsState.CONNECTING
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    inner class WsListener : WebSocketAdapter() {
        // 在发送帧之前调用。(两次)
        override fun onSendingFrame(websocket: WebSocket?, frame: WebSocketFrame?) {
            super.onSendingFrame(websocket, frame)

            if (heartbeatOffset >= 3) {
                EventBus.getDefault().post(NoticeConstants.WS.SOCKET_HEARTBEAT_ERROR)
                if (VauApplication.abOptNetParallel) {
                    LinkStateManager.dispatchNetSlow()
                }
            }

            if (heartbeatOffset > 5) {
                heartbeatOffset = 0
                resetConnect()
                return
            }

            ++heartbeatOffset

            // log("---- WsManager ---- onSendingFrame( --- ping --- $heartbeatOffset")

        }

        // 当接收到一个pong帧时调用
        override fun onPongFrame(websocket: WebSocket?, frame: WebSocketFrame?) {
            super.onPongFrame(websocket, frame)

            // log("---- WsManager ---- onPongFrame( --- pong --- $heartbeatOffset")

            if (heartbeatOffset >= 3) {
                EventBus.getDefault().post(NoticeConstants.WS.SOCKET_HEARTBEAT_NORMAL)
                if (VauApplication.abOptNetParallel) {
                    LinkStateManager.dispatchHeartbeatNormal()
                }
            }
            heartbeatOffset = 0
        }

        override fun onTextMessage(websocket: WebSocket?, text: String?) {
            super.onTextMessage(websocket, text)

            if (heartbeatOffset >= 3) {
                EventBus.getDefault().post(NoticeConstants.WS.SOCKET_HEARTBEAT_NORMAL)
                if (VauApplication.abOptNetParallel) {
                    LinkStateManager.dispatchHeartbeatNormal()
                }
            }
            heartbeatOffset = 0

            // 解析json
            val (messageType, data, message) = GsonUtil.fromJson(text, SocketData::class.java) ?: SocketData()

            when (messageType) {
                "0" -> {
                    handleMarketData(data)
                }
                // 全部
                "1" -> {
                    if (VauApplication.abOptNetParallel) {
                        //经过验证，restoreWsMsg这个方法不应该在handleMarketData()之后，因为如果一旦handleMarketData方法比较耗时，
                        // 就会不能第一时间告诉服务端收到了全推消息，可能会【增加】服务端的WebSocket多次推送全推数据
                        restoreWsMsg(ws, "", "11")
                    }
                    //TODO xinhuan 当WebSocket和产品列表并行请求后，如果全推数据过来，此时产品列表数据还没有返回，那么全推数据就丢弃了，就会有小部分概率有一些产品没有及时更新最新数据。IOS也是这样，暂时没有处理
                    handleMarketData(data)
                    if (!VauApplication.abOptNetParallel) {
                        //经过验证，restoreWsMsg这个方法不应该在handleMarketData()之后，因为如果一旦handleMarketData方法比较耗时，
                        // 就会不能第一时间告诉服务端收到了全推消息，可能会【增加】服务端的WebSocket多次推送全推数据
                        restoreWsMsg(ws, "", "11")
                    }
                }

                "2" -> {

                    log("---- WsManager ---- onTextMessage() --- 接收数据 --- $text")

                    val socketRecord = data?.record?.getOrNull(0)
                    if (8 == socketRecord?.type) {
                        SpManager.putLeverageTrade(socketRecord.UserRecord.leverage)
                        return
                    }

                    // 取订单集合
                    val orderKeyList = mutableListOf<String>()

                    for (socketRecord in data?.record ?: arrayListOf()) {
                        // 收集orderKey
                        orderKeyList.add(socketRecord.TradeRecord.orderKey)
                        /*
                         *  0：平仓，包括挂单变现价单
                         *  1：开仓，包括设置了止盈止损的订单自动平仓，有可能还包括爆仓
                         *  2：挂单
                         *  3：取消挂单(撤单)
                         *  设置止盈止损及改单没有通知
                         *  部分平仓会收到两个通知，显示1关单，再是0下单
                         */
                        val orderType = socketRecord.type
                        if (orderType == 0 || orderType == 1) {
                            EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_OPEN_ORDER)
                        } else if (orderType == 2 || orderType == 3) {
                            EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_PENDING_ORDER)
                        }
                    }
                    data?.record?.getOrNull(0)?.let { socketRecord ->
                        // 只有开仓和挂单进行弹窗
                        if (socketRecord.type == 1 || socketRecord.type == 2) {
                            if (!socketRecord.symbol.isNullOrEmpty() && !socketRecord.volume.isNullOrEmpty() && !socketRecord.price.isNullOrEmpty() && !socketRecord.cmd.isNullOrEmpty()) {
                                EventBus.getDefault().post(DataEvent(NoticeConstants.WS.INAPP_CHANGE_OF_ORDER, socketRecord))
                            }
                        }
                    }

                    // 发送orderKey
                    ws?.sendText(mGson.toJson(OrderReplyData(9, orderKeyList)))

                }

                "3" -> {
                    val nftData = message?.getOrNull(0)
                    log("---- WsManager ---- messageType=3 --- 接收数据 --- code: ${nftData?.type}")
                    when (nftData?.type) {
                        // 有新消息生成 （不回复，后端会每隔三秒推一次，推三次）
                        "1002" -> {
                            SpManager.putRedPointState(true)
                            EventBus.getDefault().post(NoticeConstants.WS.POINT_REMIND_MSG_SHOW)
                        }
                        // 消息全部已读
                        "1003" -> {
                            SpManager.putRedPointState(false)
                            EventBus.getDefault().post(NoticeConstants.WS.POINT_REMIND_MSG_HIDE)
                        }
                        // 资金变动
                        "1004" -> EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_FUNDS)
                        // 用户变组
                        "1005" -> EventBus.getDefault().post(NoticeConstants.WS.LOGIN_ERROR_CHANGE_OF_GROUP)
                        // token 异常 单点登录 || 1007: 用户信息修改 || 登录解绑
                        "1006", "1007", "1010" -> {
                            if (!UserDataUtil.isLogin()) return // 非登录态不需要执行下面退出逻辑
                            val eventData = GsonUtil.buildGson().fromJson(nftData.data, TokenErrorData::class.java)
                            EventBus.getDefault().post(
                                DataEvent(
                                    NoticeConstants.WS.LOGIN_ERROR_OF_TOKEN, eventData
                                )
                            )
                        }
                        // 赠送股票
                        "1008" -> EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_ORDER_FREE)
                        // 活动红点
                        "1009" -> EventBus.getDefault().post(NoticeConstants.WS.POINT_REMIND_PROMO_SHOW)
                        // 闭市 / 开市 通知
                        "1011" -> EventBus.getDefault().post(NoticeConstants.WS.SOCKET_MAINTENANCE_SYSTEM)
                        // KYC等级变动通知
                        "1012" -> {
                            handlerUtil.duringSingleTask(1000) {
                                EventBus.getDefault().post(NoticeConstants.WS.SOCKET_KYC_LEVEL_CHANGED)
                            }
                        }
                        // KYC双端数据同步通知
                        "1013" -> {
                            val eventData = GsonUtil.buildGson().fromJson(nftData.body, KycSyncData::class.java)
                            EventBus.getDefault().post(
                                DataEvent(NoticeConstants.WS.SOCKET_KYC_DATA_SYNCHRONIZATION, eventData)
                            )
                        }
                    }
                    restoreWsMsg(ws, nftData?.key.ifNull(), "10")
                }

                "4" -> {
                    // 产品 开市 闭市 通知
                    VAUSdkUtil.initMarketCloseState(data?.tradeTime ?: emptyList())
                }
            }
        }

        override fun onConnected(websocket: WebSocket?, headers: MutableMap<String, MutableList<String>>?) {
            super.onConnected(websocket, headers)
            log("---- WsManager ---- onConnected ---- WebSocket连接成功")

            PerfTraceUtil.stopTrace(PerfTraceUtil.StartTrace.Perf_v6_InitHelper_Start_Connected)

            if (VauApplication.abOptNetParallel) {
                LinkStateManager.dispatchWebSocketConnected()
            }
            EventBus.getDefault().post(NoticeConstants.Init.WS_SUCCESS_CONNECT)

            /**
             * -1:失败  0:成功  else 正在链接
             */
            if (!VauApplication.abOptNetParallel) {
                if (InitHelper.stepNum() == 0 || InitHelper.stepNum() == 2 || InitHelper.stepNum() == -1) {
                    //为什么需要从这里开始又重新走了一遍接口的调用呢？？
                    //王哥曰：未知其断久矣。若断时已久，且其订单有变（如挂单有变），则前端未得同步此讯。是以，吾需于此重请接口。
                    InitHelper.initialize(EnumInitStep.MT4_LOGIN, false)
                }
            }

            startTimeMillisWs = System.currentTimeMillis()
            DealLogUtil.saveSuccessDealLog("connect", startTimeMillisWs)
            // 回传验证信息
            ws?.sendText(returnUserParams())
            mStatus = EnumWsState.CONNECT_SUCCESS

            reconnectHandler?.removeCallbacksAndMessages(null)
        }

        override fun onConnectError(websocket: WebSocket?, exception: WebSocketException?) {
            super.onConnectError(websocket, exception)

            log("---- WsManager ---- onConnectError ---- WebSocket连接失败 ---- State=${websocket?.state}", Log.ERROR)

            mStatus = EnumWsState.CONNECT_FAIL      // 连接失败
            EventBus.getDefault().post(NoticeConstants.WS.SOCKET_DISCONNECTED)
            if (VauApplication.abOptNetParallel) {
                LinkStateManager.dispatchDisconnected()
            }

            // 连接错误的时候调用重连方法
            reconnect()

            DealLogUtil.saveFailedDealLog("-1", "connect + ${exception?.message ?:"-"}", startTimeMillisWs)
        }

        // 断开
        override fun onDisconnected(
            websocket: WebSocket?,
            serverCloseFrame: WebSocketFrame?,
            clientCloseFrame: WebSocketFrame?,
            closedByServer: Boolean
        ) {
            super.onDisconnected(websocket, serverCloseFrame, clientCloseFrame, closedByServer)

            log("---- WsManager ---- onDisconnected ---- WebSocket断开连接 ---- State=${websocket?.state}", Log.ERROR)

            EventBus.getDefault().post(NoticeConstants.WS.SOCKET_DISCONNECTED)
            if (VauApplication.abOptNetParallel) {
                LinkStateManager.dispatchDisconnected()
            }

            if (mStatus == EnumWsState.RESET) {
                reconnect(1000)
            } else {
                mStatus = EnumWsState.CONNECT_FAIL // 连接失败
                // 连接错误的时候调用重连方法
                reconnect()
            }
            saveDisconnectWsDealLog()
        }
    }

    // 3s后自动重新连接
    private fun reconnect(delay: Long = 3000) {

        // 链接状态 || 重连状态 || 手动断开
        if (ws == null || true == ws?.isOpen || mStatus == EnumWsState.CONNECTING || mStatus == EnumWsState.BREAK) {
            return
        }
        log("---- WsManager ---- reconnect() ---- 自动重新连接 ---- delay=$delay ")
        mStatus = EnumWsState.CONNECTING
        reconnectHandler?.removeCallbacksAndMessages(null)
        reconnectHandler?.sendEmptyMessageDelayed(1, delay)

    }

    // 手动断开ws -> 1s后自动重连
    fun resetConnect() {
        log("---- WsManager ---- resetConnect() ---- 心跳异常 or 手动断开ws -> 1s后自动重连 ---- 分割 ----")
        if (ws != null && WebSocketState.CLOSED != ws?.state) {
            mStatus = EnumWsState.RESET
            ws?.disconnect()
        } else {
            connect()
        }
    }

    // 手动断开ws并置空 -> 不会自动重连
    fun breakSocket() {
        log("---- WsManager ---- breakSocket() ---- 手动断开ws并置空 -> 不会自动重连")
        //清除失败自动重连机制
        reconnectHandler?.removeCallbacksAndMessages(null)
        mStatus = EnumWsState.BREAK
        ws?.disconnect()
        ws = null
    }

    private fun saveDisconnectWsDealLog() {
        if (!UserDataUtil.isLogin()) return
        val dealLogInfo = DealLogInfo()
        dealLogInfo.timeStamp = System.currentTimeMillis().toString() + ""
        dealLogInfo.date = currentTimeMillisToString("dd/MM/yyyy HH:mm:ss")
        dealLogInfo.log = "account:" + UserDataUtil.accountCd() + "  disconnect"
        dealLogInfo.tel = UserDataUtil.userTel()
        DbManager.getInstance().saveDealLog(dealLogInfo)
    }

    private fun restoreWsMsg(ws: WebSocket?, key: String, msgType: String) {
        val noticesList = ArrayList<String>()
        noticesList.add(key)
        val ts = System.currentTimeMillis().toString() + ""
        val paramMap = HashMap<String, Any>()
        paramMap["messageType"] = msgType
        paramMap["notices"] = noticesList
        paramMap["ts"] = ts
        paramMap["sign"] = MD5Util.parseStrToMd5U32(ts + Md5Salt)
        paramMap["timeZone"] = AppUtil.getTimeZoneRawOffsetToHour()
        ws?.sendText(mGson.toJson(paramMap))
    }

    private fun returnUserParams(): String {
        val paramMap = java.util.HashMap<String, Any>()
        // 1：全部产品订阅 2：取消全部订阅 3：单个产品订阅 4取消单个产品订阅 7：接受ping消息
        paramMap["messageType"] = "1"
        // mt4账号 AU(5, "AU"),UK(2, "UK"),MXT(3, "MXT"),MT5(8,"MT5"),默认未登录显示AU服务器行情。
        if (!UserDataUtil.isLogin()) {
            paramMap["serverId"] = "5"
            paramMap["mt4account"] = ""
        } else {
            paramMap["serverId"] = UserDataUtil.serverId()
            paramMap["mt4account"] = UserDataUtil.accountCd()
        }
        paramMap["mobileType"] = AppUtil.getSystemModel()
        paramMap["version"] = getVersionName()
        paramMap["timeZone"] = AppUtil.getTimeZoneRawOffsetToHour().toString() + ""
        // 是否压缩
        paramMap["compreType"] = "1"
        paramMap["noTradeUserId"] = UserDataUtil.userId()

        val ts = System.currentTimeMillis().toString() + ""
        paramMap["ts"] = ts
        paramMap["sign"] = MD5Util.parseStrToMd5U32(ts + Md5Salt)

        return mGson.toJson(paramMap)
    }

    private fun handleMarketData(data: SocketDataBean?) {
        val uncompress = GZIPUtil.uncompress(data?.market)
        data?.st?.let {
            VAUSdkUtil.serverTimeMillis = it
        }
        VAUSdkUtil.updateQuotation(
            GsonUtil.fromJson(uncompress, MarketsData::class.java)
        )
    }

    private fun log(content: String, level: Int = Log.INFO) {
        if (logSwitch) {
            when (level) {
                Log.DEBUG -> Log.d(initTag, content)
                Log.INFO -> Log.i(initTag, content)
                Log.WARN -> Log.w(initTag, content)
                Log.ERROR -> Log.e(initTag, content)
            }
        }
    }

    fun getWsState() = ws?.state
}