package cn.com.vau.common.greendao.common;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import cn.com.vau.common.greendao.dbUtils.ExtendInfo;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "EXTEND_INFO".
*/
public class ExtendInfoDao extends AbstractDao<ExtendInfo, Long> {

    public static final String TABLENAME = "EXTEND_INFO";

    /**
     * Properties of entity ExtendInfo.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "_id");
        public final static Property TryTimes = new Property(1, String.class, "tryTimes", false, "TRY_TIMES");
        public final static Property State = new Property(2, String.class, "state", false, "STATE");
        public final static Property Msg = new Property(3, String.class, "msg", false, "MSG");
        public final static Property Res = new Property(4, String.class, "res", false, "RES");
        public final static Property Reqtime = new Property(5, String.class, "reqtime", false, "REQTIME");
    }


    public ExtendInfoDao(DaoConfig config) {
        super(config);
    }
    
    public ExtendInfoDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"EXTEND_INFO\" (" + //
                "\"_id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"TRY_TIMES\" TEXT," + // 1: tryTimes
                "\"STATE\" TEXT," + // 2: state
                "\"MSG\" TEXT," + // 3: msg
                "\"RES\" TEXT," + // 4: res
                "\"REQTIME\" TEXT);"); // 5: reqtime
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"EXTEND_INFO\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, ExtendInfo entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String tryTimes = entity.getTryTimes();
        if (tryTimes != null) {
            stmt.bindString(2, tryTimes);
        }
 
        String state = entity.getState();
        if (state != null) {
            stmt.bindString(3, state);
        }
 
        String msg = entity.getMsg();
        if (msg != null) {
            stmt.bindString(4, msg);
        }
 
        String res = entity.getRes();
        if (res != null) {
            stmt.bindString(5, res);
        }
 
        String reqtime = entity.getReqtime();
        if (reqtime != null) {
            stmt.bindString(6, reqtime);
        }
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, ExtendInfo entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String tryTimes = entity.getTryTimes();
        if (tryTimes != null) {
            stmt.bindString(2, tryTimes);
        }
 
        String state = entity.getState();
        if (state != null) {
            stmt.bindString(3, state);
        }
 
        String msg = entity.getMsg();
        if (msg != null) {
            stmt.bindString(4, msg);
        }
 
        String res = entity.getRes();
        if (res != null) {
            stmt.bindString(5, res);
        }
 
        String reqtime = entity.getReqtime();
        if (reqtime != null) {
            stmt.bindString(6, reqtime);
        }
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public ExtendInfo readEntity(Cursor cursor, int offset) {
        ExtendInfo entity = new ExtendInfo( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // tryTimes
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // state
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // msg
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // res
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5) // reqtime
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, ExtendInfo entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setTryTimes(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setState(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setMsg(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setRes(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setReqtime(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(ExtendInfo entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(ExtendInfo entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(ExtendInfo entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
