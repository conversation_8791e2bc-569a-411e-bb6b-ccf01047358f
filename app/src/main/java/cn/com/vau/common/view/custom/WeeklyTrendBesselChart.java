package cn.com.vau.common.view.custom;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Point;
import android.graphics.Shader;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import cn.com.vau.R;
import cn.com.vau.util.AppUtil;
import cn.com.vau.util.DistKt;
import cn.com.vau.util.ExpandKt;

/**
 * 热门产品
 */
public class WeeklyTrendBesselChart extends View {

    //主数据线画笔
    private Paint mPaint;
    //纬线画笔
    private Paint mLongitudeLinePaint = null;
    //经线画笔
    private Paint mLatitudeLinePaint = null;

    private Paint cubicPaintFill;
    private int lineColor;
    private float strokeWidth;
    private int latitudeColor;
    private int longitudeColor;
    private int cubicFillColorStart, cubicFillColorEnd;
    private boolean showLatitudeLine, showLongitudeLine;
    private List<Float> pointDataList = new ArrayList<>();
    private List<Point> pointList;

    private float yDisplayWeight = 0.1f;    //百分比
    private int latitudeNum = 4;
    private int longitudeNum = 0;
    private float mYMax = 0f;
    private float mYMin = 0f;
    private boolean displayFull = false;
    // 强制使用绿色
    private boolean forceTrendColor = false;

    public WeeklyTrendBesselChart(Context context) {
        this(context, null);
    }

    public WeeklyTrendBesselChart(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public WeeklyTrendBesselChart(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initXmlAttrs(context, attrs);
        initPaint(context);
    }


    private void initPaint(Context context) {
        mPaint = new Paint();
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setAntiAlias(true);
        mPaint.setColor(lineColor);
        mPaint.setStrokeWidth(DistKt.dp2px(strokeWidth));

        cubicPaintFill = new Paint();
        cubicPaintFill.setAntiAlias(true); // 防鋸齒
        cubicPaintFill.setStyle(Paint.Style.FILL_AND_STROKE);
        cubicPaintFill.setStrokeWidth(0);

        if (showLatitudeLine) {
            mLatitudeLinePaint = new Paint();
            mLatitudeLinePaint.setStyle(Paint.Style.STROKE);
            mLatitudeLinePaint.setColor(latitudeColor);
            mLatitudeLinePaint.setStrokeWidth(1f);
        }

        if (showLongitudeLine) {
            mLongitudeLinePaint = new Paint();
            mLongitudeLinePaint.setStyle(Paint.Style.STROKE);
            mLongitudeLinePaint.setColor(longitudeColor);
            mLongitudeLinePaint.setStrokeWidth(1f);
        }
    }

    private void initXmlAttrs(Context context, AttributeSet attrs) {
        @SuppressLint("CustomViewStyleable") TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.WeeklyTrendBesselChart);
        if (typedArray == null) return;
        lineColor = typedArray.getColor(R.styleable.WeeklyTrendBesselChart_lineColor, getResources().getColor(R.color.cffffff));
        strokeWidth = typedArray.getFloat(R.styleable.WeeklyTrendBesselChart_strokewidth, 1f);
        latitudeColor = typedArray.getColor(R.styleable.WeeklyTrendBesselChart_jingLineColor, -1);
        longitudeColor = typedArray.getColor(R.styleable.WeeklyTrendBesselChart_weiLineColor, -1);
        typedArray.recycle();
        showLatitudeLine = latitudeColor != -1;
        showLongitudeLine = longitudeColor != -1;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        drawLatitudeLine(canvas);
        drawDataSource(canvas);
    }

    private void drawLatitudeLine(Canvas canvas) {
        if (showLatitudeLine) {
            //经线间宽度
            float latitudeSpace = getHeight() / (latitudeNum - 1);
            Path path = new Path();
            for (int i = 0; i < latitudeNum; i++) {
                float tempLatitudeSpace = i * latitudeSpace;//经线间隙
                if (i == 0 || i == latitudeNum - 1) {
                }else {
                    path.moveTo(0, tempLatitudeSpace);
                    path.lineTo(getWidth(), tempLatitudeSpace);
                    canvas.drawPath(path, mLatitudeLinePaint);
                    path.reset();
                }
            }
        }
    }

    private void drawDataSource(Canvas canvas) {
        if (longitudeNum > 1) {
            pointList = new ArrayList<>();
            for (int i = 0; i < pointDataList.size(); i++) {
                Point point = new Point();
                point.set(
                        getWidth() / (longitudeNum - 1) * i,
                        (int) (
                                (1f - (pointDataList.get(i) - mYMin) / (mYMax - mYMin)) * getHeight()
                        )
                );
                pointList.add(point);
            }
            drawScrollLine(canvas, pointList);
        }
    }

    private void drawScrollLine(Canvas canvas, List<Point> dataPointList) {
        Point startp = new Point();
        Point endp = new Point();
        Point firstPoint = new Point();
        Path path = new Path();
        for (int i = 0; i < dataPointList.size() - 1; i++) {
            startp = dataPointList.get(i);
            endp = dataPointList.get(i + 1);
            if(i == 0) {
                path.moveTo(startp.x, startp.y);
                firstPoint = startp;
            }
            int wt = (startp.x + endp.x) / 2;
            Point p3 = new Point();
            Point p4 = new Point();
            p3.y = startp.y;
            p3.x = wt;
            p4.y = endp.y;
            p4.x = wt;


            //path.moveTo(startp.x, startp.y);
            path.cubicTo(p3.x, p3.y, p4.x, p4.y, endp.x, endp.y);
        }
        canvas.drawPath(path, mPaint);

        path.lineTo(endp.x, getHeight()); //把曲線迴圈最後給收尾封閉到原來的起點
        path.lineTo(firstPoint.x, getHeight());
        path.close(); //封閉

        //漸層效果
        LinearGradient lg = new LinearGradient(0, 0, 0, getHeight(), cubicFillColorStart, cubicFillColorEnd, Shader.TileMode.CLAMP);// CLAMP重复最后一个颜色至最后
        cubicPaintFill.setShader(lg);
        canvas.drawPath(path, cubicPaintFill); //畫實心曲線
    }

    public void setDisplayFull (boolean displayFull){
        this.displayFull = displayFull;
    }

    public void setForceTrendColor(boolean forceTrendColor) {
        this.forceTrendColor = forceTrendColor;
    }

    private Boolean isDisplayFull (){
        return displayFull;
    }

    public void setData(ArrayList<String> datas, boolean destineColor) {
        pointDataList = getData(datas);
        if (pointDataList.size() > 1) {
            mYMax = Collections.max(pointDataList);
            mYMin = Collections.min(pointDataList);
            longitudeNum = pointDataList.size();
            if (destineColor) {
                Float lastPoint = pointDataList.get(pointDataList.size() - 1);
                Float firstPoint = pointDataList.get(0);
                if (lastPoint > firstPoint || forceTrendColor) {
                    lineColor = getResources().getColor(R.color.c00c79c);
                    cubicFillColorStart = getResources().getColor(R.color.c1f00c79c);
                    cubicFillColorEnd = getResources().getColor(R.color.transparent);
                } else if (lastPoint < firstPoint) {
                    lineColor = getResources().getColor(R.color.ce35728);
                    cubicFillColorStart = getResources().getColor(R.color.c1fe35728);
                    cubicFillColorEnd = getResources().getColor(R.color.transparent);
                } else {
                    lineColor = getResources().getColor(getRose0LineColor());
                    cubicFillColorStart = getResources().getColor(getRose0FillColor());
                    cubicFillColorEnd = getResources().getColor(R.color.transparent);
                }
                mPaint.setColor(lineColor);
            }
        }
        mYMax = mYMax + (mYMax - mYMin) * yDisplayWeight;
        mYMin = mYMin - (mYMax - mYMin) * yDisplayWeight;
        if (mYMax == mYMin) {
            mYMax += 1f;
            mYMin -= 1f;
        }
        invalidate();
    }

    private int getRose0LineColor() {
        return AppUtil.isLightTheme()? R.color.c731e1e1e : R.color.c61ffffff;
    }

    private int getRose0FillColor() {
        return AppUtil.isLightTheme()? R.color.c1f1e1e1e : R.color.c1fffffff;
    }

    private ArrayList<Float> getData(ArrayList<String> datas) {
        ArrayList<Float> list = new ArrayList<>();
        if (datas != null && datas.size() > 1) {
            if (isDisplayFull()){
                for (String data : datas) {
                    list.add(ExpandKt.toFloatCatching(data, 0f));
                }
                return list;
            }
            if (datas.size() > 7) {
                for (int i = datas.size() - 7; i < datas.size(); i++) {
                    list.add(ExpandKt.toFloatCatching(datas.get(i), 0f));
                }
            }else if (datas.size() > 1 && datas.size() <= 7) {
                for (String data : datas) {
                    list.add(ExpandKt.toFloatCatching(data, 0f));
                }
            }
        }
        //Collections.reverse(list);
        return list;
    }

}
