package cn.com.vau.common.view

import android.content.Context
import android.graphics.Paint
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.common.application.InitHelper
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.data.init.ShareAccountInfoData
import cn.com.vau.databinding.IncludeFragmentTradesLoginLiveAccountBinding
import cn.com.vau.trade.ext.copyData
import cn.com.vau.trade.ext.toMarginLevelColor
import cn.com.vau.util.*
import kotlinx.coroutines.*

class AccountLoggedInView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private var mBinding: IncludeFragmentTradesLoginLiveAccountBinding? = null

    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff) }
    private val ce35728 by lazy { ContextCompat.getColor(context, R.color.ce35728) }
    private val cf44040 by lazy { ContextCompat.getColor(context, R.color.cf44040) }
    private var onMarginLevelClick: (() -> Unit)? = null

    init {
        mBinding = IncludeFragmentTradesLoginLiveAccountBinding.inflate(LayoutInflater.from(context), this)
        mBinding?.tvMarginLevelTitle?.paintFlags = Paint.UNDERLINE_TEXT_FLAG
        mBinding?.tvMarginLevelTitle?.setOnClickListener {
            onMarginLevelClick?.invoke()
        }
    }

    fun refreshLoggedInBoard(enableColorRes: Int, shareAccountBean: ShareAccountInfoData) {
        // 行情维护 || 尚未初始化完畢
        if (Constants.MARKET_MAINTAINING || InitHelper.isNotSuccess()) {
            // 已登录布局
            mBinding?.tvEquity?.setTextDiff("...")
            mBinding?.tvCurrency?.setTextDiff("")
            mBinding?.tvFloatingPnL?.setTextDiff("...")
            mBinding?.tvFloatingPnL?.setTextColorDiff(color_c1e1e1e_cebffffff)
            mBinding?.tvMarginLevel?.setTextDiff("...")
            mBinding?.tvMarginLevel?.setTextColorDiff(color_c1e1e1e_cebffffff)
            return
        }

        MainScope().launch {
            val equityUi = withContext(Dispatchers.Default) { shareAccountBean.equity.numCurrencyFormat2() }
            val floatingPnlUi = withContext(Dispatchers.Default) { shareAccountBean.profit.numCurrencyFormat2() }
            // 已登录布局
            mBinding?.tvEquity?.setTextDiff(equityUi)
            mBinding?.tvFloatingPnL?.setTextDiff("${if (shareAccountBean.profit > 0) "+" else ""}${floatingPnlUi}")
        }

        mBinding?.tvCurrency?.setTextDiff(UserDataUtil.currencyType())
        mBinding?.tvFloatingPnL?.setTextColorDiff(enableColorRes)

        if (shareAccountBean.marginLevel == 0.0) {
            mBinding?.tvMarginLevel?.setTextDiff("---")
        } else {
            MainScope().launch {
                val marginLevelUi = withContext(Dispatchers.Default) {
                    shareAccountBean.marginLevel.numFormat2(2, false)
                }
                mBinding?.tvMarginLevel?.setTextDiff("${marginLevelUi}%")
            }
        }
        mBinding?.tvMarginLevel?.setTextColorDiff(shareAccountBean.copyData().toMarginLevelColor(context))
    }

    fun appInBackgroundMoreThan1m() {
        mBinding?.tvEquity?.text = "..."
        mBinding?.tvCurrency?.text = ""
        mBinding?.tvFloatingPnL?.setTextColor(color_c1e1e1e_cebffffff)
        mBinding?.tvFloatingPnL?.text = "..."
        mBinding?.tvMarginLevel?.text = "..."
        mBinding?.tvMarginLevel?.setTextColor(color_c1e1e1e_cebffffff)
    }

    fun showBannerEffect(bannerVisible: Boolean) {
        mBinding?.viewSplit?.isVisible = bannerVisible
    }

    // 点击预付款比
    fun setOnMarginLevelClick(callback: (() -> Unit)?) {
        onMarginLevelClick = callback
    }
}