package cn.com.vau.common.utils;

import java.util.regex.Pattern;

/**
 * Created by roy on 2018/11/16.
 * 计算
 * double类型创建BigDecimal对象的时候，可能会出现精度误差，不建议使用。
 * 如果必须创建Double类型使用：
 * new BigDecimal(Double.toString(bigDecimal)) / BigDecimal.valueOf(bigDecimal)
 */
public class OrderVolumeUtil {

    /**
     * 判断是否倍数或整除
     *
     * @param x 大值
     * @param y 小值
     * @return 结果
     */
    public static boolean isDivideExactly(Double x, Double y) {
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        Integer newX = 1;
        Integer newY = 1;
        if (x <= 1 && y < 1) {
            int t1 = x.toString().split("\\.")[1].length();
            int t2 = y.toString().split("\\.")[1].length();
            int pInt = 1;
            if (t1 > t2) {
                pInt = t1;
            } else if (t1 < t2) {
                pInt = t2;
            } else {
                pInt = t1;
            }
            Double powNum = Math.pow(10, pInt);
            Double xNum = powNum * x;
            Double yNum = powNum * y;
            // 同乘相同倍数
            newX = xNum.intValue();
            newY = yNum.intValue();
        } else if (x >= 1 && y < 1) {
            if (x % 1 == 0) {
                newX = x.intValue();
                System.err.println("目标数检测到整数:" + x);
            }
            if (y % 1 == 0) {
                newY = y.intValue();
                System.err.println("来源数检测到整数:" + y);
            }
            int t1 = x.toString().split("\\.")[1].length();
            int t2 = y.toString().split("\\.")[1].length();
            int pInt = 1;
            if (t1 > t2) {
                pInt = t1;
            } else if (t1 < t2) {
                pInt = t2;
            } else {
                pInt = t1;
            }
            Double powNum = Math.pow(10, pInt);
            Double xNum = powNum * x;
            Double yNum = powNum * y;
            // 同乘相同倍数
            newX = xNum.intValue();
            newY = yNum.intValue();
        } else if (x >= 1 && y >= 1) {
            if (pattern.matcher(x.toString()).matches()) {
                newX = x.intValue();
                System.err.println("目标数检测到整数:" + x);
            }
            if (pattern.matcher(y.toString()).matches()) {
                newY = y.intValue();
                System.err.println("来源数检测到整数:" + y);
            }
            int t1 = x.toString().split("\\.")[1].length();
            int t2 = y.toString().split("\\.")[1].length();
            int pInt = 1;
            if (t1 > t2) {
                pInt = t1;
            } else if (t1 < t2) {
                pInt = t2;
            } else {
                pInt = t1;
            }
            Double powNum = Math.pow(10, pInt);
            Double xNum = powNum * x;
            Double yNum = powNum * y;
            // 同乘相同倍数
            newX = xNum.intValue();
            newY = yNum.intValue();
        }
        if (newX % 1 == 0 && newY % 1 == 0) {
            if (newY == 0 || newX % newY == 0) {
                return true;
            } else {
                return false;
            }

        } else {
            return false;
        }
    }

}
