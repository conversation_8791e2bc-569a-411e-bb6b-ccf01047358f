package cn.com.vau.common.greendao.common;

import java.util.Map;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.AbstractDaoSession;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.identityscope.IdentityScopeType;
import org.greenrobot.greendao.internal.DaoConfig;

import cn.com.vau.common.greendao.dbUtils.DealLogInfo;
import cn.com.vau.common.greendao.dbUtils.ErrorLogInfo;
import cn.com.vau.common.greendao.dbUtils.ExtendInfo;
import cn.com.vau.common.greendao.dbUtils.StAccountInfoDetail;
import cn.com.vau.common.greendao.dbUtils.UserEmailHistory;
import cn.com.vau.common.greendao.dbUtils.UserInfoDetail;
import cn.com.vau.common.greendao.dbUtils.UserPhoneHistory;

import cn.com.vau.common.greendao.common.DealLogInfoDao;
import cn.com.vau.common.greendao.common.ErrorLogInfoDao;
import cn.com.vau.common.greendao.common.ExtendInfoDao;
import cn.com.vau.common.greendao.common.StAccountInfoDetailDao;
import cn.com.vau.common.greendao.common.UserEmailHistoryDao;
import cn.com.vau.common.greendao.common.UserInfoDetailDao;
import cn.com.vau.common.greendao.common.UserPhoneHistoryDao;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.

/**
 * {@inheritDoc}
 * 
 * @see org.greenrobot.greendao.AbstractDaoSession
 */
public class DaoSession extends AbstractDaoSession {

    private final DaoConfig dealLogInfoDaoConfig;
    private final DaoConfig errorLogInfoDaoConfig;
    private final DaoConfig extendInfoDaoConfig;
    private final DaoConfig stAccountInfoDetailDaoConfig;
    private final DaoConfig userEmailHistoryDaoConfig;
    private final DaoConfig userInfoDetailDaoConfig;
    private final DaoConfig userPhoneHistoryDaoConfig;

    private final DealLogInfoDao dealLogInfoDao;
    private final ErrorLogInfoDao errorLogInfoDao;
    private final ExtendInfoDao extendInfoDao;
    private final StAccountInfoDetailDao stAccountInfoDetailDao;
    private final UserEmailHistoryDao userEmailHistoryDao;
    private final UserInfoDetailDao userInfoDetailDao;
    private final UserPhoneHistoryDao userPhoneHistoryDao;

    public DaoSession(Database db, IdentityScopeType type, Map<Class<? extends AbstractDao<?, ?>>, DaoConfig>
            daoConfigMap) {
        super(db);

        dealLogInfoDaoConfig = daoConfigMap.get(DealLogInfoDao.class).clone();
        dealLogInfoDaoConfig.initIdentityScope(type);

        errorLogInfoDaoConfig = daoConfigMap.get(ErrorLogInfoDao.class).clone();
        errorLogInfoDaoConfig.initIdentityScope(type);

        extendInfoDaoConfig = daoConfigMap.get(ExtendInfoDao.class).clone();
        extendInfoDaoConfig.initIdentityScope(type);

        stAccountInfoDetailDaoConfig = daoConfigMap.get(StAccountInfoDetailDao.class).clone();
        stAccountInfoDetailDaoConfig.initIdentityScope(type);

        userEmailHistoryDaoConfig = daoConfigMap.get(UserEmailHistoryDao.class).clone();
        userEmailHistoryDaoConfig.initIdentityScope(type);

        userInfoDetailDaoConfig = daoConfigMap.get(UserInfoDetailDao.class).clone();
        userInfoDetailDaoConfig.initIdentityScope(type);

        userPhoneHistoryDaoConfig = daoConfigMap.get(UserPhoneHistoryDao.class).clone();
        userPhoneHistoryDaoConfig.initIdentityScope(type);

        dealLogInfoDao = new DealLogInfoDao(dealLogInfoDaoConfig, this);
        errorLogInfoDao = new ErrorLogInfoDao(errorLogInfoDaoConfig, this);
        extendInfoDao = new ExtendInfoDao(extendInfoDaoConfig, this);
        stAccountInfoDetailDao = new StAccountInfoDetailDao(stAccountInfoDetailDaoConfig, this);
        userEmailHistoryDao = new UserEmailHistoryDao(userEmailHistoryDaoConfig, this);
        userInfoDetailDao = new UserInfoDetailDao(userInfoDetailDaoConfig, this);
        userPhoneHistoryDao = new UserPhoneHistoryDao(userPhoneHistoryDaoConfig, this);

        registerDao(DealLogInfo.class, dealLogInfoDao);
        registerDao(ErrorLogInfo.class, errorLogInfoDao);
        registerDao(ExtendInfo.class, extendInfoDao);
        registerDao(StAccountInfoDetail.class, stAccountInfoDetailDao);
        registerDao(UserEmailHistory.class, userEmailHistoryDao);
        registerDao(UserInfoDetail.class, userInfoDetailDao);
        registerDao(UserPhoneHistory.class, userPhoneHistoryDao);
    }
    
    public void clear() {
        dealLogInfoDaoConfig.clearIdentityScope();
        errorLogInfoDaoConfig.clearIdentityScope();
        extendInfoDaoConfig.clearIdentityScope();
        stAccountInfoDetailDaoConfig.clearIdentityScope();
        userEmailHistoryDaoConfig.clearIdentityScope();
        userInfoDetailDaoConfig.clearIdentityScope();
        userPhoneHistoryDaoConfig.clearIdentityScope();
    }

    public DealLogInfoDao getDealLogInfoDao() {
        return dealLogInfoDao;
    }

    public ErrorLogInfoDao getErrorLogInfoDao() {
        return errorLogInfoDao;
    }

    public ExtendInfoDao getExtendInfoDao() {
        return extendInfoDao;
    }

    public StAccountInfoDetailDao getStAccountInfoDetailDao() {
        return stAccountInfoDetailDao;
    }

    public UserEmailHistoryDao getUserEmailHistoryDao() {
        return userEmailHistoryDao;
    }

    public UserInfoDetailDao getUserInfoDetailDao() {
        return userInfoDetailDao;
    }

    public UserPhoneHistoryDao getUserPhoneHistoryDao() {
        return userPhoneHistoryDao;
    }

}
