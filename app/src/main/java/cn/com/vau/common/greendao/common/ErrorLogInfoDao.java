package cn.com.vau.common.greendao.common;

import java.util.List;
import java.util.ArrayList;
import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.SqlUtils;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import cn.com.vau.common.greendao.dbUtils.ExtendInfo;

import cn.com.vau.common.greendao.dbUtils.ErrorLogInfo;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "ERROR_LOG_INFO".
*/
public class ErrorLogInfoDao extends AbstractDao<ErrorLogInfo, Long> {

    public static final String TABLENAME = "ERROR_LOG_INFO";

    /**
     * Properties of entity ErrorLogInfo.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "_id");
        public final static Property Mt4Id = new Property(1, String.class, "mt4Id", false, "MT4_ID");
        public final static Property ServerId = new Property(2, String.class, "serverId", false, "SERVER_ID");
        public final static Property LogType = new Property(3, String.class, "logType", false, "LOG_TYPE");
        public final static Property Host = new Property(4, String.class, "host", false, "HOST");
        public final static Property ReqPath = new Property(5, String.class, "reqPath", false, "REQ_PATH");
        public final static Property DeviceNet = new Property(6, String.class, "deviceNet", false, "DEVICE_NET");
        public final static Property ReqTime = new Property(7, String.class, "reqTime", false, "REQ_TIME");
        public final static Property TimeZone = new Property(8, String.class, "timeZone", false, "TIME_ZONE");
        public final static Property ErrorInfo = new Property(9, String.class, "errorInfo", false, "ERROR_INFO");
        public final static Property DeviceIp = new Property(10, String.class, "deviceIp", false, "DEVICE_IP");
        public final static Property ParamInfo = new Property(11, String.class, "paramInfo", false, "PARAM_INFO");
        public final static Property ExtendInfoId = new Property(12, Long.class, "extendInfoId", false, "EXTEND_INFO_ID");
    }

    private DaoSession daoSession;


    public ErrorLogInfoDao(DaoConfig config) {
        super(config);
    }
    
    public ErrorLogInfoDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
        this.daoSession = daoSession;
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"ERROR_LOG_INFO\" (" + //
                "\"_id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"MT4_ID\" TEXT," + // 1: mt4Id
                "\"SERVER_ID\" TEXT," + // 2: serverId
                "\"LOG_TYPE\" TEXT," + // 3: logType
                "\"HOST\" TEXT," + // 4: host
                "\"REQ_PATH\" TEXT," + // 5: reqPath
                "\"DEVICE_NET\" TEXT," + // 6: deviceNet
                "\"REQ_TIME\" TEXT," + // 7: reqTime
                "\"TIME_ZONE\" TEXT," + // 8: timeZone
                "\"ERROR_INFO\" TEXT," + // 9: errorInfo
                "\"DEVICE_IP\" TEXT," + // 10: deviceIp
                "\"PARAM_INFO\" TEXT," + // 11: paramInfo
                "\"EXTEND_INFO_ID\" INTEGER);"); // 12: extendInfoId
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"ERROR_LOG_INFO\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, ErrorLogInfo entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String mt4Id = entity.getMt4Id();
        if (mt4Id != null) {
            stmt.bindString(2, mt4Id);
        }
 
        String serverId = entity.getServerId();
        if (serverId != null) {
            stmt.bindString(3, serverId);
        }
 
        String logType = entity.getLogType();
        if (logType != null) {
            stmt.bindString(4, logType);
        }
 
        String host = entity.getHost();
        if (host != null) {
            stmt.bindString(5, host);
        }
 
        String reqPath = entity.getReqPath();
        if (reqPath != null) {
            stmt.bindString(6, reqPath);
        }
 
        String deviceNet = entity.getDeviceNet();
        if (deviceNet != null) {
            stmt.bindString(7, deviceNet);
        }
 
        String reqTime = entity.getReqTime();
        if (reqTime != null) {
            stmt.bindString(8, reqTime);
        }
 
        String timeZone = entity.getTimeZone();
        if (timeZone != null) {
            stmt.bindString(9, timeZone);
        }
 
        String errorInfo = entity.getErrorInfo();
        if (errorInfo != null) {
            stmt.bindString(10, errorInfo);
        }
 
        String deviceIp = entity.getDeviceIp();
        if (deviceIp != null) {
            stmt.bindString(11, deviceIp);
        }
 
        String paramInfo = entity.getParamInfo();
        if (paramInfo != null) {
            stmt.bindString(12, paramInfo);
        }
 
        Long extendInfoId = entity.getExtendInfoId();
        if (extendInfoId != null) {
            stmt.bindLong(13, extendInfoId);
        }
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, ErrorLogInfo entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String mt4Id = entity.getMt4Id();
        if (mt4Id != null) {
            stmt.bindString(2, mt4Id);
        }
 
        String serverId = entity.getServerId();
        if (serverId != null) {
            stmt.bindString(3, serverId);
        }
 
        String logType = entity.getLogType();
        if (logType != null) {
            stmt.bindString(4, logType);
        }
 
        String host = entity.getHost();
        if (host != null) {
            stmt.bindString(5, host);
        }
 
        String reqPath = entity.getReqPath();
        if (reqPath != null) {
            stmt.bindString(6, reqPath);
        }
 
        String deviceNet = entity.getDeviceNet();
        if (deviceNet != null) {
            stmt.bindString(7, deviceNet);
        }
 
        String reqTime = entity.getReqTime();
        if (reqTime != null) {
            stmt.bindString(8, reqTime);
        }
 
        String timeZone = entity.getTimeZone();
        if (timeZone != null) {
            stmt.bindString(9, timeZone);
        }
 
        String errorInfo = entity.getErrorInfo();
        if (errorInfo != null) {
            stmt.bindString(10, errorInfo);
        }
 
        String deviceIp = entity.getDeviceIp();
        if (deviceIp != null) {
            stmt.bindString(11, deviceIp);
        }
 
        String paramInfo = entity.getParamInfo();
        if (paramInfo != null) {
            stmt.bindString(12, paramInfo);
        }
 
        Long extendInfoId = entity.getExtendInfoId();
        if (extendInfoId != null) {
            stmt.bindLong(13, extendInfoId);
        }
    }

    @Override
    protected final void attachEntity(ErrorLogInfo entity) {
        super.attachEntity(entity);
        entity.__setDaoSession(daoSession);
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public ErrorLogInfo readEntity(Cursor cursor, int offset) {
        ErrorLogInfo entity = new ErrorLogInfo( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // mt4Id
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // serverId
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // logType
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // host
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // reqPath
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // deviceNet
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7), // reqTime
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8), // timeZone
            cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9), // errorInfo
            cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10), // deviceIp
            cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11), // paramInfo
            cursor.isNull(offset + 12) ? null : cursor.getLong(offset + 12) // extendInfoId
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, ErrorLogInfo entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setMt4Id(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setServerId(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setLogType(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setHost(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setReqPath(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setDeviceNet(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setReqTime(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
        entity.setTimeZone(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
        entity.setErrorInfo(cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9));
        entity.setDeviceIp(cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10));
        entity.setParamInfo(cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11));
        entity.setExtendInfoId(cursor.isNull(offset + 12) ? null : cursor.getLong(offset + 12));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(ErrorLogInfo entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(ErrorLogInfo entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(ErrorLogInfo entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
    private String selectDeep;

    protected String getSelectDeep() {
        if (selectDeep == null) {
            StringBuilder builder = new StringBuilder("SELECT ");
            SqlUtils.appendColumns(builder, "T", getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T0", daoSession.getExtendInfoDao().getAllColumns());
            builder.append(" FROM ERROR_LOG_INFO T");
            builder.append(" LEFT JOIN EXTEND_INFO T0 ON T.\"EXTEND_INFO_ID\"=T0.\"_id\"");
            builder.append(' ');
            selectDeep = builder.toString();
        }
        return selectDeep;
    }
    
    protected ErrorLogInfo loadCurrentDeep(Cursor cursor, boolean lock) {
        ErrorLogInfo entity = loadCurrent(cursor, 0, lock);
        int offset = getAllColumns().length;

        ExtendInfo extendInfo = loadCurrentOther(daoSession.getExtendInfoDao(), cursor, offset);
        entity.setExtendInfo(extendInfo);

        return entity;    
    }

    public ErrorLogInfo loadDeep(Long key) {
        assertSinglePk();
        if (key == null) {
            return null;
        }

        StringBuilder builder = new StringBuilder(getSelectDeep());
        builder.append("WHERE ");
        SqlUtils.appendColumnsEqValue(builder, "T", getPkColumns());
        String sql = builder.toString();
        
        String[] keyArray = new String[] { key.toString() };
        Cursor cursor = db.rawQuery(sql, keyArray);
        
        try {
            boolean available = cursor.moveToFirst();
            if (!available) {
                return null;
            } else if (!cursor.isLast()) {
                throw new IllegalStateException("Expected unique result, but count was " + cursor.getCount());
            }
            return loadCurrentDeep(cursor, true);
        } finally {
            cursor.close();
        }
    }
    
    /** Reads all available rows from the given cursor and returns a list of new ImageTO objects. */
    public List<ErrorLogInfo> loadAllDeepFromCursor(Cursor cursor) {
        int count = cursor.getCount();
        List<ErrorLogInfo> list = new ArrayList<ErrorLogInfo>(count);
        
        if (cursor.moveToFirst()) {
            if (identityScope != null) {
                identityScope.lock();
                identityScope.reserveRoom(count);
            }
            try {
                do {
                    list.add(loadCurrentDeep(cursor, false));
                } while (cursor.moveToNext());
            } finally {
                if (identityScope != null) {
                    identityScope.unlock();
                }
            }
        }
        return list;
    }
    
    protected List<ErrorLogInfo> loadDeepAllAndCloseCursor(Cursor cursor) {
        try {
            return loadAllDeepFromCursor(cursor);
        } finally {
            cursor.close();
        }
    }
    

    /** A raw-style query where you can pass any WHERE clause and arguments. */
    public List<ErrorLogInfo> queryDeep(String where, String... selectionArg) {
        Cursor cursor = db.rawQuery(getSelectDeep() + where, selectionArg);
        return loadDeepAllAndCloseCursor(cursor);
    }
 
}
