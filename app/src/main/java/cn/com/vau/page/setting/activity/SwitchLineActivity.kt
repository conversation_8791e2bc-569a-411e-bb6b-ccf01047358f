package cn.com.vau.page.setting.activity

import android.content.Context
import android.content.Intent
import cn.com.vau.R
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.mvvm.base.BaseMvvmBindingActivity
import cn.com.vau.common.storage.SpManager
import cn.com.vau.databinding.ActivitySwitchLineBinding
import cn.com.vau.databinding.ItemSingleEdittextSelectBinding
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.clickNoRepeat
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

class SwitchLineActivity : BaseMvvmBindingActivity<ActivitySwitchLineBinding>() {

    private val type by lazy { intent.getStringExtra(KEY_TYPE) ?: TYPE_NO_TRADE }

    private val adapter by lazy { SwitchLineAdapter(getList().toMutableList(), getIndex()) }

    private val itemEdittextSelectBinding by lazy { ItemSingleEdittextSelectBinding.inflate(layoutInflater) }

    override fun initView() {
        if (HttpUrl.official) {
            finish()
            return
        }
        // 切换按钮
        mBinding.tvNext.setOnClickListener {
            setIndex(adapter.selectedIndex)
            if (adapter.selectedIndex == -1) {
                SpManager.putCustomH5Url(itemEdittextSelectBinding.etContent.text.toString())
            }
            ToastUtil.showToast("切换成功\n杀死APP重启后生效！")
            finish()
        }

        if (type == TYPE_H5) {
            adapter.addFooterView(itemEdittextSelectBinding.root)
            itemEdittextSelectBinding.etContent.setText(SpManager.getCustomH5Url())
            updateFooterView()
            itemEdittextSelectBinding.ivSelected.clickNoRepeat {
                adapter.selectedIndex = -1
                adapter.notifyDataSetChanged()
                updateFooterView()
            }
        }

        // 设置适配器及选中项
        mBinding.mRecyclerView.adapter = adapter
        adapter.setOnItemClickListener { _, _, position ->
            adapter.selectedIndex = position
            adapter.notifyDataSetChanged()
            updateFooterView()
        }
    }

    private fun updateFooterView() {
        itemEdittextSelectBinding.ivSelected.setImageResource(
            if (adapter.selectedIndex == -1)
                R.drawable.icon2_cb_tick_circle_c15b374
            else
                R.drawable.draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14
        )
    }

    private fun getList() = when (type) {
        TYPE_NO_TRADE -> HttpUrl.testNoTradeUrls
        TYPE_TRADE -> HttpUrl.testTradeUrls
        TYPE_ST_TRADE -> HttpUrl.testStTradeUrls
        TYPE_WS -> HttpUrl.testWsUrls
        TYPE_ST_WS -> HttpUrl.testStWsUrls
        else -> HttpUrl.testH5Urls
    }

    private fun getIndex() = SpManager.getSwitchHttpUrlIndex(type, 0)

    private fun setIndex(index: Int) {
        SpManager.putSwitchHttpUrlIndex(type, index)
    }

    companion object {

        private const val KEY_TYPE = "type"

        const val TYPE_NO_TRADE = "no_trade"
        const val TYPE_TRADE = "trade"
        const val TYPE_ST_TRADE = "st_trade"
        const val TYPE_WS = "ws"
        const val TYPE_ST_WS = "st_ws"
        const val TYPE_H5 = "h5"

        fun open(context: Context, type: String) {
            val intent = Intent(context, SwitchLineActivity::class.java)
            intent.putExtra(KEY_TYPE, type)
            context.startActivity(intent)
        }
    }
}

class SwitchLineAdapter(var dataList: MutableList<String>, var selectedIndex: Int = 0) : BaseQuickAdapter<String, BaseViewHolder>(R.layout.item_single_text_select, dataList) {

    override fun convert(holder: BaseViewHolder, item: String) {
        holder.setText(R.id.tvContent, item)
            .setImageResource(
                R.id.ivSelected,
                if (holder.layoutPosition == selectedIndex) R.drawable.icon2_cb_tick_circle_c15b374
                else R.drawable.draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14
            )
    }

}