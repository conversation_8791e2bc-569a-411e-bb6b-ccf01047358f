package cn.com.vau.page.user.login.model

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.*
import cn.com.vau.data.account.*
import cn.com.vau.page.user.login.presenter.VerificationContract
import io.reactivex.disposables.Disposable

class VerificationModel : VerificationContract.Model {

    override fun getBindingTelSMSApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<ForgetPwdVerificationCodeBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().getTelSMSApi(map), baseObserver)
        return baseObserver.disposable
    }

    override fun pwdLoginApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<LoginBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().loginNewApi(map), baseObserver)
        return baseObserver.disposable
    }

    override fun bindUserApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<LoginBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().thirdpartyBindApi(map), baseObserver)
        return baseObserver.disposable
    }

    override fun updateTelApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<ChangeUserInfoSuccessBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().usersetUpTelApi(map), baseObserver)
        return baseObserver.disposable
    }

    override fun checkVerificationCodeApi(map: java.util.HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>): Disposable {
        HttpUtils.loadData(
            RetrofitHelper.getHttpService().smsValidateSmsForgetPwdCodeApi(map),
            baseObserver
        )
        return baseObserver.disposable
    }

    override fun goEditPwdApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<ChangeUserInfoSuccessBean>): Disposable {
        HttpUtils.loadData(
            RetrofitHelper.getHttpService().forgetpwdForgetUpPwdApi(map),
            baseObserver
        )
        return baseObserver.disposable
    }

    override fun getVerificationCodeApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<ForgetPwdVerificationCodeBean>): Disposable {
        HttpUtils.loadData(
            RetrofitHelper.getHttpService().forgetpwdGetVerificationCodeApi(map),
            baseObserver
        )
        return baseObserver.disposable
    }

    override fun insertFundPWDApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<BaseBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().usersetUpfundpwdApi(map), baseObserver)
        return baseObserver.disposable
    }

    override fun forgotFundPWDApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<BaseBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().usersetForgetSafePwdApi(map), baseObserver)
        return baseObserver.disposable
    }

    override fun withdrawal(map: HashMap<String, Any>, baseObserver: BaseObserver<DataObjStringBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().fundWithDraw(map), baseObserver)
        return baseObserver.disposable
    }
}