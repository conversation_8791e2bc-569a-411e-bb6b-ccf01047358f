package cn.com.vau.page.user.login

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.navigation.fragment.NavHostFragment
import cn.com.vau.R
import cn.com.vau.common.base.fragment.BaseFrameFragment
import cn.com.vau.common.constants.Constants
import cn.com.vau.data.account.SelectCountryNumberObjDetail
import cn.com.vau.databinding.FragmentForgetPwdFirstBinding
import cn.com.vau.page.common.selectArea.SelectAreaCodeActivity
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.user.forgotPwdFirst.ForgetPwdFirstContract
import cn.com.vau.page.user.forgotPwdFirst.ForgetPwdFirstModel
import cn.com.vau.page.user.forgotPwdFirst.ForgotPwdFirstPresenter
import cn.com.vau.util.CaptchaUtil
import cn.com.vau.util.LogUtil
import cn.com.vau.util.TabType
import cn.com.vau.util.setVp
import com.netease.nis.captcha.Captcha
import com.netease.nis.captcha.CaptchaListener

@SuppressLint("SetTextI18n")
class ForgetPwdFirstFragment : BaseFrameFragment<ForgotPwdFirstPresenter, ForgetPwdFirstModel>(), ForgetPwdFirstContract.View {

    private val mBinding: FragmentForgetPwdFirstBinding by lazy { FragmentForgetPwdFirstBinding.inflate(layoutInflater) }

    var username: String? = null
    var captcha: Captcha? = null

    private var tableLayoutInitFinish = false
    private val typeAdapter: ForgetPwdTypeAdapter by lazy { ForgetPwdTypeAdapter() }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = mBinding.root

    override fun initParam() {
        super.initParam()
        arguments?.let {
            mPresenter.isShowEmail = it.getBoolean("isShowEmail")
            mPresenter.handleType = it.getInt(Constants.HANDLE_TYPE, 0)
            username = it.getString("username")
            if (it.containsKey("areaCodeData")) {
                mPresenter.areaCodeData = it.getSerializable("areaCodeData") as SelectCountryNumberObjDetail?
            } else {
                mPresenter.initCode()
            }
        }
    }

    @SuppressLint("ObsoleteSdkInt")
    override fun initView() {
        super.initView()
        val titleList = mutableListOf(
            getString(R.string.phone_number),
            getString(R.string.email)
        )
        mBinding.mViewPager2.adapter = typeAdapter
        mBinding.mTabLayout.setVp(mBinding.mViewPager2, titleList, TabType.LINE_INDICATOR)
        mBinding.mViewPager2.setCurrentItem(if (mPresenter.isShowEmail) 1 else 0, false)

        tableLayoutInitFinish = true

        showTel()
    }

    override fun initListener() {
        super.initListener()
        mBinding.mHeaderBar.run {
            setStartBackIconClickListener {
                goBack()
            }
            // 客服
            setEndIconClickListener {
                openActivity(HelpCenterActivity::class.java)
            }
        }

        // 回退事件
        requireActivity().onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                goBack()
            }
        })

        typeAdapter.areaCodeClick {
            val bundle = Bundle()
            bundle.putString("selectAreaCode", mPresenter.areaCodeData?.countryNum ?: Constants.defaultCountryNum)
            openActivity(SelectAreaCodeActivity::class.java, bundle, Constants.SELECT_AREA)
        }
        typeAdapter.smsClick {
            mPresenter.smsSendType = VerificationActivity.TYPE_SEND_SMS
            mPresenter.getVerificationCodeApi(typeAdapter.mobile.toString().trim(), "")
        }
        typeAdapter.emailClick {
            mPresenter.smsSendType = VerificationActivity.TYPE_SEND_EMAIL
            mPresenter.getVerificationCodeApi(typeAdapter.email.toString().trim(), "")
        }
        typeAdapter.whatsAppClick {
            mPresenter.smsSendType = VerificationActivity.TYPE_SEND_WA
            mPresenter.getVerificationCodeApi(typeAdapter.mobile.toString().trim(), "")

        }
    }

    private fun initCaptcha() {
        //易盾
        val loginCaptchaListener = object : CaptchaListener {
            override fun onReady() {}
            override fun onValidate(result: String, validate: String, msg: String) {
                if (!TextUtils.isEmpty(validate)) {
                    mPresenter.getVerificationCodeApi(
                        if (mPresenter.smsSendType == VerificationActivity.TYPE_SEND_EMAIL)
                            typeAdapter.email.toString().trim()
                        else
                            typeAdapter.mobile.toString().trim(), validate
                    )
                }
            }

            //建议直接打印错误码，便于排查问题
            override fun onError(code: Int, msg: String) {
            }

            override fun onClose(closeType: Captcha.CloseType) {
                if (closeType == Captcha.CloseType.VERIFY_SUCCESS_CLOSE) {
                    Handler(Looper.getMainLooper()).post {
                        //成功 + 关闭
                    }
                }
            }
        }

        captcha = CaptchaUtil.getCaptcha(requireContext(), loginCaptchaListener)
    }

    fun goBack() {
        if (arguments?.getInt("isFrom", 0) == 1)
            ac.finish()
        else {
            try {
                NavHostFragment.findNavController(this).popBackStack()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override fun goForgetSecond() {
        if (!isAdded || !isVisible || activity == null) {
            LogUtil.e("ForgetPwdFirstFragment", "Fragment not in a valid state, cannot navigate.")
            return
        }
        val bundle = Bundle()
        bundle.putString("email", typeAdapter.email.toString().trim())
        bundle.putString("txId", mPresenter.txId)
        bundle.putString("mobile", typeAdapter.mobile.toString().trim())
        bundle.putString("smsSendType", mPresenter.smsSendType)
        bundle.putString("countryCode", mPresenter.areaCodeData?.countryCode)
        bundle.putString("code", mPresenter.areaCodeData?.countryNum)
        bundle.putInt(Constants.HANDLE_TYPE, mPresenter.handleType)
        NavHostFragment.findNavController(this).navigate(R.id.actionForgetFirstPwdtoSecond, bundle)
    }

    // 选择了区号数据结果
    @SuppressLint("SetTextI18n")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (resultCode) {
            Constants.SELECT_AREA -> {
                val areaData = data?.extras?.get(Constants.SELECT_AREA_CODE) as SelectCountryNumberObjDetail
                mPresenter.areaCodeData = areaData
                typeAdapter.setAreaCodeStr("+${areaData.countryNum}")
                mPresenter.setSelectAreaData(areaData)
            }
        }
    }

    override fun showTel() {
        typeAdapter.setAreaCodeStr("+${mPresenter.areaCodeData?.countryNum}")
        if (mPresenter.isShowEmail) {
            typeAdapter.email = username ?: ""
        } else {
            typeAdapter.mobile = username ?: ""
        }
        typeAdapter.notifyItemRangeChanged(0, 2)
    }

    override fun showCaptcha() {
        initCaptcha()
        captcha?.validate()
    }

    override fun onDestroy() {
        super.onDestroy()
        if (captcha != null) captcha?.destroy()
    }
}