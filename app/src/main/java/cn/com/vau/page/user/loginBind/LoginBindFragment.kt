package cn.com.vau.page.user.loginBind

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.widget.doAfterTextChanged
import androidx.navigation.fragment.NavHostFragment
import cn.com.vau.R
import cn.com.vau.common.base.fragment.BaseFrameFragment
import cn.com.vau.common.constants.Constants
import cn.com.vau.data.account.SelectCountryNumberObjDetail
import cn.com.vau.databinding.FragmentLoginBindBinding
import cn.com.vau.page.common.selectArea.SelectAreaCodeActivity
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.user.login.VerificationActivity
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.CaptchaUtil
import cn.com.vau.util.LogUtil
import com.netease.nis.captcha.Captcha
import com.netease.nis.captcha.CaptchaListener

/**
 * 绑定手机号
 */
@SuppressLint("SetTextI18n")
class LoginBindFragment : BaseFrameFragment<LoginBindPresenter, LoginBindModel>(), LoginBindContract.View {

    private val mBinding: FragmentLoginBindBinding by lazy { FragmentLoginBindBinding.inflate(layoutInflater) }

    var captcha: Captcha? = null

    private val color_c731e1e1e_c61ffffff by lazy {
        AttrResourceUtil.getColor(requireActivity(), R.attr.color_c731e1e1e_c61ffffff)
    }

    private val color_cffffff_c1e1e1e by lazy {
        AttrResourceUtil.getColor(requireActivity(), R.attr.color_cebffffff_c1e1e1e)
    }

    override fun initParam() {
        super.initParam()
        arguments?.let {
            mPresenter.email = it.getString(Constants.USER_EMAIL) ?: ""
            mPresenter.pwd = it.getString(Constants.USER_PWD) ?: ""
            mPresenter.handleType = it.getInt(Constants.HANDLE_TYPE, 0)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View = mBinding.root

    @SuppressLint("SetTextI18n")
    override fun initView() {
        super.initView()

        mBinding.etMobile.setHint("${getString(R.string.phone_number)}*")

        mBinding.tvLoginType.text = getString(R.string.hi_x, "${mPresenter.email}")

        checkTitleTextViewShow()

        mPresenter.getLocalAreaInfo()

        initNextView()
    }

    override fun initListener() {
        mBinding.mHeaderBar.setStartBackIconClickListener{
            back()
        }.setEndIconClickListener{
            openActivity(HelpCenterActivity::class.java)
        }
        mBinding.tvSendEms.setOnClickListener(this)
        mBinding.tvAreaCode.setOnClickListener(this)
        mBinding.llWhatsApp.setOnClickListener(this)

        mBinding.etMobile.doAfterTextChanged {
            initNextView()
        }
    }

    private fun initCaptcha() {
        //易盾
        val loginCaptchaListener = object : CaptchaListener {
            override fun onReady() {}
            override fun onValidate(result: String, validate: String, msg: String) {
                if (!TextUtils.isEmpty(validate)) {
                    mPresenter.getCodeApi(
                        mBinding.etMobile.text.toString().trim().ifEmpty { mPresenter.areaCodeData?.mobile }, validate
                    )
                }
            }

            //建议直接打印错误码，便于排查问题
            override fun onError(code: Int, msg: String) {
            }

            override fun onClose(closeType: Captcha.CloseType) {
                if (closeType == Captcha.CloseType.VERIFY_SUCCESS_CLOSE) {
                    Handler(Looper.getMainLooper()).post {
                        //成功 + 关闭
                    }
                }
            }
        }

        captcha = CaptchaUtil.getCaptcha(requireContext(), loginCaptchaListener)
    }

    private var isNext = false

    private fun initNextView() {
        mBinding.tvSendEms.setBackgroundResource(
            if (mBinding.etMobile.length() > 0)
                R.drawable.draw_shape_c1e1e1e_cebffffff_r100
            else
                R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100
        )
        mBinding.tvSendEms.setTextColor(if (mBinding.etMobile.length() > 0) color_cffffff_c1e1e1e else color_c731e1e1e_c61ffffff)
        mBinding.llWhatsApp.setBackgroundResource(
            if (mBinding.etMobile.length() > 0)
                R.drawable.shape_cbf25d366_r100
            else
                R.drawable.shape_c3325d366_r100
        )

        isNext = mBinding.etMobile.length() > 0
    }

    private fun checkTitleTextViewShow() {
    }

    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.tvSendEms -> {
                if (!isNext) return
                mPresenter.smsSendType = VerificationActivity.TYPE_SEND_SMS
                mPresenter.phoneIsUsedApi(
                    mBinding.etMobile.text.toString().trim()
                )
            }

            R.id.llWhatsApp -> {
                if (!isNext) return
                mPresenter.smsSendType = VerificationActivity.TYPE_SEND_WA
                mPresenter.phoneIsUsedApi(
                    mBinding.etMobile.text.toString().trim()
                )
            }

            R.id.tvAreaCode -> {
                val bundle = Bundle()
                bundle.putString("selectAreaCode", mPresenter.areaCodeData?.countryNum ?: Constants.defaultCountryNum)
                openActivity(SelectAreaCodeActivity::class.java, bundle, Constants.SELECT_AREA)
            }
        }
    }

    override fun back() {
        if (!NavHostFragment.findNavController(this).popBackStack()) activity?.finish()
    }

    override fun goSecond() {
        if (!isAdded || !isVisible || activity == null) {
            LogUtil.e("LoginBindFragment", "Fragment not in a valid state, cannot navigate.")
            return
        }
        mPresenter.areaCodeData?.mobile = mBinding.etMobile.text.toString().trim()
        mPresenter.areaCodeData?.email = mPresenter.email
        mPresenter.areaCodeData?.pwd = mPresenter.pwd
        mPresenter.areaCodeData?.handleType = mPresenter.handleType
        val bundle = Bundle()
        bundle.putSerializable("bind_data_bean", mPresenter.areaCodeData)
        bundle.putString(VerificationActivity.KEY_SEND_TYPE, mPresenter.smsSendType)
        NavHostFragment.findNavController(this).navigate(R.id.action_bind_first_to_second, bundle)
    }

    override fun showCaptcha() {
        initCaptcha()
        captcha?.validate()
    }

    /**
     * 选择了区号数据结果
     */
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (resultCode) {
            Constants.SELECT_AREA -> {
                val areaData =
                    data?.extras?.get(Constants.SELECT_AREA_CODE) as SelectCountryNumberObjDetail
                mPresenter.setSelectAreaData(areaData)
                mBinding.tvAreaCode.text = "+${mPresenter.areaCodeData?.countryNum ?: Constants.defaultCountryNum} "
            }
        }
    }

    @SuppressLint("SetTextI18n")
    override fun showLocalAreaInfo() {
        mBinding.tvAreaCode.text = "+${mPresenter.areaCodeData?.countryNum} "
    }

    companion object {
        @JvmStatic
        fun newInstance() =
            LoginBindFragment()
    }
}