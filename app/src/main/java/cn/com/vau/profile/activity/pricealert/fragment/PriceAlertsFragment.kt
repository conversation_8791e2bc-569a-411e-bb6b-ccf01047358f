package cn.com.vau.profile.activity.pricealert.fragment

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.utils.SDKIntervalUtil
import cn.com.vau.common.view.DividerItemDecoration
import cn.com.vau.databinding.FragmentRecyclerviewBinding
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.profile.activity.pricealert.activity.CreatePriceAlertActivity
import cn.com.vau.profile.activity.pricealert.adapter.PriceAlertsManagerAdapter
import cn.com.vau.profile.activity.pricealert.viewmodel.PriceAlertsManageViewModel
import cn.com.vau.profile.activity.pricealert.viewmodel.PriceAlertsManageViewModel.Companion.ADAPTER_EDIT
import cn.com.vau.profile.activity.pricealert.viewmodel.PriceAlertsManageViewModel.Companion.ADAPTER_PRICE
import cn.com.vau.profile.activity.pricealert.viewmodel.PriceAlertsManageViewModel.Companion.ADAPTER_SELECT
import cn.com.vau.profile.activity.pricealert.viewmodel.PriceAlertsManageViewModel.Companion.ADAPTER_SHOW_LIST
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.dp2px
import cn.com.vau.util.ifNull
import cn.com.vau.util.json
import cn.com.vau.util.toIntCatching
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * @description:
 * @author: GG
 * @createDate: 2024 10月 08 21:02
 * @updateUser:
 * @updateDate: 2024 10月 08 21:02
 */
class PriceAlertsFragment : BaseMvvmBindingFragment<FragmentRecyclerviewBinding>(), SDKIntervalCallback {

    private val mViewModel: PriceAlertsManageViewModel by activityViewModels()

    private val index: Int by lazy { arguments?.getString(KEY_INDEX).ifNull().toIntCatching() }

    private val adapter: PriceAlertsManagerAdapter by lazy { PriceAlertsManagerAdapter() }

    override fun onCallback() {
        adapter.notifyItemRangeChanged(0, adapter.data.size, ADAPTER_PRICE)
    }

    override fun initView() {
        mBinding.mRecyclerView.adapter = adapter
        mBinding.mRecyclerView.addItemDecoration(DividerItemDecoration(8.dp2px(), dividerColor = AttrResourceUtil.getColor(context = requireContext(), R.attr.color_c0a1e1e1e_c0affffff)))
        mBinding.mRecyclerView.animation = null
    }

    override fun initData() {
        super.initData()
        val data = if (index == 0) {
            mViewModel.priceAlertListLiveData.value?.flatMap { it.list.orEmpty() }?.toMutableList()
        } else {
            mViewModel.priceAlertListLiveData.value?.getOrNull(index - 1)?.list?.toMutableList()
        }
        if (adapter.data.json != data.json)
            adapter.setList(data)
    }

    override fun initListener() {
        super.initListener()
        adapter.setChildClick { data, itemData ->
            if (adapter.isEdit) {
                adapter.notifyItemChanged(adapter.getItemPosition(data), ADAPTER_SELECT)
                mViewModel.sendEvent(ADAPTER_SELECT)
            } else {
                CreatePriceAlertActivity.open(requireContext(), productName = data.symbol, true, itemData)
            }
        }
        adapter.setSelectClick { data, itemData ->
            adapter.notifyItemChanged(adapter.getItemPosition(data), ADAPTER_SELECT)
            mViewModel.sendEvent(ADAPTER_SELECT)
        }
        adapter.setEnableClick { data, itemData ->
            mViewModel.enableAndDisablePriceWarn(data = itemData)
        }
        adapter.setOnItemChildClickListener { _, view, position ->
            when (view.id) {
                R.id.viewBg -> {
                    if (!adapter.isEdit)
                        return@setOnItemChildClickListener
                    if (adapter.data.getOrNull(position)?.list?.all { it.isSelect } == true) {
                        adapter.data.getOrNull(position)?.list?.forEach {
                            it.isSelect = false
                        }
                    } else {
                        adapter.data.getOrNull(position)?.list?.forEach {
                            it.isSelect = true
                        }
                    }
                    adapter.notifyItemChanged(position, ADAPTER_SELECT)
                    mViewModel.sendEvent(ADAPTER_SELECT)
                }

                R.id.ivExtent -> {
                    adapter.data.getOrNull(position)?.isShowList = !adapter.data.getOrNull(position)?.isShowList.ifNull()
                    adapter.notifyItemChanged(position, ADAPTER_SHOW_LIST)
                }
            }
        }
    }

    override fun createObserver() {
        super.createObserver()
        lifecycleScope.launch {
            mViewModel.eventFlow
                .collectLatest { event ->
                    when (event) {
                        ADAPTER_SELECT -> {
                            adapter.notifyItemRangeChanged(0, adapter.data.size, ADAPTER_SELECT)
                        }
                    }
                }
        }
        mViewModel.priceAlertListLiveData.observe(this) {
            if (mViewModel.oldData.json != it.json) {
                initData()
            }
        }
        mViewModel.isEditLiveData.observe(this) {
            updateEditState(it.ifNull())
        }
    }

    override fun onResume() {
        super.onResume()
        SDKIntervalUtil.instance.removeCallBack(this)
        SDKIntervalUtil.instance.addCallBack(this)
    }

    /**
     * 更新管理状态
     */
    private fun updateEditState(isEdit: Boolean) {
        adapter.isEdit = isEdit
        adapter.notifyItemRangeChanged(0, adapter.data.size, ADAPTER_EDIT)
    }

    override fun onPause() {
        super.onPause()
        SDKIntervalUtil.instance.removeCallBack(this)
    }

    companion object {

        private const val KEY_INDEX = "index"

        fun newInstance(index: String): PriceAlertsFragment {
            val args = Bundle()
            args.putString(KEY_INDEX, index)
            val fragment = PriceAlertsFragment()
            fragment.arguments = args
            return fragment
        }
    }
}