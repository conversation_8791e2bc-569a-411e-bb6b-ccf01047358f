package cn.com.vau.profile.activity.pricealert.activity

import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.databinding.ActivityPriceAlertListBinding
import cn.com.vau.profile.activity.pricealert.adapter.ProduceAlterListAdapter
import cn.com.vau.profile.activity.pricealert.viewmodel.PriceAlertsManageViewModel
import cn.com.vau.util.*
import cn.com.vau.util.widget.NoDataView

/**
 * 产品价格提醒列表
 */
class PriceAlterListActivity : BaseMvvmActivity<ActivityPriceAlertListBinding, PriceAlertsManageViewModel>() {

    private val symbol: String by lazy { intent.getStringExtra(Constants.PARAM_PRODUCT_NAME).ifNull() }

    private val alterListAdapter: ProduceAlterListAdapter by lazy {
        ProduceAlterListAdapter().apply {
            setEmptyView(NoDataView(this@PriceAlterListActivity).apply {
                setHintMessage(getString(R.string.no_alert))
            })
        }
    }

    override fun initView() {
        mBinding.mHeaderBar.setTitleText(symbol)
        mBinding.recyclerview.adapter = alterListAdapter
    }

    override fun initListener() {
        super.initListener()
        mBinding.mHeaderBar.setEndTextClickListener {
            openActivity(PriceAlertsManageActivity::class.java)
        }
        alterListAdapter.setSwitchListener {
            mViewModel.enableAndDisablePriceWarn(it)
        }
        alterListAdapter.setNbOnItemClickListener { _, _, position ->
            CreatePriceAlertActivity.open(this, productName = symbol, true, alterListAdapter.data.getOrNull(position))
        }
        mBinding.tvNext.clickNoRepeat {
            CreatePriceAlertActivity.open(this, productName = symbol, false)
        }
    }

    override fun onResume() {
        super.onResume()
        mViewModel.getPriceWarn(symbol = symbol)
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.priceAlertListLiveData.observe(this) {
            if (it == null || it.getOrNull(0)?.list == null) {
                alterListAdapter.setList(null)
                return@observe
            }
            alterListAdapter.setList(it.getOrNull(0)?.list?.getOrNull(0)?.list)
        }
    }
}