package cn.com.vau.profile.activity.pricealert.adapter

import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import androidx.transition.ChangeBounds
import androidx.transition.TransitionManager
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.data.pricealtert.ProduceAlterData
import cn.com.vau.data.pricealtert.ProduceAlterSymbolListData
import cn.com.vau.profile.activity.pricealert.viewmodel.PriceAlertsManageViewModel.Companion.ADAPTER_EDIT
import cn.com.vau.profile.activity.pricealert.viewmodel.PriceAlertsManageViewModel.Companion.ADAPTER_PRICE
import cn.com.vau.profile.activity.pricealert.viewmodel.PriceAlertsManageViewModel.Companion.ADAPTER_SELECT
import cn.com.vau.profile.activity.pricealert.viewmodel.PriceAlertsManageViewModel.Companion.ADAPTER_SHOW_LIST
import cn.com.vau.util.ifNull
import cn.com.vau.util.mathCompTo
import cn.com.vau.util.setTextColorDiff
import cn.com.vau.util.setTextDiff
import cn.com.vau.util.toDoubleCatching
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

/**
 * @description:
 * @author: GG
 * @createDate: 2024 10月 10 16:03
 * @updateUser:
 * @updateDate: 2024 10月 10 16:03
 */
class PriceAlertsManagerAdapter(var isEdit: Boolean = false) : BaseQuickAdapter<ProduceAlterSymbolListData, BaseViewHolder>(R.layout.item_recycler_price_alerts_manage) {

    init {
        addChildClickViewIds(R.id.viewBg, R.id.ivExtent)
    }

    private var enableClick: ((ProduceAlterSymbolListData, ProduceAlterData?) -> Unit)? = null
    private var selectClick: ((ProduceAlterSymbolListData, ProduceAlterData?) -> Unit)? = null
    private var childClick: ((ProduceAlterSymbolListData, ProduceAlterData?) -> Unit)? = null
    private val c00c79c by lazy { ContextCompat.getColor(context, R.color.c00c79c) }
    private val cf44040 by lazy { ContextCompat.getColor(context, R.color.cf44040) }

    override fun convert(holder: BaseViewHolder, item: ProduceAlterSymbolListData) {
        holder.setText(R.id.tvProdName, item.symbol)
            .setText(R.id.tvBid, item.bid)
            .setText(R.id.tvAsk, item.ask)
            .setText(R.id.tvRate, item.rate + "%")
            .setGone(R.id.ivSelect, !isEdit)
            .setGone(R.id.viewLine, !item.isShowList)
            .setGone(R.id.rvList, !item.isShowList)
            .setImageResource(R.id.ivExtent, if (item.isShowList) R.drawable.draw_bitmap2_arrow_top10x10_c1e1e1e_cebffffff else R.drawable.draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff)
            .setTextColor(R.id.tvRate, if (item.rate.toDoubleCatching() > 0.0) c00c79c else cf44040)
            .setImageResource(R.id.ivSelect, if (item.list?.all { it.isSelect == true } == true) R.drawable.icon2_cb_tick_circle_c15b374 else R.drawable.draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14)
        val rvList = holder.getViewOrNull<RecyclerView>(R.id.rvList)
        val adapter = ProduceAlterListAdapter(isEdit)
        rvList?.adapter = adapter
        adapter.setList(item.list)
        adapter.setOnItemChildClickListener { _, view, position ->
            when (view.id) {
                R.id.ivSelect -> {
                    adapter.data.getOrNull(position)?.isSelect = !adapter.data.getOrNull(position)?.isSelect.ifNull()
                    adapter.notifyItemChanged(position, ADAPTER_SELECT)
                    selectClick?.invoke(item, item.list?.getOrNull(position))
                }
            }
        }
        adapter.setSwitchListener {
            enableClick?.invoke(item, it)
        }
        adapter.setOnItemClickListener { _, _, position ->
            if (isEdit) {
                adapter.data.getOrNull(position)?.isSelect = !adapter.data.getOrNull(position)?.isSelect.ifNull()
                adapter.notifyItemChanged(position, ADAPTER_SELECT)
            }
            childClick?.invoke(item, item.list?.getOrNull(position))
        }
    }

    override fun convert(holder: BaseViewHolder, item: ProduceAlterSymbolListData, payloads: List<Any>) {
        if (payloads.isNotEmpty() && payloads.getOrNull(0) == ADAPTER_PRICE) {
            val tvBid = holder.getViewOrNull<AppCompatTextView>(R.id.tvBid)
            val tvRate = holder.getViewOrNull<AppCompatTextView>(R.id.tvRate)
            val tvAsk = holder.getViewOrNull<AppCompatTextView>(R.id.tvAsk)
            tvBid?.setTextDiff(item.bid.ifNull())
            tvRate?.setTextDiff(
                when {
                    item.rate == Constants.DOUBLE_LINE -> {
                        "${Constants.DOUBLE_LINE}%"
                    }

                    item.rate.mathCompTo("0") == 1 -> {
                        "+" + item.rate.ifNull() + "%"
                    }

                    else -> {
                        item.rate.ifNull() + "%"
                    }
                }
            )
            if (item.rate != "-")
                tvRate?.setTextColorDiff(if (item.rate.toDoubleCatching() > 0.0) c00c79c else cf44040)
            tvAsk?.setTextDiff(item.ask.ifNull())
        } else if (payloads.isNotEmpty() && payloads.getOrNull(0) == ADAPTER_EDIT) {
            holder.setGone(R.id.ivSelect, !isEdit)
                .setVisible(R.id.viewLine, isEdit)
            val rvList = holder.getViewOrNull<RecyclerView>(R.id.rvList)
            if (rvList?.adapter is ProduceAlterListAdapter) {
                val adapter = rvList.adapter as ProduceAlterListAdapter
                adapter.isEdit = isEdit
                adapter.notifyItemRangeChanged(0, adapter.data.size, ADAPTER_EDIT)
            }
        } else if (payloads.isNotEmpty() && payloads.getOrNull(0) == ADAPTER_SELECT) {
            holder.setImageResource(R.id.ivSelect, if (item.list?.all { it.isSelect == true } == true) R.drawable.icon2_cb_tick_circle_c15b374 else R.drawable.draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14)

            val rvList = holder.getViewOrNull<RecyclerView>(R.id.rvList)
            if (rvList?.adapter is ProduceAlterListAdapter) {
                val adapter = rvList.adapter as ProduceAlterListAdapter
                adapter.notifyItemRangeChanged(0, adapter.data.size, ADAPTER_SELECT)
            }
        } else if (payloads.isNotEmpty() && payloads.getOrNull(0) == ADAPTER_SHOW_LIST) {
            holder.getViewOrNull<ConstraintLayout>(R.id.rootView)?.let {
                TransitionManager.beginDelayedTransition(it, ChangeBounds())
            }
            holder.setGone(R.id.rvList, !item.isShowList)
                .setImageResource(R.id.ivExtent, if (item.isShowList) R.drawable.draw_bitmap2_arrow_top10x10_c1e1e1e_cebffffff else R.drawable.draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff)
        } else {
            convert(holder, item)
        }
    }

    /**
     * 设置嵌套的RecyclerView内的开关监听
     */
    fun setEnableClick(block: ((ProduceAlterSymbolListData, ProduceAlterData?) -> Unit)?) {
        enableClick = block
    }

    /**
     * 设置嵌套的RecyclerView内的 选中监听
     */
    fun setSelectClick(block: ((ProduceAlterSymbolListData, ProduceAlterData?) -> Unit)?) {
        selectClick = block
    }

    /**
     * 设置嵌套的RecyclerView内的点击事件监听
     */
    fun setChildClick(block: ((ProduceAlterSymbolListData, ProduceAlterData?) -> Unit)?) {
        childClick = block
    }
}