package cn.com.vau.profile

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.*
import android.widget.ImageView
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.StringRes
import androidx.core.content.ContextCompat
import androidx.core.view.*
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.*
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmFragment
import cn.com.vau.common.utils.VAUStartUtil
import cn.com.vau.common.view.*
import cn.com.vau.data.account.*
import cn.com.vau.data.strategy.StStrategySignalProviderCenterBean
import cn.com.vau.databinding.*
import cn.com.vau.page.coupon.CouponManagerActivity
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.html.*
import cn.com.vau.page.login.activity.SignUpActivity
import cn.com.vau.page.security.SecurityActivity
import cn.com.vau.page.setting.activity.SettingActivity
import cn.com.vau.page.user.accountManager.AccountManagerActivity
import cn.com.vau.page.user.loginPwd.LoginPwdActivity
import cn.com.vau.page.user.openAccountFifth.OpenFifthAddressSelectActivity
import cn.com.vau.page.user.sumsub.SumSubJumpHelper
import cn.com.vau.profile.activity.authentication.AuthenticationActivity
import cn.com.vau.profile.activity.kycAuth.KycAuthActivity
import cn.com.vau.profile.activity.manageFunds.FundsActivity
import cn.com.vau.profile.activity.pricealert.activity.PriceAlertsManageActivity
import cn.com.vau.profile.adapter.*
import cn.com.vau.profile.stfund.StFundsActivity
import cn.com.vau.profile.viewmodel.ProfileViewModel
import cn.com.vau.signals.stsignal.activity.*
import cn.com.vau.signals.stsignal.center.activity.StSignalCenterActivity
import cn.com.vau.util.*
import cn.com.vau.util.opt.PerfTraceUtil
import cn.com.vau.util.tracking.*
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.entity.node.BaseNode
import com.youth.banner.adapter.BannerImageAdapter
import com.youth.banner.holder.BannerImageHolder
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.*
import org.json.JSONObject

/**
 * Created by roy on 2018/10/16.
 * 我的
 */
class ProfileFragment : BaseMvvmFragment<FragmentProfileBinding, ProfileViewModel>() {

    private val headerView by lazy { HeaderRecyclerProfileTopBinding.inflate(layoutInflater) }
    private var vsLoginBinding: VsProfileLoginBinding? = null
    private var vsNotLoginBinding: VsProfileNotLoginBinding? = null
    private var vsBannerBinding: VsLayoutBannerBinding? = null
    private var vsSTCenterBinding: VsLayoutStCenterBinding? = null

    private val adapter by lazy {
        ProfileItemAdapter().apply {
            addHeaderView(headerView.root)
            setAdapterDefaultData(this)
        }
    }
    private val itemDecoration by lazy { DividerItemDecoration(0.dp2px(), 30.dp2px()) }

    private val launcher: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        if (it.resultCode == Constants.RESULT_CODE_SECURITY) {
            mViewModel.twoFactorStatusApi()
        }
    }

    /**
     * 钱包
     */
    private val cryptoWalletItem by lazy {
        ProfileItemBean(
            title = getString(R.string.crypto_wallet),
            icon = AttrResourceUtil.getDrawable(requireContext(), R.attr.imgProfileCryptoWallet),
            itemType = ProfileItemBean.TYPE_ITEM
        )
    }

    /**
     * ib转归属
     */
    private val transferIBItem by lazy {
        ProfileItemBean(
            title = getString(R.string.transfer_ib_cpa_sales),
            icon = AttrResourceUtil.getDrawable(requireContext(), R.attr.imgProfileTransferIB),
            itemType = ProfileItemBean.TYPE_ITEM
        )
    }

    /**
     * 邀请
     */
    private val referItem by lazy {
        ProfileItemBean(
            title = getString(R.string.referrals),
            icon = AttrResourceUtil.getDrawable(requireContext(), R.attr.imgProfileReferrals),
            itemType = ProfileItemBean.TYPE_ITEM
        )
    }

    /**
     * vantage 会员
     */
    private val vantageRewardsItem by lazy {
        ProfileItemBean(
            title = getString(R.string.vantage_rewards),
            icon = AttrResourceUtil.getDrawable(requireContext(), R.attr.imgProfileVantageRewards),
            itemType = ProfileItemBean.TYPE_ITEM
        )
    }

    /**
     * 任务中心
     */
    private val missionItem by lazy {
        ProfileItemBean(
            title = getString(R.string.mission_center),
            icon = AttrResourceUtil.getDrawable(requireContext(), R.attr.imgProfileMissionCenter),
            itemType = ProfileItemBean.TYPE_ITEM
        )
    }

    /**
     * 优惠券
     */
    private val couponsItem by lazy {
        ProfileItemBean(
            title = getString(R.string.coupons),
            itemType = ProfileItemBean.TYPE_ITEM,
            icon = AttrResourceUtil.getDrawable(requireContext(), R.attr.imgProfileCoupons)
        )
    }

    /**
     * ib
     */
    private val ibItem by lazy {
        ProfileItemBean(
            title = "IB",
            itemType = ProfileItemBean.TYPE_ITEM,
            icon = AttrResourceUtil.getDrawable(requireContext(), R.attr.imgProfileIb)
        )
    }

    /**
     * 收藏的策略
     */
    private val favouritesItem by lazy {
        ProfileItemBean(
            title = getString(R.string.favourites),
            itemType = ProfileItemBean.TYPE_ITEM,
            icon = AttrResourceUtil.getDrawable(requireContext(), R.attr.imgProfileFavourites)
        )
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        PerfTraceUtil.startTrace(PerfTraceUtil.StartTrace.Perf_v6_Profile_Create_First)
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun initParam(savedInstanceState: Bundle?) {
        EventBus.getDefault().register(this)
    }

    @SuppressLint("ObsoleteSdkInt", "SetTextI18n")
    override fun initView() {
        PerfTraceUtil.firstFrameTrace(mBinding.root, PerfTraceUtil.StartTrace.Perf_v6_Profile_Create_First, PerfTraceUtil.StartTrace.Perf_v6_Profile_First_Finish)
        initViewStub()
        initLoginState()

        // 神策自定义埋点(v3500)
        // App_Tab 页面浏览 -> app内五个tab页面加载完成时触发
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.TAB_NAME, "Profile") // Tab 名称
        SensorsDataUtil.track(SensorsConstant.V3500.APP_TAB_PAGE_VIEW, properties)
    }

    /**
     * 设置 登录 和 未登录 的页面回调
     */
    private fun initViewStub() {
        mBinding.vsLogin.setOnInflateListener { stub, inflated ->
            vsLoginBinding = VsProfileLoginBinding.bind(inflated)
            loginConfig()
        }
        mBinding.vsNotLogin.setOnInflateListener { stub, inflated ->
            vsNotLoginBinding = VsProfileNotLoginBinding.bind(inflated)
            vsNotLoginBinding?.tvLogin?.clickNoRepeat {
                doLoginClick()
                SensorsDataUtil.track(SensorsConstant.V3550.NLIPROFILEPAGE_SIGNUPLOGIN_CLICK)
            }
            notLoginConfig()
            SensorsDataUtil.track(SensorsConstant.V3550.NLIPROFILEPAGE_VIEW)
        }
    }

    /**
     * 根据登录设置显示状态
     */
    private fun initLoginState() {
        mBinding.vsLogin.isVisible = mViewModel.isLogin()
        mBinding.vsNotLogin.isGone = mViewModel.isLogin()
    }

    /**
     * 登录过 就跳 登录页面 ，从来没有登录过 就跳注册页面
     */
    private fun doLoginClick() {
        if (mViewModel.isLoggedIn()) {
            openActivity(LoginPwdActivity::class.java)
        } else {
            openActivity(SignUpActivity::class.java)
        }
    }

    /**
     * 登录后的设置
     */
    private fun loginConfig() {
        initTopAccountData()
        initLocalUserData()
        checkOtherVisibility()
        initBanner()
        initSTCenter()
        // 判断是跟单的话 显示信号源中心的view
        headerView.vsStCenter.isVisible = mViewModel.isCopyTrading()

        initRecyclerView()
        vsLoginBinding?.mRefreshLayout?.setOnRefreshListener { refreshLayout ->
            initTopAccountData()
            mViewModel.queryAllData()
        }
    }

    /**
     * 未登录的设置
     */
    private fun notLoginConfig() {
        initNotLoginTop()
        vsNotLoginBinding?.ivNotLogin?.let {
            ImageLoaderUtil.loadImage(requireContext(), AttrResourceUtil.getDrawable(requireContext(), R.attr.imgProfileWelcome), it)
        }
    }

    override fun initListener() {
        super.initListener()
        // 账号选择
        mBinding.clAccount.clickNoRepeat {
            if (mViewModel.isLogin()) {
                openActivity(
                    AccountManagerActivity::class.java,
                    Bundle().apply {
                        putInt(Constants.IS_FROM, 2)
                    }
                )
            } else {
                doLoginClick()
            }
        }
        // 客服
        mBinding.ivMessage.clickNoRepeat {
            openActivity(HelpCenterActivity::class.java)
        }
        // 设置
        mBinding.ivSetting.clickNoRepeat {
            openActivity(SettingActivity::class.java)
            // 神策自定义埋点(v3500)
            sensorsTrack("Function Button", "Settings")
        }
        // 个人主页
        headerView.viewHeader.clickNoRepeat {
            val bundle = Bundle()
            bundle.putString("IS_VERIFIED", mViewModel.verifiedStatus.toString())
            openActivity(PersonalDetailsActivity::class.java, bundle)
            // 神策自定义埋点(v3500)
            sensorsTrack("User Information", "more")
        }
        // uid 点击复制 并弹出toast
        headerView.tvUid.setOnClickListener {
            mViewModel.getUid().copyText(toast = getString(R.string.uid_copied))
            SensorsDataUtil.track(SensorsConstant.V3540.PROFILE_PAGE_UID_COPY_CLICK)
        }
        // 认证状态
        headerView.tvVerified.clickNoRepeat {
            if (mViewModel.isKycAccount()) {
                KycAuthActivity.openActivity(requireActivity())
            } else {
                AuthenticationActivity.openActivity(
                    requireActivity(),
                    mViewModel.userDataLiveData.value?.userNickName,
                    mViewModel.userDataLiveData.value?.pic
                )
            }
            // 神策自定义埋点(v3500)
            sensorsTrack("User Information", "more")
        }
        // 会员等级
        headerView.ivMembershipLevel.clickNoRepeat {
            val h5Url = mViewModel.userDataOtherLiveData.value?.vantageRewardUrl
            if (h5Url.isNullOrBlank()) return@clickNoRepeat

            openActivity(
                HtmlActivity::class.java,
                Bundle().apply {
                    putString("url", h5Url)
                    putInt("tradeType", 3)
                }
            )

            LogEventUtil.setLogEvent(
                BuryPointConstant.V331.PROMO_VANTAGE_REWARDS_PAGE_VIEW,
                hashMapOf("Position" to "Profile_badge")
            )
            // 神策自定义埋点(v3500)
            sensorsTrack("User Information", "more")
        }

        adapter.setNbOnItemClickListener { adapter, view, position ->
            val item = adapter.data.getOrNull(position) as? ProfileItemBean ?: return@setNbOnItemClickListener
            setListClick(item.title)
        }
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.userDataLiveData.observe(this) { value ->
            vsLoginBinding?.mRefreshLayout?.finishRefresh()
            updateUserData(value)
        }

        mViewModel.userDataOtherLiveData.observe(this) { value ->
            value?.let { home ->
                updateUserOtherData(home)
            }
        }
        mViewModel.twoFactorStatusLiveData.observe(this) {
            it?.let {
                showSecurityStatus(it)
            }
        }
        // 接收 只需要 一次的数据
        lifecycleScope.launch {
            // 不绑定生命周期，这样什么情况下都会收到数据 并刷新页面
            mViewModel.eventFlow.collectLatest {
                if (it == false) {
                    // base 接口网络请求失败
                    vsLoginBinding?.mRefreshLayout?.finishRefresh(false)
                    return@collectLatest
                }
                if (it !is DataEvent) {
                    return@collectLatest
                }
                when (it.tag) {
                    // 优惠券
                    ProfileViewModel.TAG_COUPONS -> {
                        checkAndAddRewardsItem()
                        if (mViewModel.isHideRewards()) {
                            findParentNode(R.string.other)?.let { node ->
                                checkAndAddOrRemove(node, couponsItem, 0, isNeedAdd = it.data == true)
                            }
                        } else {
                            findParentNode(R.string.rewards)?.let { rewardsItem ->
                                val index = rewardsItem.childNode?.indexOfFirst { node -> (node as ProfileItemBean).title == getString(R.string.mission_center) }.ifNull(-1)
                                checkAndAddOrRemove(rewardsItem, couponsItem, index + 1, isNeedAdd = it.data == true)
                            }
                        }
                    }
                    // 收藏的策略
                    ProfileViewModel.TAG_FAVOURITES -> {
                        findParentNode(R.string.other)?.let { node ->
                            checkAndAddOrRemove(node, favouritesItem, 0, isNeedAdd = it.data == true)
                        }
                    }

                    // 认证状态
                    ProfileViewModel.TAG_AUDIT_STATUS -> {
                        if (it.data !is AuditStatusData.Obj) {
                            // 如果网络请求错误 就显示获取认证的文案
                            updateVerifiedButton("-1", "-1")
                            return@collectLatest
                        }
                        updateVerifiedButton(it.data.accountAuditStatus.ifNull("-1"), it.data.poiAuditStatus.ifNull("-1"))
                    }

                    ProfileViewModel.TAG_KYC_AUDIT_STATUS -> {
                        updateKycVerifiedStatus(it.data.toString().toIntCatching())
                    }

                    ProfileViewModel.TAG_ST_CENTER -> {
                        if (it.data !is StStrategySignalProviderCenterBean.Data) return@collectLatest
                        updateStrategyCenterView(it.data)
                    }

                    ProfileViewModel.TAG_JUMP_SUMSUB -> {
                        SumSubJumpHelper.isJumpSumSub(requireContext(), Constants.SUMSUB_TYPE_POI)
                        LogEventUtil.setLogEvent(
                            BuryPointConstant.V334.REGISTER_LIVE_LVL2_BUTTON_CLICK,
                            hashMapOf("Position" to "Withdraw_button")
                        )
                    }

                    ProfileViewModel.TAG_OPEN_ACCOUNT -> {
                        SumSubJumpHelper.isJumpSumSub(requireContext(), Constants.SUMSUB_TYPE_POA)
                        LogEventUtil.setLogEvent(
                            BuryPointConstant.V334.REGISTER_LIVE_LVL3_BUTTON_CLICK,
                            hashMapOf("Position" to "Withdraw_button")
                        )
                    }

                    ProfileViewModel.TAG_OPEN_ASIC_ADDRESS -> {
                        val bundle = Bundle()
                        bundle.putInt(Constants.IS_FROM, 1)
                        openActivity(OpenFifthAddressSelectActivity::class.java, bundle)
                    }

                    ProfileViewModel.TAG_JUMP_WITHDRAW -> {
                        hideLoadDialog()
                        val bundle = Bundle()
                        bundle.putString("url", it.data.toString())
                        bundle.putString("title", getString(R.string.withdraw))
                        bundle.putInt("tradeType", 3)
                        bundle.putBoolean("isNoTitleCanBack", true)
                        openActivity(HtmlActivity::class.java, bundle)
                    }
                }

                PerfTraceUtil.stopTrace(PerfTraceUtil.StartTrace.Perf_v6_Profile_First_Finish)
            }
        }
        // 请求所有数据需要只能放在这里，因为需要提前设置监听，用于监听是否需要添加item
        mViewModel.queryAllData()
    }

    /**
     * 初始化 登录后 顶部栏图标 和 账号类型
     */
    private fun initTopAccountData() {
        if (mViewModel.isLiveVirtualAccount()) {
            mBinding.tvAccountStatus.text = getString(R.string.live)
            mBinding.groupAccountNum.isVisible = false
        } else if (mViewModel.isCopyTrading()) {
            mBinding.groupAccountNum.isVisible = true
            mBinding.tvAccountId.text = mViewModel.getUserAccount()
            mBinding.tvAccountStatus.text = buildString {
                append("Copy Trading")
            }
        } else {
            mBinding.groupAccountNum.isVisible = true
            mBinding.tvAccountId.text = mViewModel.getUserAccount()
            if (mViewModel.isDemo()) {
                mBinding.tvAccountStatus.text = getString(R.string.demo)
            } else {
                mBinding.tvAccountStatus.text = getString(R.string.live)
            }
        }
    }

    /**
     * 初始化未登录 的顶部栏样式
     */
    private fun initNotLoginTop() {
        mBinding.groupAccountNum.isGone = true
        mBinding.tvAccountStatus.text = if (mViewModel.isLoggedIn()) getString(R.string.log_in) else getString(R.string.demo)
    }

    /**
     * 用本地数据存储的用户数据 初始化一遍 能显示的数据
     */
    private fun initLocalUserData() {
        ImageLoaderUtil.loadImageWithOption(
            requireContext(),
            mViewModel.userPic(),
            headerView.ivAvatar,
            RequestOptions()
                .placeholder(R.mipmap.ic_launcher)
                .error(R.mipmap.ic_launcher)
        )
        headerView.tvNickName.text = mViewModel.userNickName()
        if (mViewModel.getUid().isNotBlank()) {
            headerView.tvUid.isVisible = true
            headerView.tvUid.text = buildString {
                append("UID: ")
                append(mViewModel.getUid())
            }
        } else {
            headerView.tvUid.isVisible = false
        }
    }

    /**
     * 设置vsBanner的监听 inflate后 初始化 Banner
     */
    private fun initBanner() {
        headerView.vsBanner.setOnInflateListener { stub, inflated ->
            vsBannerBinding = VsLayoutBannerBinding.bind(inflated)
            vsBannerBinding?.mBanner?.setAdapter(object : BannerImageAdapter<String>(emptyList()) {
                override fun onBindView(holder: BannerImageHolder?, data: String?, position: Int, size: Int) {
                    val options = RequestOptions()
                        .placeholder(R.drawable.shape_placeholder)
                        .error(R.drawable.shape_placeholder)
                    holder?.imageView?.setScaleType(ImageView.ScaleType.FIT_XY)
                    ImageLoaderUtil.loadImageWithOption(requireContext(), data, holder?.imageView, options)
                }
            })?.setScrollTime(1500)
                ?.addBannerLifecycleObserver(this)

            vsBannerBinding?.mBanner?.setOnBannerListener { _, bannerPosition ->
                val bannerBean = mViewModel.userDataOtherLiveData.value?.advert?.getOrNull(bannerPosition)
                val pushBean = bannerBean?.appJumpDefModel

                VAUStartUtil.openActivity(requireContext(), pushBean)

                mViewModel.eventsAddClicksCountApi(bannerBean?.eventId.ifNull())

                // 神策自定义埋点(v3500)
                // App_我的页面Banner点击 -> 点击app我的页面Banner位时触发
                val properties = JSONObject()
                properties.put(SensorsConstant.Key.MKT_ID, bannerBean?.eventId.ifNull()) // 素材id
                properties.put(SensorsConstant.Key.MKT_NAME, "") // 素材名称
                properties.put(SensorsConstant.Key.MKT_RANK, bannerPosition + 1) // 素材排序
                properties.put(SensorsConstant.Key.TARGET_URL, pushBean?.urls?.def.ifNull()) // 跳转链接
                SensorsDataUtil.track(SensorsConstant.V3500.APP_PROFILE_BANNER_CLICK, properties)
            }

            vsBannerBinding?.mBanner?.addOnPageChangeListener(object : CustomViewPagerOnPageChangeListener() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    vsBannerBinding?.mIndicator?.changeIndicator(position)
                }
            })
        }
    }

    /**
     * 设置监听 信号源中心的view inflate
     */
    private fun initSTCenter() {
        headerView.vsStCenter.setOnInflateListener { stub, inflated ->
            vsSTCenterBinding = VsLayoutStCenterBinding.bind(inflated)
            // 信号源中心的页面点击事件
            vsSTCenterBinding?.root?.clickNoRepeat {
                if (mViewModel.isStPublicTrade()) {
                    openActivity(StSignalCenterActivity::class.java)
                    LogEventUtil.setLogEvent(BuryPointConstant.V348.CT_PROFILE_SP_CENTER_BTN_CLICK)
                } else {
                    openActivity(StProviderToPublicTradeActivity::class.java)
                    LogEventUtil.setLogEvent(BuryPointConstant.V348.CT_PROFILE_BECOME_SP_BTN_CLICK)
                }
            }
        }
    }

    /**
     * 更新信号源中心的数据
     */
    private fun updateStrategyCenterView(dataBean: StStrategySignalProviderCenterBean.Data?) {
        vsSTCenterBinding?.tvStrategies?.text =
            buildString {
                append(dataBean?.publicStrategyCount.ifNull("0"))
                append("/")
                append(dataBean?.maxStrategyCount.ifNull("100"))
            }
        if (UserDataUtil.isStPublicTrade()) {
            vsSTCenterBinding?.tvActiveCopiers?.text = dataBean?.copiers.ifNull("0")
        }
    }

    /**
     * 初始化recyclerview ， 设置 title 占满一行，item 4个一行
     */
    private fun initRecyclerView() {
        val gridLayoutManager = GridLayoutManager(context, 4)
        gridLayoutManager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int = when (adapter.getItemViewType(position)) {
                ProfileItemBean.TYPE_TITLE -> gridLayoutManager.spanCount
                else -> 1
            }
        }
        vsLoginBinding?.mRecyclerView?.layoutManager = gridLayoutManager
        // 如果没有添加底部间距，则添加一次
        if (vsLoginBinding?.mRecyclerView?.itemDecorationCount == 0) {
            vsLoginBinding?.mRecyclerView?.addItemDecoration(itemDecoration)
        }
        vsLoginBinding?.mRecyclerView?.adapter = adapter
    }

    /**
     * 设置底部list的点击事件
     */
    private fun setListClick(title: String?) {
        when (title) {
            // 入金
            getString(R.string.deposit) -> {
                if (mViewModel.isDemo()) {
                    ToastUtil.showToast(getString(R.string.this_function_is_accounts))
                    return
                }

                NewHtmlActivity.openActivity(requireContext(), url = UrlConstants.HTML_FUND_DEPOSIT)
                LogEventUtil.setLogEvent(
                    BuryPointConstant.V331.DEPOSIT_TRAFFIC_BUTTON_CLICK,
                    hashMapOf(
                        "Position" to "Profile"
                    )
                )
                // 神策自定义埋点(v3500)
                sensorsTrack("Money Button", "Deposit")
            }
            // 转账 TransferActivity
            getString(R.string.transfer) -> {
                if (mViewModel.isDemo()) {
                    ToastUtil.showToast(getString(R.string.this_function_is_accounts))
                    return
                }

                NewHtmlActivity.openActivity(requireContext(), url = UrlConstants.HTML_FUND_TRANSFER)
                // 神策自定义埋点(v3500)
                sensorsTrack("Money Button", "Transfer")
            }
            // 出金
            getString(R.string.withdraw) -> {
                if (mViewModel.isDemo()) {
                    ToastUtil.showToast(getString(R.string.this_function_is_accounts))
                    return
                }
                mViewModel.jumpWithdraw()
            }

            // 资金管理
            getString(R.string.funds) -> {
                // 该功能仅支持真实账户
                if (mViewModel.isDemo()) {
                    ToastUtil.showToast(getString(R.string.this_function_is_accounts))
                    return
                }
                if (mViewModel.isCopyTrading()) {
                    openActivity(StFundsActivity::class.java)
                } else {
                    openActivity(FundsActivity::class.java)
                }
                // 神策自定义埋点(v3500)
                sensorsTrack("Money Button", "Funds")
            }
            // 钱包管理
            getString(R.string.crypto_wallet) -> {
                // 该功能仅支持真实账户
                if (mViewModel.isDemo()) {
                    ToastUtil.showToast(getString(R.string.this_function_is_accounts))
                    return
                }

                if (!mViewModel.userDataOtherLiveData.value?.walletUrl.isNullOrBlank()) {
                    NewHtmlActivity.openActivity(
                        requireContext(),
                        url = mViewModel.userDataOtherLiveData.value?.walletUrl.ifNull()
//                    url = "https://secure-bluebird.crm-alpha.com/appToWallet?token=67328cabf9ef4f729b3432bd4ab5340a&crmUserId=********&sign=189C1F98E080EBED4F619E65A16DC2A39665FAA957CE777D6DA7DAA762B62E3E&device=ios&lang=ja&theme=1",
                    )
                }
            }

            // 会员中心
            getString(R.string.vantage_rewards) -> {
                if (!NewHtmlViewModel.isJumpNewHtml(mViewModel.userDataOtherLiveData.value?.vantageRewardUrl.ifNull())) {
                    openActivity(
                        HtmlActivity::class.java,
                        Bundle().apply {
                            putString("url", mViewModel.userDataOtherLiveData.value?.vantageRewardUrl.ifNull())
                            putInt("tradeType", 3)
                            putString("title", title)
                        }
                    )
                } else {
                    NewHtmlActivity.openActivity(requireContext(), url = mViewModel.userDataOtherLiveData.value?.vantageRewardUrl.ifNull(), title = title)
                }
                LogEventUtil.setLogEvent(
                    BuryPointConstant.V331.PROMO_VANTAGE_REWARDS_PAGE_VIEW,
                    hashMapOf("Position" to "Profile")
                )
                // 神策自定义埋点(v3500)
                sensorsTrack("Function Button", "Vantage Rewards")
            }

            // 任务中心
            getString(R.string.mission_center) -> {
                if (!NewHtmlViewModel.isJumpNewHtml(mViewModel.userDataOtherLiveData.value?.missionCenterUrl.ifNull())) {
                    openActivity(
                        HtmlActivity::class.java,
                        Bundle().apply {
                            putString("url", mViewModel.userDataOtherLiveData.value?.missionCenterUrl ?: "")
                            putInt("tradeType", 3)
                            putString("title", title)
                        }
                    )
                } else {
                    NewHtmlActivity.openActivity(requireContext(), url = mViewModel.userDataOtherLiveData.value?.missionCenterUrl.ifNull(), title = title)
                }
                LogEventUtil.setLogEvent(
                    BuryPointConstant.V331.PROMO_VANTAGE_REWARDS_MISSION_CENTER_PAGE_VIEW,
                    hashMapOf("Position" to "Profile")
                )
                // 神策自定义埋点(v3500)
                sensorsTrack("Function Button", "Mission Center")
            }

            // 优惠券
            getString(R.string.coupons) -> {
                openActivity(CouponManagerActivity::class.java)
                // 神策自定义埋点(v3500)
                sensorsTrack("Function Button", "Coupons")
            }

            "IB" -> {
                openActivity(
                    HtmlActivity::class.java,
                    Bundle().apply {
                        putInt("tradeType", 25)
                    }
                )
                // 神策自定义埋点(v3500)
                sensorsTrack("Function Button", "IB")
            }

            getString(R.string.favourites) -> {
                openActivity(StFollowListActivity::class.java)
                // 神策自定义埋点(v3500)
                sensorsTrack("User Information", "more")
            }

            // 价格提醒
            getString(R.string.price_alerts) -> {
                openActivity(PriceAlertsManageActivity::class.java)
            }

            // 邀请
            getString(R.string.referrals) -> {
                if (TextUtils.isEmpty(mViewModel.userDataOtherLiveData.value?.referFriendUrl)) return
                NewHtmlActivity.openActivity(requireContext(), mViewModel.userDataOtherLiveData.value?.referFriendUrl.ifNull())
                LogEventUtil.setLogEvent(
                    BuryPointConstant.V342.PROMO_REFERRAL_BONUS_PAGE_VIEW,
                    Bundle().apply {
                        putString(
                            BuryPointConstant.PositionType.KEY_POSITION,
                            BuryPointConstant.PositionType.PROFILE
                        )
                        putString(BuryPointConstant.PositionType.KEY_ELIGIBILITY, "-")
                    }
                )
                // 神策自定义埋点(v3500)
                sensorsTrack("Function Button", "Referrals")
            }

            // 安全
            getString(R.string.security) -> {
                launcher.launch(
                    Intent(context, SecurityActivity::class.java).apply {
                        if (mViewModel.twoFactorStatusLiveData.value != null) {
                            putExtra("TwoFactorStatus", mViewModel.twoFactorStatusLiveData.value)
                        }
                    }
                )
                // 神策自定义埋点(v3500)
                sensorsTrack("Function Button", "Account and Security")
            }

            // ib 转归属
            getString(R.string.transfer_ib_cpa_sales) -> {
                if (mViewModel.userDataOtherLiveData.value?.transferUrl.isNullOrBlank()) return
                NewHtmlActivity.openActivity(requireContext(), url = mViewModel.userDataOtherLiveData.value?.transferUrl.ifNull())
            }
        }
    }

    /**
     * 网络获取用户数据 并显示
     */
    private fun updateUserData(value: AccountHomeData.MyHome?) {
        if (true == value?.isIB) {
            mViewModel.setUserIsIB()
            if (!adapter.data.contains(ibItem)) {
                // Coupons > IB > favourites
                findParentNode(R.string.other)?.let { node ->
                    val couponsIndex = node.childNode?.indexOfFirst { item -> (item as ProfileItemBean).title == getString(R.string.coupons) }.ifNull()
                    checkAndAddOrRemove(node, ibItem, couponsIndex + 1, isNeedAdd = true)
                }
            }
        } else {
            findParentNode(R.string.other)?.let { node ->
                checkAndAddOrRemove(node, ibItem, isNeedAdd = false)
            }
        }
        if (mViewModel.getUid().isNotBlank()) {
            headerView.tvUid.isVisible = true
            headerView.tvUid.text = buildString {
                append("UID: ")
                append(mViewModel.getUid())
            }
        } else {
            headerView.tvUid.isVisible = false
        }
        ImageLoaderUtil.loadImageWithOption(
            requireContext(),
            value?.pic.ifNull(),
            headerView.ivAvatar,
            RequestOptions()
                .placeholder(R.mipmap.ic_launcher)
                .error(R.mipmap.ic_launcher)
        )

        headerView.tvNickName.text = value?.userNickName ?: ""
        // 是否需要刷新Telegram图标
        if (!mViewModel.isEventBusRefreshTelegramStatus) {
            // 是否显示telegram图标
            headerView.ivTelegram.isVisible = value?.telegramStatus == 2
        }

        value?.mt4AccountId?.let {
            mBinding.tvAccountId.text = it
        }
    }

    /**
     * 网络获取用户其他数据 并显示
     */
    private fun updateUserOtherData(data: AccountHomeData.MyHome) {
        headerView.ivMembershipLevel.isGone = TextUtils.isEmpty(data.mpProfilePic)

        checkOtherVisibility()
        ImageLoaderUtil.loadImage(requireContext(), data.mpProfilePic, headerView.ivMembershipLevel)
        // 不展示 rewards item 的情况 直接不判断内部添加和移除逻辑
        if (!mViewModel.isHideRewards()) {
            checkAndAddRewardsItem()
            findParentNode(R.string.rewards)?.let { node ->
                // 是否展示会员中心和任务中心
                checkAndAddOrRemove(node, vantageRewardsItem, 0, true == data.isShowMp)
                checkAndAddOrRemove(node, missionItem, 1, true == data.isShowMp)
                // 是否展示邀请
                checkAndAddOrRemove(node, referItem, isNeedAdd = true == data.isShowReferFriend)
            }
        }
        if (data.showWallet) {
            findParentNode(R.string.assets)?.let { node ->
                if (mViewModel.userDataOtherLiveData.value?.showWalletSsNew == true) {
                    cryptoWalletItem.rightTopString = "NEW"
                }
                // 展示钱包
                checkAndAddOrRemove(node, cryptoWalletItem, isNeedAdd = true)
            }
        } else {
            findParentNode(R.string.assets)?.let { node ->
                // 不展示钱包
                checkAndAddOrRemove(node, cryptoWalletItem, isNeedAdd = false)
            }
        }
        findParentNode(R.string.other)?.let { node ->
            // 是否在other的最后面显示 ib 转归属的item
            checkAndAddOrRemove(node, transferIBItem, isNeedAdd = !data.transferUrl.isNullOrBlank() && mViewModel.isV1V2Supervise())
        }

        // banner
        if (data.advert?.size.ifNull(0) == 0) {
            headerView.vsBanner.isGone = true
        } else {
            headerView.vsBanner.isVisible = true
            vsBannerBinding?.mIndicator?.isVisible = data.advert?.size.ifNull(0) > 1
            vsBannerBinding?.mIndicator?.initIndicatorCount(data.advert?.size.ifNull(0))
            vsBannerBinding?.mBanner?.setDatas(data.advert?.map { data -> data.imgUrl })
            vsBannerBinding?.mBanner?.start()
        }
    }

    private fun updateKycVerifiedStatus(level: Int) {
        headerView.tvVerified.isVisible = true
        checkOtherVisibility()

        when (level) {
            1 -> {
                headerView.tvVerified.text = getString(R.string.partially_verified)
                mViewModel.verifiedStatus = getString(R.string.partially_verified)
                headerView.tvVerified.setTextColor(ContextCompat.getColor(requireContext(), R.color.cff8e5c))
                headerView.tvVerified.background = ContextCompat.getDrawable(requireContext(), R.drawable.draw_shape_cfcebe5_c3e3535_r100)
                val drawable = ContextCompat.getDrawable(requireContext(), R.drawable.bitmap_img_source_tick11x8_ce35728)
                drawable?.setBounds(0, 0, drawable.intrinsicWidth, drawable.intrinsicHeight)
                headerView.tvVerified.setCompoundDrawablesRelativeWithIntrinsicBounds(drawable, null, null, null)
            }

            2 -> {
                headerView.tvVerified.text = getString(R.string.verified)
                mViewModel.verifiedStatus = getString(R.string.verified)
                headerView.tvVerified.setTextColor(ContextCompat.getColor(requireContext(), R.color.c00c79c))
                headerView.tvVerified.background = ContextCompat.getDrawable(requireContext(), R.drawable.draw_shape_ce5f7f3_c283b3d_r100)
                val drawable = ContextCompat.getDrawable(requireContext(), R.drawable.bitmap_img_source_tick11x8_c00c79c)
                drawable?.setBounds(0, 0, drawable.intrinsicWidth, drawable.intrinsicHeight)
                headerView.tvVerified.setCompoundDrawablesRelativeWithIntrinsicBounds(drawable, null, null, null)
            }

            else -> {
                headerView.tvVerified.text = getString(R.string.unverified)
                mViewModel.verifiedStatus = getString(R.string.unverified)
                headerView.tvVerified.setTextColor(AttrResourceUtil.getColor(requireContext(), R.attr.color_ca61e1e1e_c99ffffff))
                headerView.tvVerified.background = ContextCompat.getDrawable(requireContext(), R.drawable.draw_shape_ce8e8e8_c414348_r100)
                headerView.tvVerified.setCompoundDrawablesRelativeWithIntrinsicBounds(null, null, null, null)
            }
        }
    }

    /**
     * 更新用户的验证状态
     */
    private fun updateVerifiedButton(lv1Status: String?, lv2Status: String?) {
        headerView.tvVerified.isVisible = true
        checkOtherVisibility()

        mViewModel.verifiedStatus =
            if (lv1Status == AuthenticationActivity.TYPE_LV1_COMPLETED && lv2Status == AuthenticationActivity.TYPE_COMPLETED) {
                AuthenticationActivity.updateTextView(headerView.tvVerified, AuthenticationActivity.TEXTVIEW_TYPE_VERIFIED)
            } else if (lv1Status == AuthenticationActivity.TYPE_LV1_COMPLETED || lv2Status == AuthenticationActivity.TYPE_COMPLETED) {
                AuthenticationActivity.updateTextView(headerView.tvVerified, AuthenticationActivity.TEXTVIEW_TYPE_SEMI_VERIFIED)
            } else {
                AuthenticationActivity.updateTextView(headerView.tvVerified, AuthenticationActivity.TEXTVIEW_TYPE_GET_VERIFIED)
            }
    }

    /**
     * 认证状态和会员积分Profile图片 有一个显示 就让 ll父布局显示
     */
    private fun checkOtherVisibility() {
        // asic 不跳转新开户页面 所以直接不显示认证按钮
        if (mViewModel.isAsic()) {
            headerView.tvVerified.isVisible = false
        }
        headerView.llOther.isVisible = headerView.tvVerified.isVisible || headerView.ivMembershipLevel.isVisible
    }

    /**
     * 显示安全设置状态 , 全部设置 才不显示感叹号
     */
    @SuppressLint("NotifyDataSetChanged")
    private fun showSecurityStatus(data: SecurityStatusData.Obj) {
        val otherItem = findParentNode(R.string.other)
        val securityItem = otherItem?.childNode?.firstOrNull { node -> (node as ProfileItemBean).title == getString(R.string.security) }
        // 设置了少于3个安全机制时，账号和安全设置的图标显示角标；设置了3个安全机制，不显示角标
        if (!(data.passKeyStatus == true && data.twoFactorUser == true && mViewModel.isSetLocalLock())) {
            (securityItem as ProfileItemBean).rightTopIcon = R.drawable.icon2_cb_info_circle_ce35728
        }
        adapter.notifyDataSetChanged()
    }

    /**
     * 通过title 查找父节点
     */
    private fun findParentNode(@StringRes titleResId: Int): BaseNode? = adapter.data.firstOrNull { node -> (node as ProfileItemBean).title == getString(titleResId) }

    /**
     * 检测是否需要添加rewardsItem
     */
    private fun checkAndAddRewardsItem() {
        if (mViewModel.isHideRewards()) return
        val index = adapter.data.indexOfFirst { node -> (node as ProfileItemBean).title == getString(R.string.rewards) }
        if (index == -1) {
            val fundsIndex = adapter.data.indexOfFirst { node -> (node as ProfileItemBean).title == getString(R.string.funds) }
            val cryptoWalletIndex = adapter.data.indexOfFirst { node -> (node as ProfileItemBean).title == getString(R.string.crypto_wallet) }
            // 如果有钱包的话 就插入到钱包后面
            if (cryptoWalletIndex != -1) {
                adapter.addData(cryptoWalletIndex + 1, createDefaultRewardsItem())
            } else {
                // 如果没有钱包 就插入到 资金后面
                // 如果没有资金 -1 +1 = 0 就是插到 0 的位置
                adapter.addData(fundsIndex + 1, createDefaultRewardsItem())
            }
        }
    }

    /**
     * 检测是否需要添加或移除
     */
    private fun checkAndAddOrRemove(parentNode: BaseNode, childNode: BaseNode, index: Int = -1, isNeedAdd: Boolean = true) {
        if (isNeedAdd && parentNode.childNode?.contains(childNode) != true) {
            if (index >= 0) {
                adapter.nodeAddData(parentNode, index, childNode)
            } else {
                adapter.nodeAddData(parentNode, childNode)
            }
        } else if (!isNeedAdd && parentNode.childNode?.contains(childNode) == true) {
            adapter.nodeRemoveData(parentNode, childNode)
        }
    }

    /**
     * 重置 adapter内的item 恢复到 默认显示的item
     */
    private fun setAdapterDefaultData(profileItemAdapter: ProfileItemAdapter) {
        profileItemAdapter.setList(mutableListOf(createDefaultOtherItem()))
        // 判断 当前是 demo
        val assetsIndex = profileItemAdapter.data.indexOfFirst { node -> (node as ProfileItemBean).title == getString(R.string.assets) }
        if (mViewModel.isDemo()) {
            //  并且 有 assets 这个item 就移除掉
            if (assetsIndex != -1) {
                profileItemAdapter.removeAt(assetsIndex)
            }
        } else {
            // 非 demo 的情况下 如果没有 assets 这个item 就添加上去
            if (assetsIndex == -1) {
                profileItemAdapter.addData(0, createDefaultAssetsItem())
            }
        }
    }

    /**
     * 构造最基础的 other item 显示的数据
     */
    private fun createDefaultOtherItem() = ProfileItemBean(
        title = getString(R.string.other),
        itemType = ProfileItemBean.TYPE_TITLE,
        childNode = mutableListOf(
            ProfileItemBean(
                title = getString(R.string.price_alerts),
                itemType = ProfileItemBean.TYPE_ITEM,
                icon = AttrResourceUtil.getDrawable(requireContext(), R.attr.imgProfilePriceAlter)
            ),
            ProfileItemBean(
                title = getString(R.string.security),
                itemType = ProfileItemBean.TYPE_ITEM,
                icon = AttrResourceUtil.getDrawable(requireContext(), R.attr.imgProfileSecurity)
            )
        )
    )

    /**
     * 构造最基础的 assets item 显示的数据
     * 资产的item 以及内部节点 入金 转账 出金 资金
     */
    private fun createDefaultAssetsItem() = ProfileItemBean(
        title = getString(R.string.assets),
        itemType = ProfileItemBean.TYPE_TITLE,
        childNode = mutableListOf(
            ProfileItemBean(
                title = getString(R.string.deposit),
                itemType = ProfileItemBean.TYPE_ITEM,
                icon = AttrResourceUtil.getDrawable(requireContext(), R.attr.imgProfileDeposit)
            ),
            ProfileItemBean(
                title = getString(R.string.transfer),
                itemType = ProfileItemBean.TYPE_ITEM,
                icon = AttrResourceUtil.getDrawable(requireContext(), R.attr.imgProfileTransfer)
            ),
            ProfileItemBean(
                title = getString(R.string.withdraw),
                itemType = ProfileItemBean.TYPE_ITEM,
                icon = AttrResourceUtil.getDrawable(requireContext(), R.attr.imgProfileWithdraw)
            ),
            ProfileItemBean(
                title = getString(R.string.funds),
                itemType = ProfileItemBean.TYPE_ITEM,
                icon = AttrResourceUtil.getDrawable(requireContext(), R.attr.imgProfileFunds)
            )
        )
    )

    /**
     * 构造最基础的 rewards item 显示的数据
     */
    private fun createDefaultRewardsItem() = ProfileItemBean(
        title = getString(R.string.rewards),
        itemType = ProfileItemBean.TYPE_TITLE
    )

    @SuppressLint("SetTextI18n")
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            NoticeConstants.AFTER_LOGOUT_RESET -> {
                initLoginState()
                notLoginConfig()
            }

            NoticeConstants.SWITCH_ACCOUNT -> {
                initLoginState()
                resetViewAndData()
                mViewModel.queryAllData()
            }
            /*
             出入金，IB佣金申请
             个人信息有修改 , 开通真实账户成功（跳老拉新数据需要重新获取）
             用户变组
             昵称 ， 头像
             杠杆变化
             */
            NoticeConstants.WS.CHANGE_OF_FUNDS,
            NoticeConstants.REFRESH_PERSONAL_INFO_DATA, NoticeConstants.REFRESH_ACCOUNT_MANAGER,
            NoticeConstants.WS.LOGIN_ERROR_CHANGE_OF_GROUP, NoticeConstants.REFRESH_OPEN_ACCOUNT_GUIDE,
            NoticeConstants.LINK_THIRD_PARTY
                -> {
                mViewModel.isNeedResumeRefresh = true
            }

            NoticeConstants.CHANGE_NAME -> {
                headerView.tvNickName.text = mViewModel.userNickName()
            }

            NoticeConstants.CHANGE_PHOTO -> {
                // 加载本地保存的头像地址
                ImageLoaderUtil.loadImageWithOption(
                    requireContext(),
                    mViewModel.userPic(),
                    headerView.ivAvatar,
                    RequestOptions()
                        .placeholder(R.mipmap.ic_launcher)
                        .error(R.mipmap.ic_launcher)
                )
            }

            NoticeConstants.SYNC_SECURITY_LEVEL -> {
                mViewModel.twoFactorStatusApi()
            }
            // 公开交易设置成功
            NoticeConstants.PROVIDER_TO_PUBLIC_TRADE_SUCCESS -> {
                mViewModel.strategySignalProviderCenterApi()
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onDataEvent(event: DataEvent) {
        when (event.tag) {
            NoticeConstants.REFRESH_TELEGRAM_BINDING_RESULT -> { // telegram 绑定结果
                mViewModel.isEventBusRefreshTelegramStatus = true
                val isBind = event.data as? Boolean
                headerView.ivTelegram.isVisible = isBind == true
            }

            NoticeConstants.STStrategy.NOTIFY_STRATEGY_PUBLIC_COUNT -> {
                vsSTCenterBinding?.tvStrategies?.text =
                    buildString {
                        append(event.data.ifNull(0).toString())
                        append("/")
                        append("100")
                    }
            }

            NoticeConstants.SYNC_KYC_USER_LEVEL -> {
                val kycVerifyLevelObj = event.data as? KycVerifyLevelObj
                updateKycVerifiedStatus(kycVerifyLevelObj?.level.ifNull())
            }
        }
    }

    /**
     * 切换账号或退出重进的时候 重置页面和数据 ， 显示默认样式
     */
    private fun resetViewAndData() {
        setAdapterDefaultData(adapter)
        initTopAccountData()
        initLocalUserData()
        headerView.vsStCenter.isVisible = mViewModel.isCopyTrading()
        headerView.ivMembershipLevel.isVisible = false
        checkOtherVisibility()
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (headerView.vsBanner.isVisible) {
            if (hidden) {
                vsBannerBinding?.mBanner?.stop()
            } else {
                // 设置 profile页面显示的时候 判断显示登录还是未登录的布局
                initViewStub()
                initLoginState()
                vsBannerBinding?.mBanner?.start()
                if (mViewModel.isNeedResumeRefresh) {
                    mViewModel.isNeedResumeRefresh = false
                    mViewModel.queryAllData()
                }
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        EventBus.getDefault().unregister(this)
    }

    /**
     * 神策自定义埋点(v3500)
     * App_我的页面按钮点击 -> App_我的页面按钮点击
     */
    private fun sensorsTrack(buttonLocation: String, buttonName: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.BUTTON_LOCATION, buttonLocation) // 按钮位置
        properties.put(SensorsConstant.Key.BUTTON_NAME, buttonName) // 按钮名称
        SensorsDataUtil.track(SensorsConstant.V3500.APP_PROFILE_PAGE_CLICK, properties)
    }
}