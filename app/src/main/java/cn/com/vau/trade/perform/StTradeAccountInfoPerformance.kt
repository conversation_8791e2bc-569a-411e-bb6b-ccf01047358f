package cn.com.vau.trade.perform

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.view.View
import android.view.ViewGroup
import android.view.animation.DecelerateInterpolator
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import cn.com.vau.R
import cn.com.vau.common.application.InitHelper
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.performance.AbsPerformance
import cn.com.vau.common.performance.PerformManager
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.databinding.FragmentStManualTradingBinding
import cn.com.vau.databinding.LayoutBottomTipGlossaryBinding
import cn.com.vau.databinding.LayoutBottomTradeTipMarginLevelBinding
import cn.com.vau.trade.data.AccountInfoCardBean
import cn.com.vau.trade.data.AccountInfoDiffUtil
import cn.com.vau.trade.data.AccountInfoItemBean
import cn.com.vau.trade.data.AccountInfoType
import cn.com.vau.trade.data.applyFieldFormatting
import cn.com.vau.trade.ext.copyData
import cn.com.vau.trade.ext.setExpandAnimation
import cn.com.vau.trade.ext.toBalanceColor
import cn.com.vau.trade.ext.toFloatingPnlColor
import cn.com.vau.trade.ext.toMarginLevelColor
import cn.com.vau.trade.ext.toMarginRiskLevel
import cn.com.vau.trade.view.TradeAccountInfoView
import cn.com.vau.trade.viewmodel.OrderViewModel
import cn.com.vau.ui.order.perform.BalanceResetPerformance
import cn.com.vau.util.mathAdd
import cn.com.vau.util.screenHeight
import cn.com.vau.util.widget.dialog.base.BottomDialog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * Created by array on 2025/5/12 14:31
 * Desc: 跟单账户信息
 */
class StTradeAccountInfoPerformance(
    val fragment: Fragment,
    private val orderViewMode: OrderViewModel,
    private val mBinding: FragmentStManualTradingBinding,
) : AbsPerformance() {
    private var glossaryPopup: BottomDialog<LayoutBottomTipGlossaryBinding>? = null
    private var glossaryView: LayoutBottomTipGlossaryBinding? = null

    private var marginLevelPopup: BottomDialog<LayoutBottomTradeTipMarginLevelBinding>? = null
    private var marginLevelView: LayoutBottomTradeTipMarginLevelBinding? = null

    private val performManager by lazy {
        PerformManager(fragment)
    }

    /**
     * 账户信息ViewModel
     */
    private var viewModel: StAccountInfoViewModel? = null

    /**
     * 账户信息差异刷新工具
     */
    private val diffUtil: AccountInfoDiffUtil by lazy {
        AccountInfoDiffUtil()
    }

    /**
     * 当前账户信息卡片bean
     */
    private var mCardBean: AccountInfoCardBean? = null

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        viewModel = ViewModelProvider(fragment)[StAccountInfoViewModel::class.java]
        EventBus.getDefault().register(this)
        initView()
        initListener()
        addPerformance()
    }

    /**
     * 设置展开/收起状态
     * @param expand true为展开，false为收起
     */
    fun setExpand(expand: Boolean) {
        mBinding.mAccountInfoView.setExpandAnimation(expand)
    }
    private fun addPerformance() {
        //负值清零
        performManager.addPerformance(BalanceResetPerformance(fragment, mBinding.mAccountInfoView.getResetEntry()))
    }

    override fun onCallback() {
        showAccountInfo()
    }

    fun initView() {
        showAccountInfo()
    }

    fun initListener() {
        /**
         * 点击选中账户信息
         */
        mBinding.mAccountInfoView.setOnSelectListener { type ->
            updateTitleAccountInfo(type)
        }

        /**
         * 点击账户信息title，弹窗
         */
        mBinding.mAccountInfoView.setOnTipClickListener { type ->
            when (type) {
                AccountInfoType.MarginLevel -> showMarginLevelPop()
                else -> showGlossaryPop()
            }
        }

        /**
         * 事件流：刷新账户信息UI
         */
        fragment.lifecycleScope.launch {
            viewModel?.cardBeanFlow?.flowWithLifecycle(fragment.lifecycle, Lifecycle.State.STARTED)?.collect {
                refreshAccountInfoUi(it)
            }
        }
    }

    /**
     * 刷新账户信息UI
     */
    private fun refreshAccountInfoUi(cardBean: AccountInfoCardBean) {
        if (cardBean.changedFields.isEmpty()) {
            return
        }
        cardBean.changedFields.forEach { field ->
            when (field) {
                AccountInfoType.Equity -> {
                    handleEquity(cardBean)
                }

                AccountInfoType.FloatingPnL -> {
                    handleFloatingPnL(cardBean)
                }

                AccountInfoType.MarginLevel -> {
                    handleMarginLevel(cardBean)
                }

                AccountInfoType.Credit -> {
                    handleCredit(cardBean)
                }

                AccountInfoType.MarginAndFreeMargin -> {
                    handleMarginAndFreeMargin(cardBean)
                }

                AccountInfoType.Balance -> {
                    handleBalance(cardBean)
                }
            }
        }
        //通知ProductTitle更新账户信息
        updateTitleAccountInfo(mBinding.mAccountInfoView.getSelectedType())
    }

    /**
     * 净值
     */
    private fun handleEquity(cardBean: AccountInfoCardBean) {
        mBinding.mAccountInfoView.setEquityText(cardBean.equityUi)
    }

    /**
     * 浮动盈亏
     */
    private fun handleFloatingPnL(cardBean: AccountInfoCardBean) {
        mBinding.mAccountInfoView.setFloatingPnlColor(cardBean.toFloatingPnlColor(fragment.requireActivity()))
        mBinding.mAccountInfoView.setFloatingPnLText(
            "${if (cardBean.profit > 0) "+" else ""}${cardBean.profitUi}"
        )
    }

    /**
     * 预付款比率
     */
    private fun handleMarginLevel(cardBean: AccountInfoCardBean) {
        val marginLevel = cardBean.marginLevel
        mBinding.mAccountInfoView.apply {
            setMarginLevelText(if (marginLevel == 0.0) "--" else "${cardBean.marginLevelUi}%")
            setMarginLevelColor(cardBean.toMarginLevelColor(fragment.requireActivity()))
            setMarginRiskLevel(cardBean.toMarginRiskLevel())
        }
    }

    /**
     * 信用额度
     */
    private fun handleCredit(cardBean: AccountInfoCardBean) {
        mBinding.mAccountInfoView.setCreditText(cardBean.creditUi)
    }

    /**
     * 保证金和可用保证金
     */
    private fun handleMarginAndFreeMargin(cardBean: AccountInfoCardBean) {
        mBinding.mAccountInfoView.setMarginAndFreeMarginText("${cardBean.marginUi}/${cardBean.freeMarginUi}")
    }

    /**
     * 余额
     */
    private fun handleBalance(cardBean: AccountInfoCardBean) {
        mBinding.mAccountInfoView.setBalanceColor(cardBean.toBalanceColor(fragment.requireActivity()))
        mBinding.mAccountInfoView.setBalanceText(cardBean.balanceUi)
    }

    /**
     * 刷新产品对title中的账户信息
     */
    private fun updateTitleAccountInfo(type: AccountInfoType) {
        val text = type.getDisplayText(mBinding.mAccountInfoView)
        orderViewMode.changeAccountInfoItem(AccountInfoItemBean(type, text))
    }

    /**
     * 获取选中类型显示的文本
     */
    private fun AccountInfoType.getDisplayText(infoView: TradeAccountInfoView): String {
        return when (this) {
            AccountInfoType.Equity -> infoView.getEquityText()
            is AccountInfoType.FloatingPnL -> infoView.getFloatingPnLText()
            AccountInfoType.MarginLevel -> infoView.getMarginLevelText()
            AccountInfoType.Credit -> infoView.getCreditText()
            AccountInfoType.MarginAndFreeMargin -> infoView.getMarginAndFreeMarginText()
            AccountInfoType.Balance -> infoView.getBalanceText()
        }
    }

    /**
     * 术语表弹窗
     */
    private fun showGlossaryPop() {
        glossaryPopup = BottomDialog.Builder<LayoutBottomTipGlossaryBinding>(fragment.requireActivity())
            .setTitle(fragment.getString(R.string.glossary))
            .setViewBinding(LayoutBottomTipGlossaryBinding::inflate)
            .setMaxHeight((screenHeight * 0.86f).toInt())
            .build()
        glossaryView = glossaryPopup?.getContentViewBinding()
        glossaryView?.tvReviewTitle?.isVisible = true
        glossaryView?.tvReviewContent?.isVisible = true
        glossaryView?.tvLearnMore?.set(fragment.getString(R.string.learn_more)) {
            glossaryPopup?.dismiss()
            showMarginLevelPop()
        }
        glossaryView?.tvPnlContent?.text = fragment.getString(R.string.the_profit_and_open_positions_including_swap_rates)
        glossaryPopup?.show()
    }

    /**
     * 保证金水平弹窗
     */
    @SuppressLint("SetTextI18n")
    private fun showMarginLevelPop() {
        if (InitHelper.isNotSuccess()) {
            return
        }
        marginLevelPopup = BottomDialog.Builder<LayoutBottomTradeTipMarginLevelBinding>(fragment.requireActivity())
            .setTitle(fragment.requireActivity().getString(R.string.margin_level))
            .setViewBinding(LayoutBottomTradeTipMarginLevelBinding::inflate)
            .build()
        marginLevelView = marginLevelPopup?.getContentViewBinding()
        val marginLevelStr = fragment.requireActivity().getString(R.string.margin_level)
        val marginCallAddAHundredPercent = VAUSdkUtil.stShareAccountBean().marginCall.mathAdd("100")
        //低风险
        val textLowRiskSubtitle = "($marginLevelStr > $marginCallAddAHundredPercent\u200E%)"
        marginLevelView?.tvLowRiskSubtitle?.text = textLowRiskSubtitle
        marginLevelView?.tvLowRiskContent?.text = fragment.requireActivity().getString(R.string.the_account_has_of_liquidation)

        //中风险
        val textMediumRiskSubtitle = "(${VAUSdkUtil.stShareAccountBean().marginCall}\u200E% < $marginLevelStr ≤ $marginCallAddAHundredPercent\u200E%)"
        marginLevelView?.tvMediumRiskSubtitle?.text = textMediumRiskSubtitle
        marginLevelView?.tvMediumRiskContent?.text = fragment.requireActivity().getString(R.string.the_accounts_you_account_risk)

        //高风险
        val textHighRiskSubtitle = "(${VAUSdkUtil.stShareAccountBean().marginStopOut}\u200E% < $marginLevelStr ≤ ${VAUSdkUtil.stShareAccountBean().marginCall}\u200E%)"
        marginLevelView?.tvHighRiskSubtitle?.text = textHighRiskSubtitle
        marginLevelView?.tvHighRiskContent?.text = fragment.requireActivity().getString(R.string.the_accounts_available_to_you_some_positions)

        //Stop Out（爆仓）
        val textStopOutSubtitle = "($marginLevelStr ≤ ${VAUSdkUtil.stShareAccountBean().marginStopOut}\u200E%)"
        marginLevelView?.tvStopOutSubtitle?.text = textStopOutSubtitle
        marginLevelView?.tvStopOutContent?.text = fragment.requireActivity().getString(
            R.string.your_positions_will_x_starting_opened_positions,
            "${VAUSdkUtil.stShareAccountBean().marginStopOut}\u200E%"
        )
        marginLevelPopup?.show()
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        EventBus.getDefault().unregister(this)
    }

    // 显示用户信息（登陆根据账户类型显示，未登录显示登陆按钮）
    @SuppressLint("SetTextI18n")
    private fun showAccountInfo() {
        // 行情维护 全设置为“...”
        if (Constants.MARKET_MAINTAINING) {
            setAccountInfoOriginalState()
        } else {
            // 非返佣账号设置公共数据账户信息
            if (shouldSkipUpdate()) return
            handleIntervalCallback()
        }
    }

    /**
     * 账户信息发生变化时，刷新UI
     */
    private fun handleIntervalCallback() {
        val newCardBean = VAUSdkUtil.stShareAccountBean().copyData()
        val changedFields = diffUtil.calculateDiff(mCardBean, newCardBean)
        if (changedFields.isNotEmpty()) {
            mCardBean = newCardBean
            viewModel?.handleChangedFields(newCardBean, changedFields)
        }
    }

    /**
     * 账户信息设置为初始状态
     */
    private fun setAccountInfoOriginalState() {
        mCardBean = null
        mBinding.mAccountInfoView.setAccountInfoOriginalState()
        updateTitleAccountInfo(mBinding.mAccountInfoView.getSelectedType())
    }

    private fun shouldSkipUpdate(): Boolean {
        return fragment.requireActivity().isFinishing ||
                fragment.isHidden ||
                fragment.isDetached ||
                InitHelper.isNotSuccess() ||
                Constants.MARKET_MAINTAINING
    }

    @SuppressLint("SetTextI18n")
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            // 切换账户 || 退出登陆
            NoticeConstants.SWITCH_ACCOUNT, NoticeConstants.AFTER_LOGOUT_RESET -> {
                setAccountInfoOriginalState()
            }

            // 应用在后台放置超过一分钟
            NoticeConstants.APP_IN_BACKGROUND_MORE_THAN_1M -> {
                setAccountInfoOriginalState()
            }
        }
    }
}

/**
 * ViewModel
 */
class StAccountInfoViewModel : BaseViewModel() {
    private val _cardBeanFlow = MutableSharedFlow<AccountInfoCardBean>()
    val cardBeanFlow: SharedFlow<AccountInfoCardBean> = _cardBeanFlow.asSharedFlow()

    private var formatJob: Job? = null

    /**
     * 格式化需要更新的字段
     */
    fun handleChangedFields(cardBean: AccountInfoCardBean, changedFields: MutableSet<AccountInfoType>) {
        if (formatJob?.isActive == true) return
        formatJob = viewModelScope.launch {
            val updatedBean = withContext(Dispatchers.Default) {
                cardBean.copy().apply {
                    changedFields.forEach { field ->
                        applyFieldFormatting(field)
                    }
                }
            }
            updatedBean.changedFields = changedFields
            _cardBeanFlow.emit(updatedBean)
        }
    }
}