package cn.com.vau.trade.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import cn.com.vau.R
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.databinding.ViewTradeTypeBinding
import cn.com.vau.trade.data.ProductState
import cn.com.vau.trade.data.TradeOrderType
import cn.com.vau.trade.viewmodel.OrderViewModel.Companion.TRADE_BUY
import cn.com.vau.trade.viewmodel.OrderViewModel.Companion.TRADE_SELL
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.addComma
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.formatProductPrice

/**
 * Created by array on 2025/5/9 15:20
 * Desc: 订单类型（sell单、buy单）
 */
class TradeTypeView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val mBinding by lazy { ViewTradeTypeBinding.inflate(LayoutInflater.from(context), this) }

    private val colorTitleSelected by lazy { ContextCompat.getColor(context, R.color.cebffffff) }
    private val colorTitleUnSelected by lazy { AttrResourceUtil.getColor(context, R.attr.color_ca61e1e1e_c99ffffff) }
    private val colorTitleNoTrading by lazy { ContextCompat.getColor(context, R.color.cebffffff) }

    private val colorPriceSelected by lazy { AttrResourceUtil.getColor(context, R.attr.color_cffffff_cebffffff) }
    private val colorPriceUnSelected by lazy { AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cffffff) }
    private val colorPriceNoTradingSelected by lazy { ContextCompat.getColor(context, R.color.cebffffff) }
    private val colorPriceNoTradingUnSelected by lazy { AttrResourceUtil.getColor(context, R.attr.color_c731e1e1e_c61ffffff) }

    private val icon2BgSellUnselected by lazy { AttrResourceUtil.getDrawable(context, R.attr.icon2BgSellUnselected) }
    private val icon2BgSellNoTrading by lazy { AttrResourceUtil.getDrawable(context, R.attr.icon2BgSellNoTrading) }
    private val icon2BgBuyUnselected by lazy { AttrResourceUtil.getDrawable(context, R.attr.icon2BgBuyUnselected) }
    private val icon2BgBuyNoTrading by lazy { AttrResourceUtil.getDrawable(context, R.attr.icon2BgBuyNoTrading) }

    /**
     * 产品状态
     */
    private var productState: ProductState = ProductState.NoProduct

    /**
     * 交易类型
     */
    private var currentType: TradeOrderType = TradeOrderType.Sell

    /**
     * 交易类型回调
     * const val TRADE_SELL = "1"
     * const val TRADE_BUY = "0"
     */
    private var onSelectListener: ((String) -> Unit)? = null

    init {
        initView()
        initListener()
    }

    private fun initView() {
        selectedOrderType(currentType)
    }

    fun initListener() {
        mBinding.clSell.clickNoRepeat {
            selectedOrderType(TradeOrderType.Sell)
            onSelectListener?.invoke(TRADE_SELL)
        }

        mBinding.clBuy.clickNoRepeat {
            selectedOrderType(TradeOrderType.Buy)
            onSelectListener?.invoke(TRADE_BUY)
        }
    }

    /**
     * 设置产品状态(切换sell buy并不使用这个方法)
     */
    fun setProductState(productState: ProductState) {
        if (this.productState == productState) {
            return
        }
        this.productState = productState
        // 产品状态为 无产品 时，重置价格
        if (productState == ProductState.NoProduct) {
            resetPrice()
        }
        // 更新按钮样式
        setSellBuyStyle()
    }

    /**
     * 更新价格
     */
    fun updatePrice(productData: ShareProductData?) {
        if (productData == null || productData.bid <= 0f || productData.ask <= 0f) {
            resetPrice()
            return
        }
        OrderUtil.tradePriceTextTop(
            mBinding.tvSellPrice,
            productData.bid.formatProductPrice(productData.digits, false).addComma(productData.digits)
        )
        OrderUtil.tradePriceTextTop(
            mBinding.tvBuyPrice,
            productData.ask.formatProductPrice(productData.digits, false).addComma(productData.digits)
        )
        setSpread(productData.spreadUI)
    }

    /**
     * 重置价格
     */
    private fun resetPrice() {
        setSellPrice("--")
        setBuyPrice("--")
        setSpread("--")
    }

    @Suppress("SameParameterValue")
    private fun setSellPrice(sellPrice: String) {
        mBinding.tvSellPrice.text = sellPrice
    }

    @Suppress("SameParameterValue")
    private fun setBuyPrice(buyPrice: String) {
        mBinding.tvBuyPrice.text = buyPrice
    }

    private fun setSpread(spread: String) {
        mBinding.tvSpread.text = spread
    }

    fun setOnSelectListener(onSelectListener: (String) -> Unit) {
        this.onSelectListener = onSelectListener
    }

    /**
     * 设置订单类型buy 或 sell
     */
    fun selectedOrderType(tradeOrderType: TradeOrderType) {
        currentType = tradeOrderType
        setSellBuyStyle()
    }

    /**
     * 根据当前订单类型和产品状态设置按钮样式
     */
    private fun setSellBuyStyle() {
        val (sellStyle, buyStyle) = when (productState) {
            ProductState.FullAccess -> when (currentType) {
                TradeOrderType.Sell -> SellBuyStyle.Selected to SellBuyStyle.UnSelected
                else -> SellBuyStyle.UnSelected to SellBuyStyle.Selected
            }

            else -> when (currentType) {
                TradeOrderType.Sell -> SellBuyStyle.NoTradingSelected to SellBuyStyle.NoTradingUnSelected
                else -> SellBuyStyle.NoTradingUnSelected to SellBuyStyle.NoTradingSelected
            }
        }
        setSellButtonStyle(sellStyle)
        setBuyButtonStyle(buyStyle)
    }

    /**
     * 设置Sell按钮样式
     * 1、可交易，选中状态
     * 2、可交易，未选中状态
     * 3、不可交易，选中状态
     * 4、不可交易，未选中状态
     */
    private fun setSellButtonStyle(style: SellBuyStyle) {
        when (style) {
            SellBuyStyle.Selected -> {
                mBinding.clSell.setBackgroundResource(R.drawable.icon2_bg_sell_selected)
                mBinding.tvSell.setTextColor(colorTitleSelected)
                mBinding.tvSellPrice.setTextColor(colorPriceSelected)
            }

            SellBuyStyle.UnSelected -> {
                mBinding.clSell.setBackgroundResource(icon2BgSellUnselected)
                mBinding.tvSell.setTextColor(colorTitleUnSelected)
                mBinding.tvSellPrice.setTextColor(colorPriceUnSelected)
            }

            SellBuyStyle.NoTradingSelected -> {
                mBinding.clSell.setBackgroundResource(icon2BgSellNoTrading)
                mBinding.tvSell.setTextColor(colorTitleNoTrading)
                mBinding.tvSellPrice.setTextColor(colorPriceNoTradingSelected)
            }

            SellBuyStyle.NoTradingUnSelected -> {
                mBinding.clSell.setBackgroundResource(icon2BgSellUnselected)
                mBinding.tvSell.setTextColor(colorTitleUnSelected)
                mBinding.tvSellPrice.setTextColor(colorPriceNoTradingUnSelected)
            }

        }
    }

    /**
     * 设置Buy按钮样式
     * 1、可交易，选中状态
     * 2、可交易，未选中状态
     * 3、不可交易，选中状态
     * 4、不可交易，未选中状态
     */
    private fun setBuyButtonStyle(style: SellBuyStyle) {
        when (style) {
            SellBuyStyle.Selected -> {
                mBinding.clBuy.setBackgroundResource(R.drawable.icon2_bg_buy_selected)
                mBinding.tvBuy.setTextColor(colorTitleSelected)
                mBinding.tvBuyPrice.setTextColor(colorPriceSelected)
            }

            SellBuyStyle.UnSelected -> {
                mBinding.clBuy.setBackgroundResource(icon2BgBuyUnselected)
                mBinding.tvBuy.setTextColor(colorTitleUnSelected)
                mBinding.tvBuyPrice.setTextColor(colorPriceUnSelected)
            }

            SellBuyStyle.NoTradingSelected -> {
                mBinding.clBuy.setBackgroundResource(icon2BgBuyNoTrading)
                mBinding.tvBuy.setTextColor(colorTitleNoTrading)
                mBinding.tvBuyPrice.setTextColor(colorPriceNoTradingSelected)
            }

            SellBuyStyle.NoTradingUnSelected -> {
                mBinding.clBuy.setBackgroundResource(icon2BgBuyUnselected)
                mBinding.tvBuy.setTextColor(colorTitleUnSelected)
                mBinding.tvBuyPrice.setTextColor(colorPriceNoTradingUnSelected)
            }
        }
    }
}

/**
 * Buy、Sell 按钮样式
 * 1、可交易，选中状态
 * 2、可交易，未选中状态
 * 3、不可交易，选中状态
 * 4、不可交易，未选中状态
 */
sealed class SellBuyStyle {
    data object Selected : SellBuyStyle()
    data object UnSelected : SellBuyStyle()
    data object NoTradingSelected : SellBuyStyle()
    data object NoTradingUnSelected : SellBuyStyle()
}


