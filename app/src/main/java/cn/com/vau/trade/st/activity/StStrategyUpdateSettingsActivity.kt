package cn.com.vau.trade.st.activity

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import cn.com.vau.R
import cn.com.vau.common.base.mvvm.BaseMvvmActivity
import cn.com.vau.common.constants.*
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.*
import cn.com.vau.common.view.*
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.databinding.ActivityStStrategyUpdateSettingsBinding
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.page.html.HtmlActivity
import cn.com.vau.signals.stsignal.dialog.BottomGSTipDialog
import cn.com.vau.signals.stsignal.viewmodel.StStrategyCopyViewModel
import cn.com.vau.trade.st.StrategyOrderBaseData
import cn.com.vau.util.*
import cn.com.vau.util.widget.dialog.*
import org.greenrobot.eventbus.EventBus

/**
 * 更改设置
 */
class StStrategyUpdateSettingsActivity : BaseMvvmActivity<ActivityStStrategyUpdateSettingsBinding, StStrategyCopyViewModel>(), SDKIntervalCallback {

    override fun initViewModels(): StStrategyCopyViewModel = getActivityViewModel(StStrategyCopyViewModel::class.java)

    override fun getLayoutId(): Int = R.layout.activity_st_strategy_update_settings

    private val currencyType: String by lazy { UserDataUtil.currencyType() }
    private val copyMode by lazy {
        mutableListOf(
            getString(R.string.equivalent_used_margin), getString(R.string.fixed_lots), getString(R.string.fixed_multiples)
        )
    }
    private val verifyHandler by lazy { VerifyCompHandler() }
    private var strategyId: String? = null
    private var slValue: String = "35"
    private var tpValue: String = ""
    private var isExpand = false    // Risk Management 是否已经展开
    private var tpEnabled = false   // Take Profit 开关
    private var slippageProtectionStatus = true    // Slippage Protection 开关
    private var lotRoundUpStatus = true            // Lot Round-Up 开关
    private var formulaLotRoundUpStatus = true     // 等比例占用保证金模式下 Lot Round-Up 开关状态
    private var minLots: Double = 0.01                  // 最小固定手数
    private var minMultiples: Double = 0.1              // 最小固定手数倍数
    private var minUsedMarginMultiples: Double = 1.0    // 最小保证金倍数
    private var investmentAmount: Double = 0.00
    private var copyModeSelectedIndex = 0
    private var slippageProtectionVisible = false   // 0滑点开关是否展示

    private var portfolioId: String = ""

    override fun onCallback() {
        mViewModel.shareStrategyData?.let {
            val profit = it.totalHistoryProfit + it.profit
            val roi = profit.div(it.investmentAmount?.toDoubleCatching() ?: 0.0).times(100)
            mBinding.layoutStrategyHeader.tvRoiRate.text = if (it.investmentAmount.mathCompTo("0") == 1) {
                "${roi.numCurrencyFormat("2")}%"
            } else {
                "∞"
            }
            mBinding.layoutStrategyHeader.tvRoiRate.setTextColor(getReturnRateColor(roi))
        }
    }

    override fun initParam() {
        super.initParam()
        SDKIntervalUtil.instance.addCallBack(this)
        if (null != intent?.extras?.getSerializable("data_strategy")) {
            mViewModel.strategyOrderData = intent?.extras?.getSerializable("data_strategy") as? StrategyOrderBaseData
        }
        portfolioId = mViewModel.strategyOrderData?.portfolioId.ifNull()
        mViewModel.initShareFollowStrategyData()
    }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        super.initView()
        mBinding.layoutStrategyCopyMode.groupInvestment.isVisible = false
        mBinding.layoutStrategySwitch.groupCopyOpenTrade.isVisible = false
        mBinding.layoutStrategyRiskManagement.tvSLTitle.text = "${getString(R.string.majuscule_sl)}:"
        mBinding.layoutStrategyRiskManagement.tvTPTitle.text = "${getString(R.string.majuscule_tp)}:"
        mBinding.layoutStrategyRiskManagement.etStopLoss.hint = "5-95".arabicReverseTextByFlag("-")
        mBinding.layoutStrategyRiskManagement.etTakeProfit.hint = "5-95".arabicReverseTextByFlag("-")

        mBinding.layoutStrategyRiskManagement.etStopLoss.setRangeAndDecimalPlaces(5, 95)
        mBinding.layoutStrategyRiskManagement.etTakeProfit.setRangeAndDecimalPlaces(5, 95)
        observ()
    }

    override fun initData() {
        super.initData()
        mBinding.layoutStrategyRiskManagement.seekStopLoss.setMinMax(5, 95, 35, "-5%", "-95%", "")
        mBinding.layoutStrategyRiskManagement.seekTakeProfit.setMinMax(5, 95, 35, "5%", "95%", "35%")
        mViewModel.shareStrategyData?.let {
            val profit = it.totalSharedProfit + it.profit
            val roi = profit.div(it.investmentAmount?.toDoubleCatching() ?: 0.0).times(100)
            mBinding.layoutStrategyHeader.tvRoiRate.text = if (it.investmentAmount.mathCompTo("0") == 1) {
                "${roi.numCurrencyFormat("2")}%"
            } else {
                "∞"
            }
            mBinding.layoutStrategyHeader.tvRoiRate.setTextColor(getReturnRateColor(roi))
        }
        mViewModel.strategyCopySettings("Following", portfolioId.ifNull())
    }

    override fun initListener() {
        super.initListener()

        mBinding.mSmartRefreshLayout.setEnableLoadMore(false)
        mBinding.mSmartRefreshLayout.setOnRefreshListener {
            mBinding.mSmartRefreshLayout.finishRefresh(Constants.finishRefreshOrMoreTime)
        }

        mBinding.layoutStrategyHeader.tvProfitSharing.setOnClickListener(this)
        mBinding.layoutStrategyHeader.tvSettlement.setOnClickListener(this)
        mBinding.layoutStrategyCopyMode.tvCopyMode.setOnClickListener(this)
        mBinding.layoutStrategyCopyMode.ivCopyModeTip.setOnClickListener(this)
        mBinding.layoutStrategyCopyMode.etLots.onFocusChangeListener = onFocusChangeListener
        mBinding.layoutStrategyCopyMode.etMultiplesPerOrder.onFocusChangeListener = onFocusChangeListener
        mBinding.layoutStrategyCopyMode.etUsedMarginMultiples.onFocusChangeListener = onFocusChangeListener
        mBinding.layoutStrategyCopyMode.tvGSUserBalanceTip.setOnClickListener(this)
        mBinding.layoutStrategyRiskManagement.tvGSUserRiskTip.setOnClickListener(this)
        mBinding.layoutStrategyRiskManagement.tvRiskManagement.setOnClickListener(this)
        mBinding.layoutStrategyRiskManagement.ivRiskManagementTip.setOnClickListener(this)
        mBinding.layoutStrategyRiskManagement.etStopLoss.onFocusChangeListener = onFocusChangeListener
        mBinding.layoutStrategyRiskManagement.etTakeProfit.onFocusChangeListener = onFocusChangeListener
        mBinding.layoutStrategyRiskManagement.ivRiskManagementArrow.setOnClickListener(this)
        mBinding.layoutStrategySwitch.tvSlippageProtection.setOnClickListener(this)
        mBinding.layoutStrategySwitch.tvLotRoundUp.setOnClickListener(this)
        mBinding.layoutStrategySwitch.ivSlippageProtectionIcon.setOnClickListener(this)
        mBinding.layoutStrategySwitch.ivLotRoundUpIcon.setOnClickListener(this)
        // 监听键盘弹出和收起    0：收起
        KeyboardUtil.registerSoftInputChangedListener(this) {
            if (it == 0) {
                checkSLandTP()
            }
        }
        mBinding.layoutStrategyRiskManagement.etStopLoss.doAfterTextChanged {
            slValue = it.ifNull().toString()
            mBinding.layoutStrategyRiskManagement.tvSLValue.text = "${slValue}%"
            mBinding.layoutStrategyRiskManagement.seekStopLoss.setProgress(slValue.toIntCatching())
            calcStopLossAmount()
        }
        mBinding.layoutStrategyRiskManagement.etTakeProfit.doAfterTextChanged {
            tpValue = it.ifNull().toString()
            mBinding.layoutStrategyRiskManagement.tvTPValue.text = if (tpValue.isEmpty()) "--" else "${tpValue}%"
            mBinding.layoutStrategyRiskManagement.seekTakeProfit.setProgress(tpValue.toIntCatching())
            calcTakeProfitAmount()
        }
        mBinding.layoutStrategyCopyMode.etMultiplesPerOrder.doAfterTextChanged {
            var input = it.ifNull().toString()
            if (input.startsWith(".")) {
                input = "0$input"
                mBinding.layoutStrategyCopyMode.etMultiplesPerOrder.setText(input)
                mBinding.layoutStrategyCopyMode.etMultiplesPerOrder.setSelection(input.length)
            }
            val num = input.toFloatCatching()
            val perOrder = if (num == 0f) "1" else num.toString()
            mBinding.layoutStrategyCopyMode.tvMultiplesPerOrderTip.text = getString(R.string.the_position_opened_x_the_strategy, perOrder)
        }
        mBinding.layoutStrategyCopyMode.etUsedMarginMultiples.doAfterTextChanged {
            var input = it.ifNull().toString()
            if (input.startsWith(".") || input.startsWith("0")) {
                input = ""
                mBinding.layoutStrategyCopyMode.etUsedMarginMultiples.setText(input)
            }
            val times = input.toFloatCatching().toString()
            mBinding.layoutStrategyCopyMode.tvUsedMarginMultiplesTip.text = getString(R.string.the_margin_percentage_x_strategys_default, times)
        }
        mBinding.layoutStrategyRiskManagement.seekStopLoss.setProgressCallBack {
            mViewModel.setSL(it.toString())
        }
        mBinding.layoutStrategyRiskManagement.seekTakeProfit.setProgressCallBack {
            mViewModel.setTP(it.toString())
        }
        mBinding.layoutStrategyRiskManagement.sbTakeProfit.setStateChangeListener { isChecked ->
            mViewModel.setTakeProfitEnabl(isChecked)
        }
        mBinding.layoutStrategySwitch.sbSlippageProtection.setOnClickListener {
            slippageProtectionStatus = !slippageProtectionStatus
            mBinding.layoutStrategySwitch.sbSlippageProtection.setState(slippageProtectionStatus)
        }
        mBinding.layoutStrategySwitch.sbLotRoundUp.setOnClickListener {
            if (copyModeSelectedIndex == 0) {
                lotRoundUpStatus = !lotRoundUpStatus
                formulaLotRoundUpStatus = lotRoundUpStatus
                mBinding.layoutStrategySwitch.sbLotRoundUp.setState(lotRoundUpStatus)
            } else {
                ToastUtil.showToast(getString(R.string.the_feature_is_used_margin_mode))
            }
        }
    }

    private fun setVerifyComponment() {
        val views = mutableListOf<VerifyComponent>()
        views.add(mBinding.layoutStrategyRiskManagement.etStopLoss)
        if (copyModeSelectedIndex == 1) {
            views.add(mBinding.layoutStrategyCopyMode.etLots)
        } else if (copyModeSelectedIndex == 2) {
            views.add(mBinding.layoutStrategyCopyMode.etMultiplesPerOrder)
        } else {
            views.add(mBinding.layoutStrategyCopyMode.etUsedMarginMultiples)
        }
        if (tpEnabled) {
            views.add(mBinding.layoutStrategyRiskManagement.etTakeProfit)
        }

        verifyHandler.add(views).to(mBinding.tvNext).submit {
            if (checkParameters()) {
                val lots = mBinding.layoutStrategyCopyMode.etLots.text.toString()
                val multiples = mBinding.layoutStrategyCopyMode.etMultiplesPerOrder.text.toString()
                val usedMarginMultiples = mBinding.layoutStrategyCopyMode.etUsedMarginMultiples.text.toString()

                val params = hashMapOf(
                    "portfolioId" to portfolioId,
                    "copyMode" to when (copyModeSelectedIndex) {
                        1 -> Constants.STRATEGY_COPY_MODE_FIXED_VOLUME
                        2 -> Constants.STRATEGY_COPY_MODE_FIXED_MAGNIFICATION
                        else -> Constants.STRATEGY_COPY_MODE_FORMULA
                    },
                    "copyModeValue" to when (copyModeSelectedIndex) {
                        1 -> lots.toDoubleCatching().toString()
                        2 -> multiples.toDoubleCatching().toString()
                        else -> usedMarginMultiples.toDoubleCatching().toString()
                    },
                    "stopLossPercentage" to slValue,
                    "takeProfitPercentage" to if (tpEnabled) tpValue else "",
                    "enableMinVol" to lotRoundUpStatus.toString(),
                )
                if (slippageProtectionVisible) {
                    params["slippageProtection"] = slippageProtectionStatus.toString()
                }
                mViewModel.strategyCopyUpdate(params)
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun observ() {
        // 加载数据接口
        mViewModel.strategyCopySettingLiveData.observe(this) {
            if (it.code != "200") {
                ToastUtil.showToast(it.msg)
                return@observe
            }
            if (it.data?.offLine == true) {
                CenterActionDialog.Builder(this)
                    .setContent(getString(R.string.this_strategy_has_signal_provider))
                    .setSingleButtonText(getString(R.string.ok))
                    .setSingleButton(true)
                    .setOnSingleButtonListener {
                        finish()
                    }
                    .build()
                    .showDialog()
                return@observe
            }
            val data = it.data
            mViewModel.profitShieldStrategy = data?.isProfitShieldStrategy == true
            ImageLoaderUtil.loadImage(this, data?.avatar.ifNull(), mBinding.layoutStrategyHeader.ivAvatar, R.mipmap.ic_launcher)
            mBinding.layoutStrategyHeader.tvNick.text = data?.nickname.ifNull()
            mBinding.layoutStrategyHeader.tvIdKey.text = "${getString(R.string.strategy_id)}："
            mBinding.layoutStrategyHeader.tvStrategyId.text = data?.strategyNo.ifNull()
            mBinding.layoutStrategyHeader.tvProfitSharingRate.text = "${data?.profitSharePercentage.ifNull().toString().percent(0)}%"
            mBinding.layoutStrategyHeader.tvSettlementValue.text = getSettlement(data?.settlementFrequency.ifNull())
            // 跟随资金
            investmentAmount = data?.totalInvestment.ifNull()
            // 固定手数：0.01 和 策略门槛之间的最大值
            minLots = 0.01.coerceAtLeast(data?.minFollowVolume.ifNull())
            mBinding.layoutStrategyCopyMode.etLots.setText("$minLots")
            mBinding.layoutStrategyCopyMode.etLots.hint = "$minLots-100".arabicReverseTextByFlag("-")
            mBinding.layoutStrategyCopyMode.etLots.setRangeAndDecimalPlaces(minLots, 100)
            // 固定倍数：1 和 策略门槛之间的最大值
            minMultiples = 0.1.coerceAtLeast(data?.minFollowMultiplier.ifNull())
            mBinding.layoutStrategyCopyMode.etMultiplesPerOrder.setText(minMultiples.formatProductPrice(1, false))
            mBinding.layoutStrategyCopyMode.etMultiplesPerOrder.hint = "$minMultiples-50.0".arabicReverseTextByFlag("-")
            mBinding.layoutStrategyCopyMode.etMultiplesPerOrder.setRangeAndDecimalPlaces(minMultiples, 50.0, 1)
            // 等比例占用保证金：1 和 策略门槛之间的最大值
            minUsedMarginMultiples = 1.0.coerceAtLeast(data?.minLotsRatioPerOrder.ifNull())
            mBinding.layoutStrategyCopyMode.etUsedMarginMultiples.setText(minUsedMarginMultiples.formatProductPrice(1, false))
            mBinding.layoutStrategyCopyMode.etUsedMarginMultiples.hint = "$minUsedMarginMultiples-50.0"
            mBinding.layoutStrategyCopyMode.etUsedMarginMultiples.setRangeAndDecimalPlaces(minUsedMarginMultiples, 50.0, 1)
            mBinding.layoutStrategyCopyMode.tvGSUserBalanceTip.text =
                getString(R.string.to_qualify_for_the_x_other_currencies, "${data?.eligibleFundLevel.ifNull()} USD")
            mBinding.layoutStrategyCopyMode.tvGSUserBalanceTip.isVisible = mViewModel.profitShieldStrategy

            strategyId = data?.strategyId.ifNull()
            // Copy Mode
            copyModeSelectedIndex = getCopyModeSelectedIndex(data?.copyMode)
            mBinding.layoutStrategyCopyMode.dropListView.setData(copyMode, copyModeSelectedIndex, getString(R.string.copy_mode)).onSelected { index ->
                mViewModel.setCopyMode(copyMode.elementAtOrNull(index).ifNull())
            }
            data?.copyModeValue.let { value ->
                if (copyModeSelectedIndex == 1) {
                    mBinding.layoutStrategyCopyMode.etLots.setText("${value.toDoubleCatching()}")
                } else if (copyModeSelectedIndex == 2) {
                    mBinding.layoutStrategyCopyMode.etMultiplesPerOrder.setText("${value.toFloatCatching()}")
                } else {
                    mBinding.layoutStrategyCopyMode.etUsedMarginMultiples.setText("${value.toFloatCatching()}")
                }
            }
            slValue = data?.stopLossPercentage.ifNullToInt(35).toString()
            mBinding.layoutStrategyRiskManagement.etStopLoss.setText(slValue)
            tpValue = if (data?.takeProfitPercentage == null) "" else data?.takeProfitPercentage.ifNullToInt(5).toString()
            tpEnabled = tpValue.isNotEmpty()
            mBinding.layoutStrategyRiskManagement.sbTakeProfit.setState(tpEnabled)
            mBinding.layoutStrategyRiskManagement.tvGSUserRiskTip.text =
                getString(R.string.to_qualify_for_the_above_x, "${data?.lossCoverPercentage.toFloatCatching().times(100).toInt()}%")
            mBinding.layoutStrategyRiskManagement.tvGSUserRiskTip.isVisible =
                mViewModel.profitShieldStrategy && mBinding.layoutStrategyRiskManagement.groupSLandTP.isVisible
            mViewModel.setTakeProfitEnabl(tpEnabled)
            calcStopLossAmount()
            lotRoundUpStatus = data?.minVolRoundup.ifNull()
            formulaLotRoundUpStatus = lotRoundUpStatus
            mBinding.layoutStrategySwitch.sbLotRoundUp.setState(lotRoundUpStatus)
            // 0滑点开关
            slippageProtectionVisible = data?.slippageProtection.ifNull()
            mBinding.layoutStrategySwitch.groupSlippageProtection.isVisible = slippageProtectionVisible
            if (slippageProtectionVisible) {
                slippageProtectionStatus = data?.slippageProtectionStatus.ifNull(true)
                mBinding.layoutStrategySwitch.sbSlippageProtection.setState(slippageProtectionStatus)
            }
        }

        // 下单提交接口
        mViewModel.strategySettingSubmitLiveData.observe(this) {
            if (it.code != "200") {
                ToastUtil.showToast(it.msg)
                return@observe
            }
            // 记录成功提交时的跟随模式
            SpManager.putStrategyOrderCopyMode(
                when (copyModeSelectedIndex) {
                    1 -> Constants.STRATEGY_COPY_MODE_FIXED_VOLUME
                    2 -> Constants.STRATEGY_COPY_MODE_FIXED_MAGNIFICATION
                    else -> Constants.STRATEGY_COPY_MODE_FORMULA
                }
            )
            // 记录成功提交时的0滑点状态
            SpManager.putStrategyOrderSlippageProtectionStatus(slippageProtectionStatus)
            // 记录成功提交时"等比例占用保证金"模式的倍数
            if (copyModeSelectedIndex == 0) {
                val usedMarginMultiples = mBinding.layoutStrategyCopyMode.etUsedMarginMultiples.text.toString()
                SpManager.putFormulaModeValueByUser(usedMarginMultiples.toFloatCatching().toString())
            }

            CenterActionWithIconDialog.Builder(this)
                .setLottieIcon(R.raw.lottie_dialog_ok)
                .setTitle(getString(R.string.update_successful))
                .setSingleButtonText(getString(R.string.ok))
                .setSingleButton(true)
                .setOnSingleButtonListener {
                    EventBus.getDefault().post(NoticeConstants.STStrategy.CHANGE_OF_ST_COPY_TRADING_ORDERS)
                    finish()
                }
                .build()
                .showDialog()
        }

        // 选择Copy Mode
        mViewModel.currentMode.observe(this) {
            when (it) {
                getString(R.string.equivalent_used_margin) -> {
                    copyModeSelectedIndex = 0
                    mBinding.layoutStrategyCopyMode.groupUsedMarginMultiples.isVisible = true
                    mBinding.layoutStrategyCopyMode.groupLotsPerOrder.isVisible = false
                    mBinding.layoutStrategyCopyMode.groupMultiplesPerOrder.isVisible = false
                    // 产品要求切换回此模式时，恢复之前在此模式下选中的开关状态
                    lotRoundUpStatus = formulaLotRoundUpStatus
                    mBinding.layoutStrategySwitch.sbLotRoundUp.setState(lotRoundUpStatus)
                    setVerifyComponment()
                }

                getString(R.string.fixed_lots) -> {
                    copyModeSelectedIndex = 1
                    mBinding.layoutStrategyCopyMode.groupUsedMarginMultiples.isVisible = false
                    mBinding.layoutStrategyCopyMode.groupLotsPerOrder.isVisible = true
                    mBinding.layoutStrategyCopyMode.groupMultiplesPerOrder.isVisible = false
                    lotRoundUpStatus = false
                    mBinding.layoutStrategySwitch.sbLotRoundUp.setState(lotRoundUpStatus)
                    setVerifyComponment()
                }

                getString(R.string.fixed_multiples) -> {
                    copyModeSelectedIndex = 2
                    mBinding.layoutStrategyCopyMode.groupUsedMarginMultiples.isVisible = false
                    mBinding.layoutStrategyCopyMode.groupLotsPerOrder.isVisible = false
                    mBinding.layoutStrategyCopyMode.groupMultiplesPerOrder.isVisible = true
                    lotRoundUpStatus = false
                    mBinding.layoutStrategySwitch.sbLotRoundUp.setState(lotRoundUpStatus)
                    setVerifyComponment()
                }
            }
        }

        mViewModel.stoplossValue.observe(this) {
            mBinding.layoutStrategyRiskManagement.etStopLoss.clearFocus()
            mBinding.layoutStrategyRiskManagement.etStopLoss.setText(it)
        }

        mViewModel.takeProfitValue.observe(this) {
            mBinding.layoutStrategyRiskManagement.etTakeProfit.clearFocus()
            mBinding.layoutStrategyRiskManagement.etTakeProfit.setText(it)
        }

        // 开启TP 滑动按钮
        mViewModel.takeProfitEnable.observe(this) {
            tpEnabled = it
            setVerifyComponment()
            mBinding.layoutStrategyRiskManagement.clTakeProfitMask.alpha = if (it) 1f else 0.25f
            mBinding.layoutStrategyRiskManagement.etTakeProfit.isEnabled = it
            mBinding.layoutStrategyRiskManagement.seekTakeProfit.setEnable(it)
            tpValue = if (it) {
                tpValue.ifEmpty { "35" }
            } else {
                ""
            }
            mBinding.layoutStrategyRiskManagement.etTakeProfit.setText(tpValue)
        }
    }

    @SuppressLint("SetTextI18n")
    private fun calcStopLossAmount() {
        val investAmount = investmentAmount.toString()
        val amount = investAmount.mathMul(slValue.ifEmpty { "0" }).mathDiv("100", 2)
        mBinding.layoutStrategyRiskManagement.tvEstimatedLoss.text = "${getString(R.string.estimated_loss)}: ${amount.numCurrencyFormat(currencyType)} $currencyType"
    }

    @SuppressLint("SetTextI18n")
    private fun calcTakeProfitAmount() {
        val investAmount = investmentAmount.toString()
        val amount = investAmount.mathMul(tpValue.ifEmpty { "0" }).mathDiv("100", 2)
        mBinding.layoutStrategyRiskManagement.tvEstimatedProfit.text = "${getString(R.string.estimated_profit)}: ${amount.numCurrencyFormat(currencyType)} $currencyType"
    }

    private fun getCopyModeSelectedIndex(copyMode: String?): Int {
        return copyMode?.let {
            when (it) {
                Constants.STRATEGY_COPY_MODE_FIXED_VOLUME -> 1
                Constants.STRATEGY_COPY_MODE_FIXED_MAGNIFICATION -> 2
                else -> 0
            }
        } ?: 0
    }

    private fun getSettlement(settlement: Int): String {
        return when (settlement) {
            1 -> getString(R.string.daily)
            2 -> getString(R.string.weekly)
            3 -> getString(R.string.monthly)
            else -> ""
        }
    }

    private fun checkSLandTP() {
        if (mBinding.layoutStrategyRiskManagement.etStopLoss.text.toString().toIntCatching() < 5) {
            mBinding.layoutStrategyRiskManagement.etStopLoss.setText("5")
        }
        if (tpEnabled) {
            if (mBinding.layoutStrategyRiskManagement.etTakeProfit.text.toString().toIntCatching() < 5) {
                mBinding.layoutStrategyRiskManagement.etTakeProfit.setText("5")
            }
        }
        window?.decorView?.clearFocus()
    }

    private val onFocusChangeListener = View.OnFocusChangeListener { v, hasFocus ->
        v?.setBackgroundResource(
            if (hasFocus)
                R.drawable.draw_shape_stroke_c1e1e1e_cebffffff_solid_c0a1e1e1e_c262930_r10
            else
                R.drawable.draw_shape_c0a1e1e1e_c262930_r10
        )
    }

    private fun getReturnRateColor(value: Double) = when {
        value >= 0 -> ContextCompat.getColor(this, R.color.c00c79c)
        else -> ContextCompat.getColor(this, R.color.ce35728)
    }

    private fun checkParameters(): Boolean {
        val lots = mBinding.layoutStrategyCopyMode.etLots.text.toString()
        val multiples = mBinding.layoutStrategyCopyMode.etMultiplesPerOrder.text.toString()
        val usedMarginMultiples = mBinding.layoutStrategyCopyMode.etUsedMarginMultiples.text.toString()
        val slScale = slValue.toIntCatching()
        val tpScale = tpValue.toIntCatching()

        if (copyModeSelectedIndex == 1) {
            if (lots.toDoubleCatching() < minLots) {
                ToastUtil.showToast(getString(R.string.please_enter_a_valid_value))
                return false
            }
        } else if (copyModeSelectedIndex == 2) {
            if (multiples.toDoubleCatching() < minMultiples) {
                ToastUtil.showToast(getString(R.string.please_enter_a_valid_value))
                return false
            }
        } else if (copyModeSelectedIndex == 0) {
            if (usedMarginMultiples.toDoubleCatching() < minUsedMarginMultiples) {
                mBinding.layoutStrategyCopyMode.etUsedMarginMultiples.setText("")
                ToastUtil.showToast(getString(R.string.please_enter_a_valid_value))
                return false
            }
        }
        if (slScale < 5 || slScale > 95) {
            ToastUtil.showToast(getString(R.string.stop_loss_should_be_5_and_95))
            return false
        }
        if (tpEnabled) {
            if (tpScale < 5 || tpScale > 95) {
                ToastUtil.showToast(getString(R.string.take_profit_should_be_5_and_95))
                return false
            }
        }

        return true
    }

    override fun onClick(view: View?) {
        super.onClick(view)
        when (view?.id) {
            R.id.tvProfitSharing -> {   // 跳转分润示例页
                openActivity(HtmlActivity::class.java, Bundle().apply {
                    putInt("tradeType", 24)
                })
            }

            R.id.tvSettlement -> {
                BottomInfoListDialog.Builder(this)
                    .setTitle(getString(R.string.settlement_frequency))
                    .setDataList(arrayListOf(HintLocalData(getString(R.string.the_profit_sharing_amount_settlement_cycle))))
                    .build()
                    .show()
            }

            R.id.tvCopyMode, R.id.ivCopyModeTip -> {
                BottomInfoListDialog.Builder(this)
                    .setTitle(getString(R.string.copy_mode))
                    .setDataList(
                        arrayListOf(
                            HintLocalData(getString(R.string.equivalent_used_margin), getString(R.string.about_copy_mode_equivalent_used_margin)),
                            HintLocalData(getString(R.string.fixed_lots), getString(R.string.about_copy_mode_fixed_lots)),
                            HintLocalData(getString(R.string.fixed_multiples), getString(R.string.about_copy_mode_fixed_multiples))
                        )
                    )
                    .build()
                    .show()
            }

            R.id.tvGSUserBalanceTip -> {
                BottomGSTipDialog.Builder(this, BottomGSTipDialog.DialogFrom.BALANCE)
                    .setTitle(getString(R.string.growth_shield_fund_eligible_funds))
                    .build()
                    .showDialog()
            }

            R.id.tvGSUserRiskTip -> {
                BottomGSTipDialog.Builder(this, BottomGSTipDialog.DialogFrom.STOPLOSS)
                    .setTitle(getString(R.string.growth_shield_can_for_example))
                    .build()
                    .showDialog()
            }

            R.id.tvRiskManagement, R.id.ivRiskManagementTip -> {
                BottomInfoListDialog.Builder(this)
                    .setTitle(getString(R.string.risk_management))
                    .setDataList(
                        arrayListOf(
                            HintLocalData(getString(R.string.stop_loss), getString(R.string.stop_loss_tips)),
                            HintLocalData(getString(R.string.take_profit), getString(R.string.set_the_take_when_the_stop_copying))
                        )
                    )
                    .build()
                    .show()
            }

            R.id.tvSlippageProtection, R.id.ivSlippageProtectionIcon -> {
                BottomInfoListDialog.Builder(this)
                    .setTitle(getString(R.string.slippage_protection))
                    .setDataList(arrayListOf(HintLocalData(getString(R.string.with_the_slippage_copiers_reducing_minor_type_variations))))
                    .build()
                    .show()
            }

            R.id.tvLotRoundUp, R.id.ivLotRoundUpIcon -> {
                BottomInfoListDialog.Builder(this)
                    .setTitle(getString(R.string.lot_round_up))
                    .setDataList(arrayListOf(HintLocalData(getString(R.string.lot_round_up_upon_you_and_selected_strategy))))
                    .build()
                    .show()
            }

            R.id.ivRiskManagementArrow -> {
                isExpand = !isExpand
                mBinding.layoutStrategyRiskManagement.groupSLandTP.isVisible = !isExpand
                mBinding.layoutStrategyRiskManagement.tvGSUserRiskTip.isVisible =
                    mViewModel.profitShieldStrategy && mBinding.layoutStrategyRiskManagement.groupSLandTP.isVisible
                mBinding.layoutStrategyRiskManagement.clSLandTP.isVisible = isExpand
                mBinding.layoutStrategyRiskManagement.ivRiskManagementArrow.rotation = if (isExpand) 180f else 0f
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        KeyboardUtil.unregisterSoftInputChangedListener(this.window)
        SDKIntervalUtil.instance.removeCallBack(this)
    }

}