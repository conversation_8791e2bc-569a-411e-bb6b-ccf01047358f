package cn.com.vau.trade.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.*
import android.widget.*
import androidx.appcompat.widget.AppCompatImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.utils.*
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.trade.activity.NewModifyOrderActivity
import cn.com.vau.util.*
import cn.com.vau.util.widget.DashedTextView
import java.security.AccessController.getContext
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 挂单的适配器
 * 多品牌 order -- 挂单
 * 跟单   order -- 挂单
 * 多品牌 kline -- 挂单
 * 跟单   kline -- 挂单
 */
@SuppressLint("SetTextI18n")
class PendingOrderAdapter(
    val mContext: Context,
    val orderList: CopyOnWriteArrayList<ShareOrderData>
) : RecyclerView.Adapter<PendingOrderAdapter.ViewHolder>() {
    private val c00c79c by lazy { ContextCompat.getColor(mContext, R.color.c00c79c) }
    private val cf44040 by lazy { ContextCompat.getColor(mContext, R.color.cf44040) }
    private val color_c1f1e1e1e_c1fffffff by lazy { AttrResourceUtil.getColor(mContext, R.attr.color_c1f1e1e1e_c1fffffff) }
    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(mContext, R.attr.color_c1e1e1e_cebffffff) }
    private val volume by lazy { mContext.getString(R.string.volume) }
    private val lot by lazy { mContext.getString(R.string.lots) }

    var isKline = false

    @SuppressLint("InflateParams")
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {

        val holder = ViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.item_recycler_pending_orders, parent, false)
        )

        holder.ivKLine.clickNoRepeat {
            mOnItemClickListener?.onStartKLine(holder.bindingAdapterPosition)
        }
        holder.tvOrderId.clickNoRepeat {
            mOnItemClickListener?.onOrderIdClick(orderList.getOrNull(holder.bindingAdapterPosition)?.order.ifNull())
        }
        holder.ivTpSlEdit.clickNoRepeat {
            mOnItemClickListener?.onModifyClick(holder.bindingAdapterPosition)
        }
        holder.tvModify.clickNoRepeat {
            mOnItemClickListener?.onModifyClick(holder.bindingAdapterPosition)
        }
        holder.tvDelete.clickNoRepeat {
            mOnItemClickListener?.onDeleteClick(holder.bindingAdapterPosition)
        }

        return holder
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        val data = orderList.getOrNull(position) ?: return

        holder.ivKLine.isVisible = isKline.not()

        // 产品名称
        holder.tvProdName.setTextDiff(data.symbol.ifNull())

        // 订单号
        holder.tvOrderId.setTextDiff("#${data.order.ifNull()}")

        val isBuyOrder = OrderUtil.isBuyOfOrder(data.cmd)
        if (isBuyOrder) {
            holder.tvOrderDirection.setTextDiff("Buy")
            holder.tvOrderDirection.setTextColorDiff(c00c79c)
            holder.tvOrderDirection.background = ContextCompat.getDrawable(mContext, R.drawable.shape_c1f00c79c_r4)
        } else {
            holder.tvOrderDirection.setTextDiff("Sell")
            holder.tvOrderDirection.setTextColorDiff(cf44040)
            holder.tvOrderDirection.background = ContextCompat.getDrawable(mContext, R.drawable.shape_c1ff44040_r4)
        }

        // 买卖类型
        holder.tvOrderType.setTextDiff(
            when (data.cmd) {
                "2", "3" -> "Limit"
                "4", "5" -> "Stop"
                "6", "7" -> "Stop Limit"
                else -> ""
            },
        )

        // 创建时间
        holder.tvCreateTime.setTextDiff(data.openTimeStr.ifNull())

        // 手数，范围，挂单价格
        // 手数
        if (PositionDataUtil.isAmountOpen()) {
            holder.tvVolTitle.text = "$volume (${UserDataUtil.currencyType()})"
            holder.tvVolume.setTextDiff(data.volumeAmount.ifNull())
        } else {
            holder.tvVolTitle.text = "$volume ($lot)"
            holder.tvVolume.setTextDiff(data.volume.ifNull())
        }

        val isStopLimit = ("6" == data.cmd || "7" == data.cmd)
        if (OrderUtil.isStopOfOrder(data.cmd)) {
            holder.tvConditions.setTextDiff(
                "${if (isBuyOrder) "≥ " else "≤ "}${data.openPrice.ifNull().addComma(data.digits)}",
            )
            holder.tvPendingPrice.setTextDiff(
                if (isStopLimit) {
                    data.stopLimitPrice.ifNull().addComma(data.digits)
                } else {
                    mContext.getString(R.string.market)
                },
            )
        } else {
            holder.tvConditions.setTextDiff("--")
            holder.tvPendingPrice.setTextDiff(data.openPrice.ifNull().addComma(data.digits))
        }

        val tp = data.takeProfit?.ifNullToDouble()
        val sl = data.stopLoss?.ifNullToDouble()
        holder.clSetTpSl.isVisible = (0.0 != tp || 0.0 != sl)
        if (0.0 == tp) {
            holder.tvTp.setTextDiff("--")
            holder.tvTp.setTextColorDiff(color_c1e1e1e_cebffffff)
        } else {
            holder.tvTp.setTextDiff(data.takeProfit.ifNull().addComma(data.digits))
            holder.tvTp.setTextColorDiff(c00c79c)
        }
        if (0.0 == sl) {
            holder.tvSl.setTextDiff("--")
            holder.tvSl.setTextColorDiff(color_c1e1e1e_cebffffff)
        } else {
            holder.tvSl.setTextDiff(data.stopLoss.ifNull().addComma(data.digits))
            holder.tvSl.setTextColorDiff(cf44040)
        }

        // 现价，距离成交
        holder.tvCurrentPrice.setTextDiff(data.currentPriceUI.ifNull().addComma(data.digits))
        holder.tvPriceGap.setTextDiff(data.gapPriceUI.ifNull().addComma(data.digits))

        holder.offView.setBackgroundColor(color_c1f1e1e1e_c1fffffff)
    }

    override fun getItemCount(): Int = orderList.size

    class ViewHolder(
        view: View,
    ) : RecyclerView.ViewHolder(view) {
        val tvProdName: TextView = view.findViewById(R.id.tvProdName)
        val ivKLine: ImageView = view.findViewById(R.id.ivKLine)
        val tvOrderId: DashedTextView = view.findViewById(R.id.tvOrderId)

        val tvOrderDirection: TextView = view.findViewById(R.id.tvOrderDirection)
        val tvOrderType: TextView = view.findViewById(R.id.tvOrderType)
        val tvCreateTime: TextView = view.findViewById(R.id.tvCreateTime)

        val tvVolTitle: TextView = view.findViewById(R.id.tvVolTitle)
        val tvVolume: TextView = view.findViewById(R.id.tvVolume)
        val tvConditionsTitle: TextView = view.findViewById(R.id.tvConditionsTitle)
        val tvConditions: TextView = view.findViewById(R.id.tvConditions)
        val tvPendingPriceTitle: TextView = view.findViewById(R.id.tvPendingPriceTitle)
        val tvPendingPrice: TextView = view.findViewById(R.id.tvPendingPrice)

        val tvCurrentPriceTitle: TextView = view.findViewById(R.id.tvCurrentPriceTitle)
        val tvCurrentPrice: TextView = view.findViewById(R.id.tvCurrentPrice)
        val tvPriceGapTitle: TextView = view.findViewById(R.id.tvPriceGapTitle)
        val tvPriceGap: TextView = view.findViewById(R.id.tvPriceGap)

        val clSetTpSl: ConstraintLayout = view.findViewById(R.id.clSetTpSl)
        val tvTpSlTitle: TextView = view.findViewById(R.id.tvTpSlTitle)
        val ivTpSlEdit: AppCompatImageView = view.findViewById(R.id.ivTpSlEdit)
        val tvSl: TextView = view.findViewById(R.id.tvSl)
        val tvTpSlOff: TextView = view.findViewById(R.id.tvTpSlOff)
        val tvTp: TextView = view.findViewById(R.id.tvTp)

        val tvModify: TextView = view.findViewById(R.id.tvModify)
        val tvDelete: TextView = view.findViewById(R.id.tvDelete)

        val offView: View = view.findViewById(R.id.offView)
    }

    private var mOnItemClickListener: OnItemClickListener? = null

    interface OnItemClickListener {
        fun onStartKLine(position: Int)

        fun onOrderIdClick(orderId: String)

        fun onModifyClick(position: Int)

        fun onDeleteClick(position: Int)
    }

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        mOnItemClickListener = onItemClickListener
    }
}
