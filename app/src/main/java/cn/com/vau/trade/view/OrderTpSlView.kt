package cn.com.vau.trade.view

import android.annotation.SuppressLint
import android.content.Context
import android.text.Editable
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import cn.com.vau.MainActivity
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.databinding.OrderTpSlViewBinding
import cn.com.vau.trade.data.ProductState
import cn.com.vau.trade.viewmodel.OrderViewModel
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.addComma
import cn.com.vau.util.arabicReverseTextByFlag
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.ifNull
import cn.com.vau.util.mathCompTo

class OrderTpSlView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val mBinding by lazy {
        OrderTpSlViewBinding.inflate(LayoutInflater.from(context), this)
    }

    private val icon2CbSquareUncheck by lazy {
        AttrResourceUtil.getDrawable(context,R.attr.icon2CbSquareUncheck)
    }

    val c00c79c by lazy {
        context.getColor(R.color.c00c79c)
    }
    val cf44040 by lazy {
        context.getColor(R.color.cf44040)
    }
    val color_cebffffff_c1e1e1e by lazy {
        AttrResourceUtil.getColor(context,R.attr.color_cebffffff_c1e1e1e)
    }

    var mViewModel:OrderViewModel? = null

    init {
        initView()
        initListener()
    }

    fun initOrderTpSlView(viewModel: OrderViewModel) {
        this.mViewModel = viewModel
        createObserver()
    }

    private fun createObserver() {
        if (mViewModel == null) return
        if (context !is MainActivity) return

        val activity = context as MainActivity
        mViewModel?.productDataChangeLieData?.observe(activity){
            resetTpSlInputValue()
        }

        //产品交易状态变化
        mViewModel?.productStateLiveData?.observe(activity) {
            if (it == ProductState.NoProduct) {
                resetTpSlInputValue()
            }
        }
        //止盈非法提示
       mViewModel?.takeProfitCheckLiveData?.observe(activity) {
           mBinding.tvTPTip.isVisible = it.isNullOrEmpty().not()
           mBinding.tvTPTip.text = it
           setTpInputViewBg()
       }

        //止损非法提示
        mViewModel?.stopLessCheckLiveData?.observe(activity) {
            mBinding.tvSLTip.isVisible = it.isNullOrEmpty().not()
            mBinding.tvSLTip.text = it
            setSlInputViewBg()
        }

        mViewModel?.refreshProductDataLiveData?.observe(activity) {
            //检查止盈止损是否合法
            if (mBinding.inputViewTp.etInputIsVisible()) {
                mViewModel?.checkTakeProfit(mBinding.inputViewTp.getInputText())
            }
            if (mBinding.inputViewSl.etInputIsVisible()) {
                mViewModel?.checkStopLess(mBinding.inputViewSl.getInputText())
            }
            if (mBinding.clSlEstimated.isVisible) {
                showSlEstimated()
            }
            if (mBinding.clTpEstimated.isVisible) {
                showTpEstimated()
            }

        }
    }

    private fun initView() {
        mBinding.inputViewTp.setInputTitleText(context.getString(R.string.take_profit))
        mBinding.inputViewSl.setInputTitleText(context.getString(R.string.stop_loss))

    }

    private fun initListener() {

        //输入止盈初始化值
        mBinding.inputViewTp.onInitInput = {
            showTpInitValue()
        }
        //输入止损初始化值
        mBinding.inputViewSl.onInitInput = {
            showSlInitValue()
        }

        //止盈输入框变化
        mBinding.inputViewTp.onTextChanged = { edt ->
            checkTakeProfitDigits(edt,true)
        }
        //止损输入框变化
        mBinding.inputViewSl.onTextChanged = { edt ->
            checkTakeProfitDigits(edt,false)
        }

        //tp 加
        mBinding.inputViewTp.onAddClick = {text ->
            val addEdText = mViewModel?.addTakeProfit(text).ifNull(text)
            mBinding.inputViewTp.setEtInputValue(addEdText)
        }
        //tp 减
        mBinding.inputViewTp.onSubClick = {text ->
            val subEdText = mViewModel?.subTakeProfit(text).ifNull(text)
            mBinding.inputViewTp.setEtInputValue(subEdText)

        }

        //sl 加
        mBinding.inputViewSl.onAddClick = {text ->
            val addEdText = mViewModel?.addStopLoss(text).ifNull(text)
            mBinding.inputViewSl.setEtInputValue(addEdText)
        }
        //sl 减
        mBinding.inputViewSl.onSubClick = {text ->
            val addEdText = mViewModel?.subStopLoss(text).ifNull(text)
            mBinding.inputViewSl.setEtInputValue(addEdText)
        }

        //止盈焦点改变
        mBinding.inputViewTp.onFocus = { hasFocus ->
            mBinding.inputViewTp.isSelected = hasFocus
            if (hasFocus) {
                showTpEstimated()
            } else {
                mBinding.inputViewTp.setEtInputValue(mViewModel?.formatTpSl(mBinding.inputViewTp.getInputText()) ?: "0")
            }
        }
       //止损焦点改变
        mBinding.inputViewSl.onFocus = { hasFocus ->
            mBinding.inputViewSl.isSelected = hasFocus
            if (hasFocus) {
                showSlEstimated()
            } else {
                mBinding.inputViewSl.setEtInputValue(mViewModel?.formatTpSl(mBinding.inputViewSl.getInputText())?:"0")
            }
        }

        //打开或关闭止盈止损
        mBinding.ivSwitch.clickNoRepeat {
            toggleTpSl()
        }
        //打开或关闭止盈止损
        mBinding.tvTpSl.clickNoRepeat {
            toggleTpSl()
        }
    }

    /**
     * 切换止盈止损打开或者关闭
     */
    private fun toggleTpSl() {
        mViewModel?.tpSlIsCheckEd =  mViewModel?.tpSlIsCheckEd.ifNull().not()
        if (mViewModel?.tpSlIsCheckEd == true) {
            mBinding.ivSwitch.setImageResource(R.drawable.icon2_cb_square)
            mBinding.gpTpSl.isVisible = true
        } else {
            mBinding.ivSwitch.setImageResource(icon2CbSquareUncheck)
            resetTpSlInputValue()
            mBinding.gpTpSl.isVisible = false
            mBinding.gpOther.isVisible = false
            setTpInputViewBg()
            setSlInputViewBg()
        }
    }

    /**
     * 重置止盈止损输入框
     */
    fun resetTpSlInputValue(){
        mBinding.inputViewTp.setEtInputValue("")
        mBinding.inputViewSl.setEtInputValue("")
        mBinding.inputViewTp.resetInputView()
        mBinding.inputViewSl.resetInputView()
        mBinding.inputViewTp.clearEtFocus()
        mBinding.inputViewSl.clearEtFocus()

    }

    fun clearEtFocus() {
        mBinding.inputViewTp.clearEtFocus()
        mBinding.inputViewSl.clearEtFocus()
    }

    /**
     * tp 初始值
     */
    private fun showTpInitValue() {
        //计算止盈止损范围
        mViewModel?.initTpSlRange()
    }

    /**
     * sl 初始值
     */
    private fun showSlInitValue() {
        //计算止盈止损范围
        mViewModel?.initTpSlRange()
    }

    /**
     * 止盈、止损价格小数位检查
     */
    private fun checkTakeProfitDigits(edt: Editable,isTP:Boolean) {
        val temp = edt.toString()
        if (temp.contains(".")) {
            val posDot = temp.indexOf(".")
            if (temp.length - posDot - 1 > mViewModel?.digits.ifNull()) {
                edt.delete(posDot + mViewModel?.digits.ifNull() + 1, posDot + 2 + mViewModel?.digits.ifNull())
            }
            if (posDot > 9 && posDot - 1 in 0..edt.length && posDot in 0..edt.length)
                edt.delete(posDot - 1, posDot)
        } else {
            if (temp.length > 9)
                edt.delete(temp.length - 1, temp.length)
        }
        if (isTP) {
            mViewModel?.checkTakeProfit(edt.toString())
            showTpEstimated()
        }else{
            mViewModel?.checkStopLess(edt.toString())
            showSlEstimated()
        }

    }

    /**
     * 市价计算预估盈利
     */
    @SuppressLint("SetTextI18n")
    private fun showTpEstimated() {
        mBinding.clTpEstimated.isVisible = mBinding.inputViewTp.isFocus() && mBinding.inputViewTp.getInputText().isEmpty().not() && mBinding.tvTPTip.isVisible.not()
        if (mBinding.clTpEstimated.isVisible.not()) {
            return
        }
        var plStr = mViewModel?.getTpEstimated(mBinding.inputViewTp.getInputText())
        var textColor = color_cebffffff_c1e1e1e
        if (-1 == plStr.mathCompTo("0")) {
            textColor = cf44040
        }
        if (1 == plStr.mathCompTo("0")) {
            plStr = "+$plStr"
            textColor = c00c79c
        }
        mBinding.tvTpEstimatedValue.setTextColor(textColor)
        mBinding.tvTpEstimatedValue.text = " ${plStr?.addComma()?:"0"} ${UserDataUtil.currencyType()}".arabicReverseTextByFlag(" ")
    }
    /**
     * 市价计算预估盈利
     */
    @SuppressLint("SetTextI18n")
    private fun showSlEstimated() {
        mBinding.clSlEstimated.isVisible = mBinding.inputViewSl.isFocus() && mBinding.inputViewSl.getInputText().isEmpty().not() && mBinding.tvSLTip.isVisible.not()
        if (mBinding.clSlEstimated.isVisible.not()) {
            return
        }
        var plStr = mViewModel?.getTpEstimated(mBinding.inputViewSl.getInputText())
        var textColor = color_cebffffff_c1e1e1e
        if (-1 == plStr.mathCompTo("0")) {
            textColor = cf44040
        }
        if (1 == plStr.mathCompTo("0")) {
            plStr = "+$plStr"
            textColor = c00c79c
        }
        mBinding.tvSlEstimatedValue.setTextColor(textColor)
        mBinding.tvSlEstimatedValue.text = " ${plStr?.addComma()?:"0"} ${UserDataUtil.currencyType()}".arabicReverseTextByFlag(" ")
    }

    /**
     * tp 是否不合法
     * return： true 不合法
     */
    fun tpTipsIsInvalid(): Boolean {
        return mBinding.tvTPTip.isVisible
    }

    /**
     * sl 是否不合法
     * return： true 不合法
     */
    fun slTipsIsInvalid(): Boolean {
        return mBinding.tvSLTip.isVisible
    }

    private fun setTpInputViewBg() {
        if (mBinding.tvTPTip.isVisible){
            mBinding.inputViewTp.setBackgroundResource(R.drawable.draw_shape_stroke_cf44040_solid_c0a1e1e1e_c262930_r6)
            return
        }
        mBinding.inputViewTp.setBackgroundResource(R.drawable.select_open_order_et_bg)
    }
    private fun setSlInputViewBg() {
        if (mBinding.tvSLTip.isVisible){
            mBinding.inputViewSl.setBackgroundResource(R.drawable.draw_shape_stroke_cf44040_solid_c0a1e1e1e_c262930_r6)
            return
        }
        mBinding.inputViewSl.setBackgroundResource(R.drawable.select_open_order_et_bg)
    }

}