package cn.com.vau.trade.view

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat
import cn.com.vau.R
import cn.com.vau.util.dp2px

class RatioLineView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private var leftRatio = 0.5f
    private val redPaint = Paint()
    private val greenPaint = Paint()
    private val margin = 2.dp2px()
    private var leftWidth = 0f

    init {
        // 初始化画笔
        redPaint.color = ContextCompat.getColor(context, R.color.cf44040)
        greenPaint.color = ContextCompat.getColor(context, R.color.c00c79c)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        // 绘制左侧绿色区域
        canvas.drawRoundRect(0f, 0f, leftWidth, height.toFloat(), 3f, 3f, greenPaint)
        // 绘制右侧红色区域
        canvas.drawRoundRect(if (leftWidth == 0f) 0f else leftWidth + margin, 0f, width.toFloat(), height.toFloat(), 3f, 3f, redPaint)
    }

    private fun setRatio(left: Float) {
        leftRatio = left
        leftWidth = width * left
        invalidate()
    }

    /**
     * 带动画的平滑比例过渡
     */
    fun animateRatio(left: Float, duration: Long = 400) {
        val animator = ValueAnimator.ofFloat(0f, 1f).apply {
            this.duration = duration
            addUpdateListener { animation ->
                val fraction = animation.animatedValue as Float
                // 线性插值过渡
                val currentLeft = leftRatio * (1 - fraction) + left * fraction
                setRatio(currentLeft)
            }
        }
        animator.start()
    }
}
