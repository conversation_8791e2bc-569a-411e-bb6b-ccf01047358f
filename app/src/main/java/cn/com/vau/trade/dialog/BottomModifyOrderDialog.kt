package cn.com.vau.trade.dialog

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.text.TextUtils
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintSet
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.performance.PerformManager
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.databinding.DialogBottomDialogModifyOrderBinding
import cn.com.vau.trade.ext.findHasFocusEditText
import cn.com.vau.trade.ext.getBottomToScreen
import cn.com.vau.trade.perform.ModifyAtPricePerformance
import cn.com.vau.trade.perform.ModifyLimitPricePerformance
import cn.com.vau.trade.perform.ModifyNextPerformance
import cn.com.vau.trade.perform.ModifyOrderInfoPerformance
import cn.com.vau.trade.perform.ModifyStopLossPerformance
import cn.com.vau.trade.perform.ModifyTakeProfitPerformance
import cn.com.vau.trade.viewmodel.BottomModifyOrderViewModel
import cn.com.vau.util.KeyboardUtil
import cn.com.vau.util.LogUtil
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.dp2px
import cn.com.vau.util.getNavigationBarHeight
import cn.com.vau.util.widget.dialog.base.BaseMvvmBottomDialog
import cn.com.vau.util.widget.dialog.base.IBuilder
import cn.com.vau.util.widget.dialog.base.IDialog
import com.google.gson.JsonObject
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlin.math.pow

/**
 * Created by array on 2025/5/27 13:47
 * Desc: 改单弹窗
 */
@SuppressLint("ViewConstructor")
class BottomModifyOrderDialog private constructor(
    activity: FragmentActivity,
    private val orderBean: ShareOrderData,
    val useInnerRequest: Boolean = false,
    private val onNextClick: ((params: JsonObject) -> Unit)? = null,
) : BaseMvvmBottomDialog<DialogBottomDialogModifyOrderBinding, BottomModifyOrderViewModel>(
    activity = activity,
    title = activity.getString(R.string.modify_orders),
    viewBinding = DialogBottomDialogModifyOrderBinding::inflate,
) {

    private val performManager: PerformManager by lazy {
        PerformManager(this)
    }

    /**
     * 订单信息
     */
    private val orderInfoPerformance: ModifyOrderInfoPerformance by lazy {
        ModifyOrderInfoPerformance(activity, this, mContentBinding, mViewModel)
    }

    /**
     * 挂单价格（Stop Price or Limit Price）
     */
    private val stopPricePerformance: ModifyAtPricePerformance by lazy {
        ModifyAtPricePerformance(activity, this, mContentBinding, mViewModel) {
            nextPerformance.atPriceSetEnable = it
            nextPerformance.updateNextStyle()
        }
    }

    /**
     * 限价价格（Limit Price）
     */
    private val limitPricePerformance: ModifyLimitPricePerformance by lazy {
        ModifyLimitPricePerformance(activity, this, mContentBinding, mViewModel) {
            nextPerformance.limitPriceSetEnable = it
            nextPerformance.updateNextStyle()
        }
    }

    /**
     * 止盈item
     */
    private val takeProfitPerformance: ModifyTakeProfitPerformance by lazy {
        ModifyTakeProfitPerformance(activity, this, mContentBinding, mViewModel) {
            nextPerformance.tpSetEnable = it
            nextPerformance.updateNextStyle()
        }
    }

    /**
     * 止损item
     */
    private val stopLossPerformance: ModifyStopLossPerformance by lazy {
        ModifyStopLossPerformance(activity, this, mContentBinding, mViewModel) {
            nextPerformance.slSetEnable = it
            nextPerformance.updateNextStyle()
        }
    }

    /**
     * next
     */
    private val nextPerformance: ModifyNextPerformance by lazy {
        ModifyNextPerformance(activity, this, mContentBinding, mViewModel) {
            onNextClick?.invoke(it)
        }
    }

    override fun useEventBus(): Boolean {
        return true
    }

    override fun useSDKIntervalUtil(): Boolean {
        return true
    }

    override fun initViewModel(): BottomModifyOrderViewModel {
        return ViewModelProvider(getMyViewModelStoreOwner())[BottomModifyOrderViewModel::class.java]
    }

    override fun onCallback() {
        updateMainOrderViewData()

    }

    override fun setContentView() {
        super.setContentView()
        initParam()
        initData()
        initView()
        initListener()
    }

    fun initParam() {
        mViewModel.orderBean = orderBean
    }

    @SuppressLint("SetTextI18n")
    fun initView() {
        addPerformances()
    }

    override fun onDismiss() {
        super.onDismiss()
        KeyboardUtil.hideSoftInput(getHostWindow())
    }

    private fun addPerformances() {
        performManager.addPerformance(orderInfoPerformance)
        performManager.addPerformance(stopPricePerformance)
        performManager.addPerformance(limitPricePerformance)
        performManager.addPerformance(takeProfitPerformance)
        performManager.addPerformance(stopLossPerformance)
        performManager.addPerformance(nextPerformance)
    }

    fun initData() {
        val data = VAUSdkUtil.symbolList().firstOrNull {
            it.symbol == orderBean.symbol
        } ?: return
        mViewModel.productBean = data
        mViewModel.digits = data.digits
        mViewModel.minProfit = "${1 / 10.toDouble().pow(mViewModel.digits.toDouble())}"
    }

    fun initListener() {
        mContentBinding.clContent.clickNoRepeat {
            KeyboardUtil.hideSoftInput(getHostWindow())
        }
        KeyboardUtil.registerSoftInputChangedListener(hostWindow) {
            LogUtil.i("zl_log", "键盘高度:${it}，导航条高度=${activity.getNavigationBarHeight()}")
            if (it == 0) {
                mContentBinding.viewTakeProfit.clearEditFocus()
                mContentBinding.viewStopLoss.clearEditFocus()
                mContentBinding.viewAtPrice.clearEditFocus()
                mContentBinding.viewLimitPrice.clearEditFocus()
            }
//            val inputHeight = it - 64.dp2px()
            val inputHeight = it
            constraintInputHeight(inputHeight)
            mContentBinding.nsvContent.findHasFocusEditText()?.let { view ->
                mContentBinding.nsvContent.post {
                    val bottomToScreen = (view.parent as ViewGroup).getBottomToScreen()
                    val scrollBy = bottomToScreen - inputHeight
                    if (scrollBy < 0) {
                        mContentBinding.nsvContent.smoothScrollBy(0, -scrollBy)
                    }
                }

            }
        }
        /** 关闭键盘-事件流 */
        lifecycleScope.launch {
            mViewModel.hideSoftInput.flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED)
                .collectLatest {
                    if (it) {
                        KeyboardUtil.hideSoftInput(hostWindow)
                    }
                }
        }

    }

    private fun constraintInputHeight(height: Int) {
        val set = ConstraintSet()
        set.clone(mContentBinding.clContent)
        set.constrainHeight(R.id.vSoftInputHeight, height)
        set.applyTo(mContentBinding.clContent)
    }

    /**
     * 更新主订单数据
     */
    @SuppressLint("SetTextI18n")
    fun updateMainOrderViewData() {
        val productData = VAUSdkUtil.symbolList().find {
            TextUtils.equals(it.symbol, orderBean.symbol)
        } ?: return
        mViewModel.productBean = productData
        performManager.onCallback()
    }

    @Suppress("unused")
    class Builder(
        activity: Activity,
        private val orderBean: ShareOrderData
    ) : IBuilder<DialogBottomDialogModifyOrderBinding, Builder>(activity) {
        private var onNextClick: ((params: JsonObject) -> Unit)? = null
        private var useInnerRequest: Boolean = false

        fun setUseInnerRequest(useInnerRequest: Boolean): Builder {
            this.useInnerRequest = useInnerRequest
            return this
        }

        fun setOnNextClick(onNextClick: ((params: JsonObject) -> Unit)?): Builder {
            this.onNextClick = onNextClick
            return this
        }

        override fun build(): BottomModifyOrderDialog {
            return super.build() as BottomModifyOrderDialog
        }

        override fun createDialog(context: Context): IDialog<DialogBottomDialogModifyOrderBinding> {
            return BottomModifyOrderDialog(context as FragmentActivity, orderBean, useInnerRequest, onNextClick)
        }

    }

}