package cn.com.vau.trade.dialog

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import cn.com.vau.R
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.ws.WsManager
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.common.utils.SDKIntervalUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.common.view.CustomTextWatcher
import cn.com.vau.data.BaseBean
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.databinding.DialogBottomClosePositionBinding
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.trade.viewmodel.ClosePositionConfirmData
import cn.com.vau.trade.viewmodel.ClosePositionViewModel
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.KeyboardUtil
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.arabicReverseTextByFlag
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.ifNull
import cn.com.vau.util.mathCompTo
import cn.com.vau.util.mathDiv
import cn.com.vau.util.mathMul
import cn.com.vau.util.numCurrencyFormat
import cn.com.vau.util.numFormat
import cn.com.vau.util.setSelectionEnd
import cn.com.vau.util.toFloatCatching
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.widget.dialog.CenterActionDialog
import cn.com.vau.util.widget.dialog.CenterActionWithIconDialog
import cn.com.vau.util.widget.dialog.base.BaseMvvmBottomDialog
import cn.com.vau.util.widget.dialog.base.BottomDialog
import cn.com.vau.util.widget.dialog.base.IBuilder
import cn.com.vau.util.widget.dialog.base.IDialog
import org.greenrobot.eventbus.EventBus

@SuppressLint("ViewConstructor")
class BottomClosePositionDialog private constructor(
    context: FragmentActivity,
    title: CharSequence?,
    var data: ShareOrderData?
) : BaseMvvmBottomDialog<DialogBottomClosePositionBinding, ClosePositionViewModel>(
    context,
    title,
    DialogBottomClosePositionBinding::inflate,
), SDKIntervalCallback {

    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff) }

    private var productData: ShareProductData? = null

    private val volumeWatcher: TextWatcher by lazy {
        object : CustomTextWatcher() {
            override fun afterTextChanged(edt: Editable) {
                val temp = edt.toString()
                if (temp.contains(".")) {
                    val posDot = temp.indexOf(".")
                    if (posDot <= 0) return
                    if (temp.length - posDot - 1 > 2) {
                        edt.delete(posDot + 3, posDot + 4)
                    }
                    if (posDot > 3)
                        edt.delete(posDot - 1, posDot)
                } else {
                    if (temp.length > 3)
                        edt.delete(temp.length - 1, temp.length)
                }
            }
        }
    }

    override fun setContentView() {
        super.setContentView()
        initData()
        initContentView()
        createObserver()
        initListener()
    }

    @SuppressLint("SetTextI18n")
    private fun initContentView() {
        mContentBinding.tvVolumeTitle.text = "${context.getString(R.string.volume)} (${context.getString(R.string.lots)})"
        data?.let {
            showOrderData(it)
        }
    }

    override fun useSDKIntervalUtil(): Boolean {
        return true
    }

    private fun initData() {
        productData = VAUSdkUtil.symbolList().firstOrNull {
            it.symbol == data?.symbol
        } ?: return
        mViewModel.data = data
        mViewModel.minVolume = productData?.minvolume ?: "0.01"
        mViewModel.stepVolume = productData?.stepvolume ?: "0.01"
        mViewModel.maxVolume = data?.volume ?: "0"
    }

    @SuppressLint("SetTextI18n")
    private fun showOrderData(data: ShareOrderData) {
        mContentBinding.tvSymbol.text = data.symbol.ifNull()
        mContentBinding.tvOrderNumber.text = "#${data.order.ifNull()}"
        mContentBinding.tvEntryPriceTitle.text = "${context.getString(R.string.entry_price).arabicReverseTextByFlag(" ")} (${data.priceCurrency.ifNull()})".arabicReverseTextByFlag(" ").ifNull()
        mContentBinding.tvEntryPrice.text = data.openPrice.numFormat(data.digits)

        mContentBinding.tvCurrentPriceTitle.text = "${context.getString(R.string.current_price).arabicReverseTextByFlag(" ")} (${data.priceCurrency.ifNull()})".arabicReverseTextByFlag(" ").ifNull()
//        mContentBinding.tvLiqPriceTitle.text = "${context.getString(R.string.liq_price)} (${UserDataUtil.currencyType()})"

        if (OrderUtil.isBuyOfOrder(data.cmd)) {
            mContentBinding.tvDirection.text = "B"
            mContentBinding.tvDirection.background = ContextCompat.getDrawable(context, R.drawable.shape_c00c79c_r4)
        } else {
            mContentBinding.tvDirection.text = "S"
            mContentBinding.tvDirection.background = ContextCompat.getDrawable(context, R.drawable.shape_cf44040_r4)
        }

        mContentBinding.etVolume.setText(data.volume)
        mContentBinding.tvPositionVolume.text = "${context.getString(R.string.lots)}"
        mContentBinding.tvPositionVolume2.text = "${data.volumeUI}"
        mContentBinding.volSeekBar.setProgress(100)
    }

    private fun createObserver() {
        mViewModel.closePositionSuccessLiveData.observe(this) {
            showSuccessDialog()
            EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_OPEN_ORDER)
            dismissDialog()
        }

        mViewModel.tradeOrdersCloseHintLiveData.observe(this) {
            showHintDialog(it)
        }

        mViewModel.tradeOrdersCloseCheckDelayLiveData.observe(this) {
            showCheckDelayDialog()
        }
    }

    @SuppressLint("SetTextI18n")
    private fun initListener() {
        mContentBinding.root.clickNoRepeat {
            KeyboardUtil.hideSoftInput(mContentBinding.etVolume)
            mContentBinding.etVolume.clearFocus()
        }

        mContentBinding.etVolume.addTextChangedListener(volumeWatcher)
        mContentBinding.etVolume.setOnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                mViewModel.isInputVolumeFromKeyBoard = true
                mContentBinding.volSeekBar.setProgress(0)
            }
        }

        mContentBinding.ivVolumeSub.clickNoRepeat {
            val editString = mContentBinding.etVolume.text.toString()
            mContentBinding.volSeekBar.setProgress(0)
            val editValue = mViewModel.volumeSub(editString)
            mContentBinding.etVolume.setText(editValue)
            mContentBinding.etVolume.setSelectionEnd()
        }

        mContentBinding.ivVolumeAdd.clickNoRepeat {
            val editString = mContentBinding.etVolume.text.toString()
            mContentBinding.volSeekBar.setProgress(0)
            val editValue = mViewModel.volumeAdd(editString)
            mContentBinding.etVolume.setText(editValue)
            mContentBinding.etVolume.setSelectionEnd()
            mContentBinding.volSeekBar.setProgress(0)
        }

        mContentBinding.volSeekBar.setOnSeekBarChangeListener { seekBar, progress, isStart ->
            if (isStart) {
                KeyboardUtil.hideSoftInput(mContentBinding.etVolume)
                mContentBinding.etVolume.clearFocus()
                mViewModel.isInputVolumeFromKeyBoard = false
                traceVolumeSeek()
            }
            if (mViewModel.isInputVolumeFromKeyBoard) return@setOnSeekBarChangeListener

            val orderData = data ?: return@setOnSeekBarChangeListener

            val value = orderData.volume.mathMul("${progress / 100f}")
            val finalValue = mViewModel.calculateVolume(value).numFormat(2)
            mContentBinding.etVolume.setText(finalValue)
            mContentBinding.etVolume.setSelectionEnd()
        }

        mContentBinding.clVolume.clickNoRepeat {
            KeyboardUtil.showSoftInput(mContentBinding.etVolume)
        }

        mContentBinding.tvNext.clickNoRepeat {
            val closeVolume = mContentBinding.etVolume.text.toString()
            if (TextUtils.isEmpty(closeVolume)) {
                // 输入手数有误，请重新输入
                ToastUtil.showToast(context.getString(R.string.number_of_lots_re_enter))
                traceConfirm()
                return@clickNoRepeat
            }
            val volumeNum = closeVolume.toFloatCatching()

            if (volumeNum == 0.0f || volumeNum > mViewModel.data?.volume.toFloatCatching() || (closeVolume.mathCompTo(mViewModel.data?.minvolume ?: "0") == -1 && closeVolume.mathCompTo(mViewModel.data?.volume) != 0)) {
                ToastUtil.showToast(context.getString(R.string.number_of_lots_re_enter))
                traceConfirm()
                return@clickNoRepeat
            }
            if ("0" == UserDataUtil.fastCloseState()) {
                dismissDialog()
                showClosePositionConfirmDialog()
                traceConfirm()
                return@clickNoRepeat
            }
            mViewModel.closeVolume = mContentBinding.etVolume.text.toString()
            if (UserDataUtil.isStLogin()) {
                mViewModel.stTradePositionClose()
            } else {
                mViewModel.tradeOrdersClose()
            }
            traceConfirm()
        }
    }

    private fun showHintDialog(data: BaseBean?) {
        val bean = data ?: return
        CenterActionDialog.Builder(context as Activity)
            .setContent(bean.info)
            .setSingleButton(true)
            .build()
            .showDialog()
    }

    private fun showCheckDelayDialog() {
        CenterActionDialog.Builder(context as Activity)
            // 当前市场波动较大，当前价格为 %s ，是否确定进行该操作？
            .setTitle(context.getString(R.string.do_you_wish_order_at_x, mViewModel.data?.closePrice))
            // 点击【确定】，将重新获取市场实时价格，因此操作造成损失将个人承担
            .setContent(context.getString(R.string.price_misquote_by_incurred))
            .setOnStartListener {
                WsManager.getInstance().resetConnect()
            }
            .setOnEndListener {
                mViewModel.tradeOrdersClose(0)
            }.build().showDialog()
    }

    private fun showSuccessDialog() {
        CenterActionWithIconDialog.Builder(context as Activity)
            .setTitle(context.getString(R.string.close_confirmed))
            .setLottieIcon(R.raw.lottie_dialog_ok)
            .setSingleButton(true)
            .setSingleButtonText(context.getString(R.string.ok))
            .build()
            .showDialog()
    }

    override fun onCallback() {
        refreshProductData()
    }

    @SuppressLint("SetTextI18n")
    private fun refreshProductData() {
        mContentBinding.tvCurrentPrice.text = mViewModel.getCurrentPrice()

        val orderData = VAUSdkUtil.shareOrderList().firstOrNull {
            it.order == mViewModel.data?.order
        } ?: return

        val profit = mViewModel.getProfit(orderData, mContentBinding.etVolume.text.toString().trim())

        mContentBinding.tvEstimatedPnl.text = "$profit ${UserDataUtil.currencyType()}".arabicReverseTextByFlag(" ").ifNull()
        val textColor = if (1 == profit.mathCompTo("0")) {
            ContextCompat.getColor(context, R.color.c00c79c)
        } else if (-1 == profit.mathCompTo("0") ) {
            ContextCompat.getColor(context, R.color.cf44040)
        } else {
            color_c1e1e1e_cebffffff
        }
        mContentBinding.tvEstimatedPnl.setTextColor(textColor)
    }

    private fun showClosePositionConfirmDialog() {
        BottomClosePositionConfirmDialog.Builder(context as Activity)
            .setTitle(context.getString(R.string.close_confirmation))
            .setData(ClosePositionConfirmData(mViewModel.data, mContentBinding.etVolume.text.toString()))
            .build()
            .showDialog()
    }

    override fun initViewModel(): ClosePositionViewModel {
        return ViewModelProvider(getMyViewModelStoreOwner())[ClosePositionViewModel::class.java]
    }

    override fun onDismiss() {
        super.onDismiss()
        mContentBinding.etVolume.removeTextChangedListener(volumeWatcher)
    }

    private fun traceVolumeSeek() {
        SensorsDataUtil.track(SensorsConstant.ORDER_POSITION.POSITIONCLOSEPOPUP_VOLUMECONTROL_CLICK)
    }

    private fun traceConfirm() {
        SensorsDataUtil.track(SensorsConstant.ORDER_POSITION.POSITIONCLOSEPOPUP_CONFIRM)
    }

    @Suppress("unused")
    class Builder(activity: Activity) :
        IBuilder<DialogBottomClosePositionBinding, Builder>(activity) {

        // 标题
        private var title: CharSequence? = null

        // 订单数据
        private var data: ShareOrderData? = null

        fun setTitle(title: CharSequence?) = apply {
            this.title = title
            return this
        }

        fun setData(data: ShareOrderData?) = apply {
            this.data = data
            return this
        }

        override fun build(): BottomClosePositionDialog {
            return super.build() as BottomClosePositionDialog
        }

        override fun createDialog(context: Context): IDialog<DialogBottomClosePositionBinding> {
            return BottomClosePositionDialog(
                context as FragmentActivity,
                title,
                data
            )
        }
    }
}