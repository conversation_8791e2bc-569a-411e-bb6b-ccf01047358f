package cn.com.vau.trade.perform

import android.annotation.SuppressLint
import android.text.TextUtils
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.performance.AbsPerformance
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.databinding.DialogBottomDialogModifyOrderBinding
import cn.com.vau.trade.dialog.BottomModifyOrderDialog
import cn.com.vau.trade.ext.isStopLimitOrder
import cn.com.vau.trade.ext.toStopOrLimitTag
import cn.com.vau.trade.viewmodel.BottomModifyOrderViewModel
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.PositionDataUtil
import cn.com.vau.util.arabicReverseTextByFlag
import cn.com.vau.util.ifNull
import cn.com.vau.util.numCurrencyFormat
import cn.com.vau.util.numFormat
import cn.com.vau.util.setTextColorDiff
import cn.com.vau.util.setTextDiff

/**
 * Created by array on 2025/5/27 17:23
 * Desc:订单信息
 */
class ModifyOrderInfoPerformance(
    val activity: FragmentActivity,
    val dialog: BottomModifyOrderDialog,
    val mBinding: DialogBottomDialogModifyOrderBinding,
    val mViewModel: BottomModifyOrderViewModel
) : AbsPerformance() {

    private val c00c79c by lazy { ContextCompat.getColor(activity, R.color.c00c79c) }
    private val cf44040 by lazy { ContextCompat.getColor(activity, R.color.cf44040) }
    private val color_c1f1e1e1e_c1fffffff by lazy { AttrResourceUtil.getColor(activity, R.attr.color_c1f1e1e1e_c1fffffff) }
    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(activity, R.attr.color_c1e1e1e_cebffffff) }
    private val volume by lazy { activity.getString(R.string.volume) }
    private val lot by lazy { activity.getString(R.string.lot) }

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        initData()
    }

    override fun onCallback() {
        updateDynamicData()
    }

    @SuppressLint("SetTextI18n")
    fun initData() {
        val orderBean = mViewModel.orderBean ?: return
        val productBean = mViewModel.productBean ?: return
        //产品名称
        mBinding.tvProdName.text = orderBean.symbol.ifNull()

        // 订单号
        mBinding.tvOrderId.setTextDiff("#${orderBean.order.ifNull()}")

        // 订单方向
        val isBuyOrder = OrderUtil.isBuyOfOrder(orderBean.cmd)
        if (isBuyOrder) {
            mBinding.tvOrderDirection.setTextDiff("Buy")
            mBinding.tvOrderDirection.setTextColorDiff(c00c79c)
            mBinding.tvOrderDirection.background = ContextCompat.getDrawable(activity, R.drawable.shape_c1f00c79c_r4)
        } else {
            mBinding.tvOrderDirection.setTextDiff("Sell")
            mBinding.tvOrderDirection.setTextColorDiff(cf44040)
            mBinding.tvOrderDirection.background = ContextCompat.getDrawable(activity, R.drawable.shape_c1ff44040_r4)
        }

        // 买卖类型
        mBinding.tvOrderType.setTextDiff(orderBean.toStopOrLimitTag())

        // 现价
        mBinding.tvCurrentPriceTitle.text = "${activity.getString(R.string.current_price)} (${productBean.priceCurrency})".arabicReverseTextByFlag(" ")
        setCurrentPrice(orderBean, productBean)

        // 手数
        if (PositionDataUtil.isAmountOpen()) {
            mBinding.tvVolTitle.text = "$volume (${UserDataUtil.currencyType()})".arabicReverseTextByFlag(" ")
            mBinding.tvVolume.setTextDiff(orderBean.volumeAmount.ifNull())
        } else {
            mBinding.tvVolTitle.text = "$volume ($lot)"
            mBinding.tvVolume.setTextDiff(orderBean.volume.ifNull())
        }

        val string = "${activity.getString(R.string.margin)}/${activity.getString(R.string.free_margin).arabicReverseTextByFlag(" ")}".arabicReverseTextByFlag("/")
        // 保证金
        mBinding.tvMarginTitle.text = "$string (${UserDataUtil.currencyType()})".arabicReverseTextByFlag(" ")
        setMargin(orderBean, productBean)
    }

    /**
     * 更新动态数据
     */
    @SuppressLint("SetTextI18n")
    fun updateDynamicData() {
        val orderBean = mViewModel.orderBean ?: return
        val productBean = VAUSdkUtil.symbolList().find {
            TextUtils.equals(it.symbol, orderBean.symbol)
        } ?: return
        mViewModel.productBean = productBean
        // 现价
        setCurrentPrice(orderBean, productBean)
        // 保证金
        setMargin(orderBean, productBean)
    }

    /**
     * 设置保证金
     */
    @SuppressLint("SetTextI18n")
    private fun setMargin(orderBean: ShareOrderData, productBean: ShareProductData) {

        val atPrice = mBinding.viewAtPrice.getPriceText()
        val limitPrice = mBinding.viewLimitPrice.getPriceText()
        val inputPrice = if (orderBean.isStopLimitOrder()) {
            limitPrice.ifEmpty { "0" }
        } else  {
            atPrice.ifEmpty { "0" }
        }
        val allMoney = OrderUtil.getRequiredMargin(
            productBean, orderBean.volume ?: "0",
            if (orderBean.cmd == "7" || orderBean.cmd == "5" || orderBean.cmd == "3") "bid" else "ask",
            inputPrice
        )
        mBinding.tvMargin.text = "${allMoney.numCurrencyFormat()}/"
        mBinding.tvFreeMargin.setTextDiff(mViewModel.getFreeMargin())
    }

    /**
     * 设置现价
     */
    private fun setCurrentPrice(orderBean: ShareOrderData, productBean: ShareProductData) {
        mBinding.tvCurrentPrice.setTextDiff(if ("-" == orderBean.closePrice) "-" else orderBean.currentPriceUI.numFormat(productBean.digits, false))
    }

}