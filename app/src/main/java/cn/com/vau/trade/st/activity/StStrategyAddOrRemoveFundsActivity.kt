package cn.com.vau.trade.st.activity

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.utils.*
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.databinding.ActivityStStrategyAddOrRemoveFundsBinding
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.signals.stsignal.dialog.BottomGSTipDialog
import cn.com.vau.trade.st.StrategyOrderBaseData
import cn.com.vau.trade.st.model.StStrategyAddOrRemoveViewModel
import cn.com.vau.util.*
import cn.com.vau.util.widget.dialog.*

/**
 * 正在跟随策略
 * 添加/减少 跟随资金
 * 添加资金 在3.53.0版本信用金需求中去掉了"可用余额"实时跳动的交互，与下单交互保持一致
 */
class StStrategyAddOrRemoveFundsActivity :
    BaseMvvmActivity<ActivityStStrategyAddOrRemoveFundsBinding, StStrategyAddOrRemoveViewModel>(), SDKIntervalCallback {

    private val currencyType: String by lazy { UserDataUtil.currencyType() }
    private val stShareAccountBean by lazy { VAUSdkUtil.stShareAccountBean() }

    private var minFollowAmount: Double = 0.0   // 上个页面传过来的跟单最小金额
    private var availableCredit: Float = 0f    // 可用信用金

    private val c00c79c by lazy { ContextCompat.getColor(this, R.color.c00c79c) }
    private val cf44040 by lazy { ContextCompat.getColor(this, R.color.cf44040) }

    @SuppressLint("SetTextI18n")
    override fun onCallback() {

        mViewModel.shareStrategyData?.let {
            val profit = it.totalHistoryProfit + it.profit
            val investmentAmount = it.investmentAmount?.toDoubleCatching() ?: 0.0
            val roi = if (investmentAmount == 0.0) 0.0 else profit.div(investmentAmount).times(100)
            mBinding.tvReturn.text = if (it.investmentAmount.mathCompTo("0") == 1) {
                "${roi.numCurrencyFormat("2")}%"
            } else {
                "∞"
            }
            mBinding.tvReturn.setTextColor(if (roi >= 0) c00c79c else cf44040)
        }

//        if (!mViewModel.isAdd) return

//        mViewModel.maxAllocatedMoney = VAUSdkUtil.stShareAccountBean().freeMargin.coerceAtLeast(0.00)
//        mBinding.tvAvailableInvestment.text = "${getString(R.string.available_investment)}: ${(mViewModel.maxAllocatedMoney).numCurrencyFormat(isRound = false)} $currencyType"

    }

    @SuppressLint("SetTextI18n")
    override fun initParam(savedInstanceState: Bundle?) {
        SDKIntervalUtil.instance.addCallBack(this)

        if (null != intent?.extras?.getSerializable("data_strategy")) {
            mViewModel.baseDataLiveData.value =
                intent?.extras?.getSerializable("data_strategy") as StrategyOrderBaseData
        }

        mViewModel.initShareFollowStrategyData()

        if (true == intent?.extras?.containsKey("SOURCE_TYPE")) {
            mViewModel.isAdd = "REMOVE" == intent?.extras?.getString("SOURCE_TYPE")
        }

        val minAmount = intent?.extras?.getString(Constants.STRATEGY_MIN_FOLLOW_AMOUNT, "").ifNull()
        minFollowAmount = minAmount.toDoubleCatching()

        mViewModel.minAllocatedMoney = when (currencyType) {
            "HKD" -> 400.00
            "JPY" -> 7000.00
            "USC", "INR" -> 4000.00
            else -> 50.00
        }
    }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        mBinding.mHeaderBar.setTitleText(
            getString(if (mViewModel.isAdd) R.string.add_funds else R.string.remove_funds)
        )

        mBinding.tvId.text = "${getString(R.string.strategy_id)}：${mViewModel.shareStrategyData?.strategyNo}".arabicReverseTextByFlag("：")

        mBinding.tvProfitSharing.text = "${(mViewModel.shareStrategyData?.profitShareRatio ?: "").mathMul("100").numFormat(0)}%"
        mBinding.tvMoneyAllocatedTitle.text = getString(if (mViewModel.isAdd) R.string.money_allocated else R.string.money_removed)

        mBinding.tvCurrency.text = currencyType

        mBinding.etMoney.hint = "${getString(R.string.min_dot)} ${(if (mViewModel.isAdd) mViewModel.minAllocatedMoney else 0.0).numCurrencyFormat()}"
        mBinding.etMoney.setCurrencyType(UserDataUtil.currencyType())
        mBinding.tvUsedCreditCurrency.text = currencyType
        mBinding.tvUsedBalanceCurrency.text = currencyType
        if (mViewModel.isAdd) {
            mBinding.tvUsedCredit.text = getString(R.string.used_credit)
            mBinding.tvUsedBalance.text = getString(R.string.used_balance)
            // Available Balance
            var availableBalance =
                // 有持仓情况
                if (VAUSdkUtil.shareOrderList().isNotEmpty()) {
                    CreditBalanceUtil.getAvailableBalance(stShareAccountBean.equity, stShareAccountBean.margin, stShareAccountBean.credit, stShareAccountBean.profit)
                }
                // 无持仓情况
                else {
                    stShareAccountBean.balance.coerceAtLeast(0.0)
                }
            val availableBalanceText = availableBalance.numCurrencyFormat(currencyType, false)
            val balanceArabic = "$availableBalanceText $currencyType".arabicReverseTextByFlag(" ")
            availableBalance = availableBalanceText.toDoubleCatching()
            mBinding.tvAvailableBalance.text = "${getString(R.string.available_balance)}: $balanceArabic".arabicReverseTextByFlag(": ")
            // Available Credit
            availableCredit = CreditBalanceUtil.getAvailableCredit(availableBalance, stShareAccountBean.equity, stShareAccountBean.credit)
            val availableCreditText = availableCredit.numCurrencyFormat(currencyType, false)
            val creditArabic = "$availableCreditText $currencyType".arabicReverseTextByFlag(" ")
            availableCredit = availableCreditText.toFloatCatching()
            mBinding.tvAvailableCredit.text = "${getString(R.string.available_credit)}: $creditArabic".arabicReverseTextByFlag(": ")
            // Available Investment
            val availableInvestment = availableBalance.mathAdd(availableCredit)
            mViewModel.maxAllocatedMoney = availableInvestment.coerceAtLeast(0.00)
            val investmentArabic = "${mViewModel.maxAllocatedMoney.numCurrencyFormat(currencyType)} $currencyType".arabicReverseTextByFlag(" ")
            mBinding.tvAvailableInvestment.text = "${getString(R.string.available_investment)}: $investmentArabic".arabicReverseTextByFlag(": ")
            // calc Used Credit / Used Balance
            calcUsedCreditAndBalance(stShareAccountBean.equity, stShareAccountBean.credit)
        } else {
            mBinding.tvUsedCredit.text = getString(R.string.removed_credit)
            mBinding.tvUsedBalance.text = getString(R.string.removed_balance)
            // Max Removable Investment
            val shareStrategyData = mViewModel.calcStrategyData
            val credit = shareStrategyData?.investmentCredit.toDoubleCatching()
            val balance = shareStrategyData?.balance.toDoubleCatching()
            val equity = balance.mathAdd(shareStrategyData?.profit.ifNull()).mathAdd(credit)
            val availableInvestmentA = CreditBalanceUtil.getMaxRemovableInvestmentA(equity, shareStrategyData?.marginUsed.ifNull())
            val availableInvestmentB = CreditBalanceUtil.getMaxRemovableInvestmentB(balance, credit, minFollowAmount)
            val maxRemovableInvestment = availableInvestmentA.coerceAtMost(availableInvestmentB)
            mViewModel.maxAllocatedMoney = maxRemovableInvestment.coerceAtLeast(0.00)
            val investmentArabic = "${mViewModel.maxAllocatedMoney.numCurrencyFormat(currencyType)} $currencyType".arabicReverseTextByFlag(" ")
            mBinding.tvAvailableInvestment.text = "${getString(R.string.max_removable_investment)}: $investmentArabic".arabicReverseTextByFlag(": ")
            // Removable Balance
            var removableBalance =
                // 判断一下信用金是否为0的情况，如果为0则不用公式去算(公式算可能会出现精度问题)
                if (credit == 0.0) {
                    maxRemovableInvestment.toFloat()
                } else {
                    CreditBalanceUtil.getRemovableBalance(maxRemovableInvestment, equity, credit)
                }
            val removableBalanceText = removableBalance.numCurrencyFormat(currencyType, false)
            val balanceArabic = "$removableBalanceText $currencyType".arabicReverseTextByFlag(" ")
            removableBalance = removableBalanceText.toFloatCatching()
            mBinding.tvAvailableBalance.text = "${getString(R.string.removable_balance)}: $balanceArabic".arabicReverseTextByFlag(": ")
            // Removable Credit
            val availableCredit = maxRemovableInvestment.mathSub(removableBalance).coerceAtLeast(0.00)
            val creditArabic = "${availableCredit.numCurrencyFormat(currencyType, false)} $currencyType".arabicReverseTextByFlag(" ")
            mBinding.tvAvailableCredit.text = "${getString(R.string.removable_credit)}: $creditArabic".arabicReverseTextByFlag(": ")
            // calc Used Credit / Used Balance
            calcUsedCreditAndBalance(equity, credit)
        }
    }

    override fun initData() {
        super.initData()
        // 获取 Settlement
        mViewModel.stStrategyCopySettingsApi()
    }

    @SuppressLint("SetTextI18n")
    override fun createObserver() {
        mViewModel.baseDataLiveData.observe(this) {
            ImageLoaderUtil.loadImage(this, it.profilePictureUrl, mBinding.ivHead, R.mipmap.ic_launcher)
            mBinding.tvName.text = it?.signalStrategyName ?: ""
        }

        mViewModel.stStrategyCopySettingsLiveData.observe(this) {

            if (null == it || 0.0 == it.minFollowVolume.ifNull()) {
                showDataExceptionDialog()
                return@observe
            }

            // Remove Funds时
//            if (!mViewModel.isAdd) {
//                val shareData = mViewModel.shareStrategyData
//                mViewModel.maxAllocatedMoney =
//                    shareData?.balance?.toDoubleOrNull().ifNull() + shareData?.profit.ifNull() - shareData?.marginUsed.ifNull() - it.minFollowAmount.ifNull()
//                // 负数置 0
//                mViewModel.maxAllocatedMoney = mViewModel.maxAllocatedMoney.coerceAtLeast(0.00)
//                mBinding.tvAvailableInvestment.text =
//                    "${if (mViewModel.isAdd) getString(R.string.available_investment) else getString(R.string.max_removable_investment)}: ${mViewModel.maxAllocatedMoney.toString().numCurrencyFormat(currencyType, false)} $currencyType"
//            }

            mBinding.tvSettlement.text = when (it.settlementFrequency) {
                1 -> getString(R.string.daily)
                2 -> getString(R.string.weekly)
                3 -> getString(R.string.monthly)
                else -> ""
            }

            mBinding.tvGSUserBalanceTip.text =
                getString(R.string.to_qualify_for_the_x_other_currencies, "${it?.eligibleFundLevel.ifNull()} USD")
            mBinding.tvGSUserBalanceTip.isVisible = it?.isProfitShieldStrategy == true
            mBinding.tvSl.text = "${getString(R.string.stop_loss)}: ${it?.stopLossPercentage.toIntCatching()}%".arabicReverseTextByFlag(":")

        }

        mViewModel.stTradeUpdateAllocationLiveData.observe(this) {
            CenterActionWithIconDialog.Builder(this)
                .setLottieIcon(R.raw.lottie_dialog_ok)
                .setTitle(getString(R.string.success))
                .setSingleButton(true)
                .setSingleButtonText(getString(R.string.ok))
                .setOnSingleButtonListener {
                    finish()
                }
                .build()
                .showDialog()
        }

    }

    override fun initListener() {
        super.initListener()

        mBinding.mSmartRefreshLayout.setEnableLoadMore(false)
        mBinding.mSmartRefreshLayout.setOnRefreshListener {
            mBinding.mSmartRefreshLayout.finishRefresh(Constants.finishRefreshOrMoreTime)
        }

        mBinding.tvProfitSharingTitle.setOnClickListener(this)
        mBinding.tvSettlementTitle.setOnClickListener(this)
        mBinding.tvAvailableInvestment.setOnClickListener(this)
        mBinding.tvUsedCredit.setOnClickListener(this)
        mBinding.tvUsedBalance.setOnClickListener(this)
        mBinding.tvGSUserBalanceTip.setOnClickListener(this)
        mBinding.tvNext.setOnClickListener(this)

        mBinding.etMoney.onFocusChangeListener = View.OnFocusChangeListener { v, hasFocus ->
            v?.setBackgroundResource(
                if (hasFocus)
                    R.drawable.draw_shape_stroke_c1e1e1e_cebffffff_r10
                else
                    R.drawable.draw_shape_c0a1e1e1e_c0affffff_r10
            )
        }

        mBinding.etMoney.doAfterTextChanged {
            val investAmount = it.ifNull().toString()
            if (investAmount.mathCompTo(mViewModel.maxAllocatedMoney.numCurrencyFormat(isRound = true)) == 1) {
                val maxInput = mViewModel.maxAllocatedMoney.numCurrencyFormat(currencyType, true)
                mBinding.etMoney.setText(maxInput)
                mBinding.etMoney.setSelection(maxInput.length)
            }
            if (mViewModel.isAdd) {
                calcUsedCreditAndBalance(stShareAccountBean.equity, stShareAccountBean.credit)
            } else {
                val shareStrategyData = mViewModel.calcStrategyData
                val credit = shareStrategyData?.investmentCredit.toDoubleCatching()
                val balance = shareStrategyData?.balance.toDoubleCatching()
                val equity = balance.mathAdd(shareStrategyData?.profit.ifNull()).mathAdd(credit)
                calcUsedCreditAndBalance(equity, credit)
            }
        }
        // 在设置监听之后赋值
        mBinding.etMoney.setText(if (mViewModel.isAdd) mViewModel.minAllocatedMoney.numCurrencyFormat() else "0".numCurrencyFormat())
    }

    override fun onClick(view: View?) {
        super.onClick(view)
        when (view?.id) {
            R.id.tvProfitSharingTitle -> {
                BottomInfoListDialog.Builder(this)
                    .setTitle(getString(R.string.profit_sharing_ratio))
                    .setDataList(arrayListOf(HintLocalData(getString(R.string.the_percentage_of_signal_provider))))
                    .build()
                    .show()
            }

            R.id.tvSettlementTitle -> {
                BottomInfoListDialog.Builder(this)
                    .setTitle(getString(R.string.settlement_frequency))
                    .setDataList(arrayListOf(HintLocalData(getString(R.string.the_profit_sharing_amount_settlement_cycle))))
                    .build()
                    .show()
            }

            R.id.tvAvailableInvestment -> {
                BottomInfoListDialog.Builder(this)
                    .setTitle(getString(if (mViewModel.isAdd) R.string.available_investment else R.string.max_removable_investment))
                    .setDataList(arrayListOf(HintLocalData(getString(if (mViewModel.isAdd) R.string.the_sum_of_level_requirement else R.string.the_sum_of_signal_provider))))
                    .build()
                    .show()
            }

            R.id.tvUsedCredit -> {
                BottomInfoListDialog.Builder(this)
                    .setTitle(getString(if (mViewModel.isAdd) R.string.used_credit else R.string.removed_credit))
                    .setDataList(arrayListOf(HintLocalData(getString(if (mViewModel.isAdd) R.string.the_use_of_the_equity_credit else R.string.the_removal_of_the_equity_credit))))
                    .build()
                    .show()
            }

            R.id.tvUsedBalance -> {
                BottomInfoListDialog.Builder(this)
                    .setTitle(getString(if (mViewModel.isAdd) R.string.used_balance else R.string.removed_balance))
                    .setDataList(arrayListOf(HintLocalData(getString(if (mViewModel.isAdd) R.string.based_on_the_the_the_if_the_the_strategy else R.string.based_on_the_the_the_credit_equity))))
                    .build()
                    .show()
            }

            R.id.tvGSUserBalanceTip -> {
                BottomGSTipDialog.Builder(this, BottomGSTipDialog.DialogFrom.BALANCE)
                    .setTitle(getString(R.string.growth_shield_fund_eligible_funds))
                    .build()
                    .showDialog()
            }

            R.id.tvNext -> {

                val submitMoney = mBinding.etMoney.text?.toString().ifNull()

                if (TextUtils.isEmpty(submitMoney) || submitMoney.mathCompTo("0") == 0) {
                    ToastUtil.showToast(getString(R.string.please_enter_a_valid_value))
                    return
                }

                if (mViewModel.isAdd && submitMoney.mathCompTo("${mViewModel.minAllocatedMoney}") == -1) {
                    mBinding.etMoney.setText(mViewModel.minAllocatedMoney.numCurrencyFormat())
                    mBinding.etMoney.setSelection(mBinding.etMoney.text.toString().length)
                    // 至少需要 %s
                    ToastUtil.showToast(
                        getString(
                            R.string.the_minimum_required_amount_is_x,
                            "${mViewModel.minAllocatedMoney.toString().numCurrencyFormat(currencyType)} $currencyType"
                        )
                    )
                    return
                }

                // 减少资金时，如果账户余额为负，则弹窗提示
                if (!mViewModel.isAdd && stShareAccountBean.balance < 0) {
                    CenterActionDialog.Builder(this)
                        .setContent(getString(R.string.cannot_remove_copy_you_stop_copying))
                        .setSingleButtonText(getString(R.string.ok))
                        .setSingleButton(true)
                        .build()
                        .showDialog()
                    return
                }

                if (!mViewModel.isAdd) {
                    CenterActionDialog.Builder(this)
                        .setTitle(getString(R.string.confirm_remove_funds))
                        .setContent(getString(R.string.this_action_will_any_be_deducted))
                        .setEndText(getString(R.string.confirm))
                        .setStartText(getString(R.string.cancel))
                        .setOnEndListener {
                            mViewModel.stTradeUpdateAllocationApi(submitMoney)
                        }
                        .build()
                        .showDialog()
                    return
                }

                mViewModel.stTradeUpdateAllocationApi(submitMoney)
            }

        }
    }

    private fun calcUsedCreditAndBalance(equity: Double, credit: Double) {
        val investmentText = mBinding.etMoney.text.toString().ifNull("0")
        val maxInput = mViewModel.maxAllocatedMoney.numCurrencyFormat(currencyType, true)
        val investment = if (investmentText.mathCompTo(maxInput) == 1) maxInput else investmentText
        var usedBalance = CreditBalanceUtil.getUsedBalance(investment, equity, credit)
        val usedBalanceText = usedBalance.numCurrencyFormat(currencyType, false)
        usedBalance = usedBalanceText.toFloatCatching()
        mBinding.tvUsedBalanceAmount.text = usedBalanceText
        val usedCredit = investment.mathSub(usedBalance.toString()).toDoubleCatching()
        mBinding.tvUsedCreditAmount.text = usedCredit.coerceAtLeast(0.00).numCurrencyFormat(currencyType, false)
        // 已用信用金 > 可用信息金 时，强制设置为可用信用金，并反计算已用余额
        if (mViewModel.isAdd) {
            if (usedCredit > availableCredit) {
                mBinding.tvUsedCreditAmount.text = availableCredit.coerceAtLeast(0f).numCurrencyFormat(currencyType, false)
                val usedBalanceNew = investmentText.mathSub(availableCredit.toString())
                mBinding.tvUsedBalanceAmount.text = usedBalanceNew.numCurrencyFormat(currencyType, false)
            }
        } else {
            if (usedCredit > credit) {
                mBinding.tvUsedCreditAmount.text = credit.coerceAtLeast(0.0).numCurrencyFormat(currencyType, false)
                val usedBalanceNew = investmentText.mathSub(credit.toString())
                mBinding.tvUsedBalanceAmount.text = usedBalanceNew.numCurrencyFormat(currencyType, false)
            }
        }
    }

    private fun showDataExceptionDialog() {
        CenterActionDialog.Builder(this)
            .setTitle(getString(R.string.data_exception_please_try_again_later))
            .setSingleButton(true)
            .setSingleButtonText(getString(R.string.ok))
            .setOnSingleButtonListener {
                finish()
            }
            .build()
            .showDialog()
    }

    override fun onDestroy() {
        super.onDestroy()
        SDKIntervalUtil.instance.removeCallBack(this)
    }

}