package cn.com.vau.trade.view

import android.annotation.SuppressLint
import android.content.Context
import android.text.Editable
import android.text.TextWatcher
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.annotation.ColorInt
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.databinding.TpSlItemViewBinding
import cn.com.vau.trade.ext.ComputeMode
import cn.com.vau.trade.ext.EditState
import cn.com.vau.trade.ext.checkTakeProfitDigits
import cn.com.vau.trade.ext.setDecimalFilter
import cn.com.vau.trade.ext.toDigits
import cn.com.vau.trade.ext.toEditStateDrawable
import cn.com.vau.trade.ext.toModeActionText
import cn.com.vau.trade.ext.toModeEditTitle
import cn.com.vau.util.numFormat
import cn.com.vau.util.setVisibilityDiff

/**
 * Created by array on 2025/2/17 16:00
 * Desc: 止盈止损 编辑item
 */
open class TakeProfitStopLossItemView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr), View.OnClickListener {

    private val mBinding: TpSlItemViewBinding by lazy {
        TpSlItemViewBinding.inflate(LayoutInflater.from(context), this)
    }

    private var orderBean: ShareOrderData? = null
    private var productData: ShareProductData? = null

    private var onPriceEditChange: ((CharSequence?) -> Unit)? = null
    private var onComputeModelEditChange: ((CharSequence?) -> Unit)? = null

    private var onTitleActionClick: ((currentMode: ComputeMode) -> Unit)? = null
    private var onSelectedItem: ((select: Boolean) -> Unit)? = null
    private var onPriceFocusChangeListener: ((hasFocus: Boolean) -> Unit)? = null
    private var onModeFocusChangeListener: ((hasFocus: Boolean) -> Unit)? = null

    private var subOperator: ((String, ComputeMode) -> Unit)? = null

    private var addOperator: ((String, ComputeMode) -> Unit)? = null

    private var isSelectedItem = false

    /*当前止盈止损计算方式*/
    private var currentComputeMode = ComputeMode.PNL

    private var digits = 0

    private lateinit var etPriceWatcher: ControllableTextWatcher
    private lateinit var etModeTextWatcher: ControllableTextWatcher
    private var isProcessingChange = false

    init {
        initWatchers()
        initView()
    }

    private fun initView() {

        mBinding.etPriceEdit.addTextChangedListener(etPriceWatcher)

        mBinding.etPriceEdit.setOnFocusChangeListener { _, hasFocus ->
            checkPriceEditFocus()
            formatPriceText()
            onPriceFocusChangeListener?.invoke(hasFocus)
        }

        mBinding.etComputeMode.setDecimalFilter()

        mBinding.etComputeMode.setOnFocusChangeListener { _, hasFocus ->
            mBinding.clSetting.isSelected = hasFocus
            onModeFocusChangeListener?.invoke(hasFocus)
        }

        mBinding.etComputeMode.addTextChangedListener(etModeTextWatcher)

        mBinding.ivTitleSelect.setOnClickListener(this)
        mBinding.tvTitle.setOnClickListener(this)
        mBinding.tvTitleAction.setOnClickListener(this)
        mBinding.ivPriceEditSub.setOnClickListener(this)
        mBinding.ivPriceEditAdd.setOnClickListener(this)
    }

    private fun initWatchers() {
        //价格输入框监听器
        etPriceWatcher = ControllableTextWatcher(active = true) { priceActive, editable ->

            val s = editable.toString()
            //小数位检查
            editable.checkTakeProfitDigits(digits)
            //输入框标题显示隐藏
            mBinding.tvPriceEditTitle.setVisibilityDiff(if (s.isEmpty()) View.GONE else View.VISIBLE)

            if (!priceActive) {
                return@ControllableTextWatcher
            }
            if (isProcessingChange) return@ControllableTextWatcher
            //价格输入框变化时，更新计算方式输入框内容（由于计算方式输入框内容变化，会更新价格输入框内容，所以需要临时禁止计算方式输入框监听变化）
            etModeTextWatcher.active = false
            onPriceEditChange?.invoke(s)//该回调中会更新计算方式输入框内容
            etModeTextWatcher.active = true
            isProcessingChange = false
        }

        //计算方式输入框监听器
        etModeTextWatcher = ControllableTextWatcher(active = true) { modeActive, editable ->
            //小数位检查
            val s = editable.toString()
            editable.checkTakeProfitDigits(currentComputeMode.toDigits())
            //输入框标题显示隐藏
            mBinding.tvComputeModeEditTitle.setVisibilityDiff(if (s.isEmpty()) View.GONE else View.VISIBLE)

            if (!modeActive) {
                return@ControllableTextWatcher
            }
            if (isProcessingChange) return@ControllableTextWatcher
            //计算方式输入框变化时，更新价格输入框内容（由于价格输入框内容变化，会更新计算方式输入框内容，所以需要临时禁止价格输入框监听变化）
            etPriceWatcher.active = false
            onComputeModelEditChange?.invoke(s)//该回调中会更新价格输入框内容
            etPriceWatcher.active = true
            isProcessingChange = false
        }
    }

    fun initData(productData: ShareProductData? = null, orderBean: ShareOrderData? = null) {
        this.productData = productData
        this.orderBean = orderBean
        this.digits = productData?.digits ?: 0
    }

    /**
     * 设置item标题
     */
    fun setTitle(title: String) {
        mBinding.tvTitle.text = title
    }

    /**
     * 选择设置止盈止损计算方式，按钮文案
     */
    private fun setTitleActionText(title: String) {
        mBinding.tvTitleAction.text = title
    }

    /**
     * 点击计算方式按钮回调
     */
    fun setOnTitleActionClickListener(listener: (currentMode: ComputeMode) -> Unit) {
        onTitleActionClick = listener
    }

    /**
     * 计算方式输入框焦点变化回调
     */
    fun setOnModeFocusChangeListener(listener: (hasFocus: Boolean) -> Unit) {
        onModeFocusChangeListener = listener
    }

    /**
     *  价格输入框焦点变化回调
     */
    fun setOnPriceFocusChangeListener(listener: (hasFocus: Boolean) -> Unit) {
        onPriceFocusChangeListener = listener
    }

    /**
     * 价格编辑框标题
     */
    fun setPriceEditTitle(title: String) {
        mBinding.tvPriceEditTitle.text = title
    }

    /**
     * 价格编辑框提示
     */
    fun setPriceEditHint(hint: String) {
        mBinding.etPriceEdit.hint = hint
    }

    /**
     * 计算方式编辑框提示
     */
    private fun setModeEditHint(hint: String) {
        mBinding.etComputeMode.hint = hint
    }

    /**
     * 计算方式标题
     */
    private fun setComputeModeEditTitle(title: String) {
        mBinding.tvComputeModeEditTitle.text = title
    }

    /**
     * 设置计算方式编辑框内容
     */
    private fun setComputeModeEditText(text: String) {
        mBinding.etComputeMode.setText(text)
        mBinding.etComputeMode.setSelection(mBinding.etComputeMode.text.toString().length)
    }

    private fun formatPriceText() {
        val price = getPriceEditText()
        val numFormat = price.numFormat(digits, false)
        setPriceTextNotUpdateModeText(numFormat)
    }

    /**
     * 设置计算方式编辑框内容，不更新价格输入框内容
     */
    fun setModeTextNotUpdatePriceText(text: String) {
        etModeTextWatcher.active = false
        setComputeModeEditText(text)
        etModeTextWatcher.active = true
    }

    /**
     * 设置价格编辑框内容，不更新计算方式输入框内容
     */
    fun setPriceTextNotUpdateModeText(text: String) {
        etPriceWatcher.active = false
        setPriceEditText(text)
        etPriceWatcher.active = true
    }

    /**
     * 是否显示提示
     */
    fun showTips(show: Boolean, tips: CharSequence? = "") {
        mBinding.tvTips.text = tips
        mBinding.tvTips.isVisible = show
    }

    /**
     * 设置提示文案颜色
     */
    fun setTipsTextColor(@ColorInt color: Int) {
        mBinding.tvTips.setTextColor(color)
    }

    /**
     * 设置编辑框状态
     */
    fun setPriceEditState(editState: EditState) {
        mBinding.clPriceEdit.setBackgroundResource(editState.toEditStateDrawable())
    }

    /**
     * 检查编辑框焦点
     */
    fun checkPriceEditFocus(): Boolean {
        val editState = if (mBinding.etPriceEdit.hasFocus()) EditState.FOCUS else EditState.NORMAL
        mBinding.clPriceEdit.setBackgroundResource(editState.toEditStateDrawable())
        return mBinding.etPriceEdit.hasFocus()
    }

    /**
     * 获取价格编辑框中的文本内容
     */
    fun getPriceEditText(): String {
        return mBinding.etPriceEdit.text.toString().trim()
    }

    /**
     * 获计算方式编辑框中的文本内容
     */
    fun getModeEditText(): String {
        return mBinding.etComputeMode.text.toString().trim()
    }

    /**
     * 设置价格编辑框的文本内容
     */
    private fun setPriceEditText(text: String) {
        mBinding.etPriceEdit.setText(text)
        mBinding.etPriceEdit.setSelection(mBinding.etPriceEdit.text.toString().length)
    }

    /**
     * 设置item是否选中
     */
    @SuppressLint("SetTextI18n")
    fun setSelectedItem(select: Boolean) {
        isSelectedItem = select
        val visibility = if (select) View.VISIBLE else View.GONE
        mBinding.groupEdit.visibility = visibility
        mBinding.ivTitleSelect.setImageResource(if (select) R.drawable.icon2_cb_square else R.drawable.draw_shape_stroke_c731e1e1e_c61ffffff_r2_s14)
        mBinding.tvTips.visibility = visibility
        if (select) {
            mBinding.etPriceEdit.setText("")
        }
    }

    fun isSelectedItem(): Boolean {
        return isSelectedItem
    }

    fun setOnPriceTextChangedListener(listener: (CharSequence?) -> Unit) {
        onPriceEditChange = listener
    }

    fun setOnComputeModeTextChangedListener(listener: (CharSequence?) -> Unit) {
        onComputeModelEditChange = listener
    }

    fun setOnSelectedItemListener(listener: (select: Boolean) -> Unit) {
        onSelectedItem = listener
    }

    fun setSubOperator(operator: (String, ComputeMode) -> Unit) {
        subOperator = operator
    }

    fun setAddOperator(operator: (String, ComputeMode) -> Unit) {
        addOperator = operator
    }

    fun setCurrentComputeMode(mode: ComputeMode) {
        currentComputeMode = mode
        setTitleActionText(mode.toModeActionText(context))
        setComputeModeEditTitle(mode.toModeEditTitle(context))
        setModeEditHint(mode.toModeEditTitle(context))
    }

    fun getCurrentComputeMode(): ComputeMode {
        return currentComputeMode
    }

    fun clearEditFocus() {
        mBinding.etPriceEdit.clearFocus()
        mBinding.etComputeMode.clearFocus()
    }

    override fun onClick(v: View?) {

        when (v?.id) {
            //点击选中
            R.id.ivTitleSelect, R.id.tvTitle -> {
                setSelectedItem(isSelectedItem.not())
                onSelectedItem?.invoke(isSelectedItem)
            }

            //点击title右侧按钮
            R.id.tvTitleAction -> {
                onTitleActionClick?.invoke(currentComputeMode)
            }

            //点击减号
            R.id.ivPriceEditSub -> {
                subOperator?.invoke(getPriceEditText(), currentComputeMode)
            }

            //点击加号
            R.id.ivPriceEditAdd -> {
                addOperator?.invoke(getPriceEditText(), currentComputeMode)
            }
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        mBinding.etPriceEdit.removeTextChangedListener(etPriceWatcher)
        mBinding.etComputeMode.removeTextChangedListener(etModeTextWatcher)
    }

}

/**
 * 控制是否监听输入框内容变化
 */
class ControllableTextWatcher(
    var active: Boolean = true,
    private val onTextChanged: ((active: Boolean, editable: Editable) -> Unit)? = null
) : TextWatcher {

    override fun afterTextChanged(s: Editable?) {
        if (s != null) {
            onTextChanged?.invoke(active, s)
        }
    }

    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
    }

    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
    }
}