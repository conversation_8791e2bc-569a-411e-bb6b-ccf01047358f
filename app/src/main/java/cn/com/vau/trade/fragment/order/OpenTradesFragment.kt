package cn.com.vau.trade.fragment.order

import OpenTradesContract
import android.annotation.SuppressLint
import android.app.Activity
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.SimpleItemAnimator
import cn.com.vau.MainActivity
import cn.com.vau.R
import cn.com.vau.common.AbConstants
import cn.com.vau.common.application.InitHelper
import cn.com.vau.common.base.fragment.BaseFrameFragment
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.Constants.PARAM_PRODUCT_NAME
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.constants.UrlConstants
import cn.com.vau.common.ext.launchActivity
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.ws.WsManager
import cn.com.vau.common.performance.PerformManager
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.AbTestUtil
import cn.com.vau.common.utils.SDKIntervalUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.common.view.DividerItemDecoration
import cn.com.vau.common.view.share.ShareHelper
import cn.com.vau.data.enums.EnumAdapterPosition
import cn.com.vau.data.enums.EnumInitStep
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.data.trade.PositionBean
import cn.com.vau.databinding.FragmentOpenTradesOrderBinding
import cn.com.vau.databinding.VsLayoutNoDataScrollBinding
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.profile.performance.TradePermissionPerformance
import cn.com.vau.trade.activity.CloseByOrderActivity
import cn.com.vau.trade.activity.KLineActivity
import cn.com.vau.trade.activity.ModifiedCloseConfigurationActivity
import cn.com.vau.trade.activity.TradesPositionDetailActivity
import cn.com.vau.trade.adapter.OpenPositionAdapter
import cn.com.vau.trade.dialog.BottomClosePositionConfirmDialog
import cn.com.vau.trade.dialog.BottomClosePositionDialog
import cn.com.vau.trade.dialog.BottomReverseOrderDialog
import cn.com.vau.trade.dialog.BottomTakeProfitStopLossDialog
import cn.com.vau.trade.interfac.RefreshInterface
import cn.com.vau.trade.model.KLineViewModel
import cn.com.vau.trade.model.OpenTradesModel
import cn.com.vau.trade.perform.SSTracePerformance
import cn.com.vau.trade.presenter.OpenTradesPresenter
import cn.com.vau.trade.viewmodel.ClosePositionConfirmData
import cn.com.vau.trade.viewmodel.OrderViewModel
import cn.com.vau.util.ActivityManagerUtil
import cn.com.vau.util.AdapterRefreshNotifyItemController
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.PositionDataUtil
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.copyText
import cn.com.vau.util.dp2px
import cn.com.vau.util.ifNull
import cn.com.vau.util.numCurrencyFormat
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.widget.dialog.BottomSelectListDialog
import cn.com.vau.util.widget.dialog.CenterActionDialog
import cn.com.vau.util.widget.dialog.CenterActionWithIconDialog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 订单/持仓
 */
class OpenTradesFragment : BaseFrameFragment<OpenTradesPresenter, OpenTradesModel>(),
    OpenTradesContract.View, SDKIntervalCallback,RefreshInterface {

    private val mBinding: FragmentOpenTradesOrderBinding by lazy {
        FragmentOpenTradesOrderBinding.inflate(layoutInflater)
    }

    var refreshFinished: (() -> Any?)? = null

    private val activityViewModel: KLineViewModel by activityViewModels()

    private val icon2CbSquareUncheck by lazy {
        AttrResourceUtil.getDrawable(requireActivity(),R.attr.icon2CbSquareUncheck)
    }

    private var vsNoData: VsLayoutNoDataScrollBinding? = null

    private var refreshController: AdapterRefreshNotifyItemController? = null

    // 产品名称
    private var filterSymbol = ""
    private var isKine = false

    private var isHideOtherSymbol = false

    private var mAdapter: OpenPositionAdapter? = null

    private val positionMap = HashMap<String, PositionBean>()

    var shareOrderList: CopyOnWriteArrayList<ShareOrderData> = CopyOnWriteArrayList<ShareOrderData>()

    private var abKeyRefreshOpt: Boolean = false

    private val refreshPositionList = mutableListOf<Int>()
    private var orderViewModel: OrderViewModel?=null

    private val closeAllDialog: BottomSelectListDialog by lazy {
        BottomSelectListDialog.Builder(requireActivity())
            .setTitle(getString(R.string.close_all))
            .setDataList(
                arrayListOf<String>().apply {
                    // 平仓全部订单
                    add(getString(R.string.close_all_positions))
                    // 平仓部分订单
                    add(getString(R.string.close_selected_positions))
                }
            )
            .setItemType(1)
            .setOnItemClickListener { position ->
                when (position) {
                    0 -> {
                        CenterActionDialog.Builder(requireActivity())
                            .setTitle(getString(R.string.close_position))
                            // 请确认以当前市场价格对所有选定订单进行平仓。任何闭市的订单将不会执行平仓操作。
                            .setContent(getString(R.string.please_confirm_to_any_be_affected))
                            .setStartText(getString(R.string.cancel))
                            .setEndText(getString(R.string.confirm))
                            .setOnStartListener {
                                sensorsTrackDialog("Cancel")
                            }
                            .setOnEndListener {
                                if (UserDataUtil.isStLogin()) {
                                    mPresenter.tradePositionBatchClose(shareOrderList)
                                } else {
                                    mPresenter.tradeOrdersBatchCloseV2(shareOrderList)
                                }
                                sensorsTrackDialog("Confirm")
                            }
                            .build()
                            .showDialog()

                        sensorsTrack("close all order")
                    }

                    1 -> {
                        openActivity(ModifiedCloseConfigurationActivity::class.java)
                        sensorsTrack("close selected order")
                    }
                }
            }
            .build()
    }

    private val performManager: PerformManager by lazy {
        PerformManager(this)
    }

    private val tradePermissionPerformance by lazy {
        TradePermissionPerformance(this)
    }

    override fun onCallback() {
        refreshAdapter(false)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View = mBinding.root

    override fun initParam() {
        super.initParam()
        filterSymbol = arguments?.getString(Constants.PARAM_PRODUCT_NAME).ifNull()
        isKine = arguments?.getBoolean("isKline", false) ?: false
        shareOrderList = PositionDataUtil.initPositionDataFromShareData(filterSymbol, isKine, positionMap)
        EventBus.getDefault().register(this)
        mBinding.mSmartRefreshLayout.setEnableRefresh(isKine)
    }

    private fun createObserver() {
        orderViewModel?.productDataChangeLieData?.observe(this) {
            filterSymbol = it.symbol
            if (isHideOtherSymbol) {
                refreshAdapter(true)
            }
        }
    }

    fun setOrderViewModel(orderViewModel: OrderViewModel) {
        this.orderViewModel = orderViewModel
        this.filterSymbol = orderViewModel.productData?.symbol.ifNull()
        createObserver()
    }

    @SuppressLint("WrongConstant")
    override fun initView() {
        super.initView()
        addPerformances()
        mBinding.mVsNoDataScroll.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                vsNoData = VsLayoutNoDataScrollBinding.bind(inflated)
                vsNoData?.mNoDataScrollView?.setHintMessage(getString(R.string.no_positions))
                if (isKine.not()) {
                    return
                }
                vsNoData?.mNoDataScrollView?.setBottomBtnText(getString(R.string.new_order))
                vsNoData?.mNoDataScrollView?.setBottomBtnViewClickListener {
                    tradePermissionPerformance.run {
                        if (handleTradeBlockType { handleNoDataBottomBtnClick() }) return@setBottomBtnViewClickListener
                    }
                    handleNoDataBottomBtnClick()
                }
            }
        })

        mBinding.closeAllGroup.visibility = View.VISIBLE
        mBinding.gpHideOther.visibility = View.VISIBLE

        mBinding.mSmartRefreshLayout.setEnableLoadMore(false)

        mAdapter = OpenPositionAdapter(
            requireContext(),
            shareOrderList,
            if (isKine) {
                EnumAdapterPosition.K_LINE
            } else {
                EnumAdapterPosition.MAIN_ORDER
            }
        )
        mBinding.mRecyclerView.adapter = mAdapter
        mBinding.mRecyclerView.itemAnimator = null
        mBinding.mRecyclerView.addItemDecoration(DividerItemDecoration(0.dp2px(), 25.dp2px()))
        mBinding.mRecyclerView.setEmptyView(mBinding.mVsNoDataScroll, { emptyViewVisible ->
            refreshNoDataView(emptyViewVisible)
        })

        if (abKeyRefreshOpt) {
            recyclerViewOpt()
        }
        if (isKine) {
            mBinding.closeAllGroup.isVisible = false
            mBinding.gpHideOther.isVisible = false
        }

    }

    fun refreshFilterSymbol(filterSymbol: String) {
        this.filterSymbol = filterSymbol
        if (isHideOtherSymbol) {
            refreshAdapter(true)
        }
    }

    private fun handleNoDataBottomBtnClick(){
        OrderViewModel.openOrder(requireActivity(),filterSymbol,checkReadOnly = SpManager.isV1V2().not())
        mPresenter.traceNewOrderClick()
        ActivityManagerUtil.getInstance().finishOtherActivities(MainActivity::class.java)
    }

    private fun handleNoDataDeposit(){
        NewHtmlActivity.openActivity(requireActivity(), url = UrlConstants.HTML_FUND_DEPOSIT)
        mPresenter.traceDepositClick()
    }

    private fun addPerformances() {
        performManager.addPerformance(tradePermissionPerformance)
    }

    /**
     * 净值小余0 && list为空时 显示入金空数据布局
     */
    private fun refreshNoDataView(emptyViewVisible: Boolean) {
        if (InitHelper.isNotSuccess()) return
        //demo账户
        if (UserDataUtil.isDemoAccount()) {
            return
        }
        //未显示空布局 or 有数据
        if (emptyViewVisible.not()) {
            return
        }
        val shareAccountBean = VAUSdkUtil.shareAccountBean()
        val stShareAccountBean = VAUSdkUtil.stShareAccountBean()
        val equity = if (UserDataUtil.isStLogin()) {
            stShareAccountBean.equity
        } else {
            shareAccountBean.equity
        }
        if (equity <= 0) {
            val hintTitle = "${getString(R.string.available_funds)}: ${equity.numCurrencyFormat()} ${UserDataUtil.currencyType()}"
            vsNoData?.mNoDataScrollView?.showHintTitle(hintTitle)
            vsNoData?.mNoDataScrollView?.setHintMessage(getString(R.string.deposit_funds_to_or_demo_trading))
            vsNoData?.mNoDataScrollView?.setBottomBtnText(getString(R.string.deposit))
            vsNoData?.mNoDataScrollView?.setBottomBtnViewClickListener {
                tradePermissionPerformance.run {
                    if (handleTradeBlockType { handleNoDataDeposit() }) return@setBottomBtnViewClickListener
                }
                handleNoDataDeposit()
            }
        } else {
            vsNoData?.mNoDataScrollView?.showHintTitle("")
            vsNoData?.mNoDataScrollView?.setHintMessage(getString(R.string.no_positions))
            vsNoData?.mNoDataScrollView?.hideBtnTextView()
            if (isKine.not()) {
                return
            }
            vsNoData?.mNoDataScrollView?.setBottomBtnText(getString(R.string.new_order))
            vsNoData?.mNoDataScrollView?.setBottomBtnViewClickListener {
                tradePermissionPerformance.run {
                    if (handleTradeBlockType { handleNoDataBottomBtnClick() }) return@setBottomBtnViewClickListener
                }
                handleNoDataBottomBtnClick()
            }
        }

    }

    //recyclerView优化
    private fun recyclerViewOpt() {
        mBinding.mRecyclerView.setHasFixedSize(true)
        //避免滑动动画会有延迟
        (mBinding.mRecyclerView.itemAnimator as? SimpleItemAnimator)?.supportsChangeAnimations = false
        refreshController = AdapterRefreshNotifyItemController(mBinding.mRecyclerView, mAdapter)
    }

    override fun initData() {
        super.initData()
        refreshAdapter(true)
    }

    override fun initListener() {
        super.initListener()

        mBinding.ivHideOther.clickNoRepeat {
            hideOtherSymbols()
        }

        mBinding.tvHideOther.clickNoRepeat {
            hideOtherSymbols()
        }

        mBinding.tvCloseAll.clickNoRepeat {
            if (shareOrderList.isEmpty()) {
                // 暂无订单
                ToastUtil.showToast(getString(R.string.you_have_no_order))
                return@clickNoRepeat
            }
            closeAllDialog.showDialog()
        }

        mBinding.mSmartRefreshLayout.setOnRefreshListener {
            InitHelper.initialize(EnumInitStep.ORDER)
        }

        mAdapter?.setOnItemClickListener(object : OpenPositionAdapter.OnItemClickListener {
            override fun onItemClick(position: Int) {
                openActivity(TradesPositionDetailActivity::class.java, Bundle().apply {
                    putString(Constants.PARAM_ORDER_NUMBER, shareOrderList.getOrNull(position)?.order.ifNull())
                })
            }

            override fun onStartKLine(position: Int) {
                openActivity(KLineActivity::class.java, Bundle().apply {
                    putString(
                        Constants.PARAM_PRODUCT_NAME,
                        shareOrderList.getOrNull(position)?.symbol.ifNull()
                    )
                    putString(
                        Constants.PARAM_ORDER_NUMBER,
                        shareOrderList.getOrNull(position)?.order.ifNull()
                    )
                })
                mPresenter.traceKlineClick()
            }

            override fun onShareClick(position: Int) {
                // 分享
                shareOrderList.getOrNull(position)?.let {
                    ShareHelper.orderShare(requireActivity() as AppCompatActivity, it.symbol, it.openPrice, it.closePrice, it.profit.toString(), it.cmd, it.digits)
                }
                SensorsDataUtil.track(SensorsConstant.V3510.POSITION_CARD_SHARE_BTN_CLICK)
            }

            override fun onReverseClick(position: Int) {
                reverse(shareOrderList.getOrNull(position) ?: ShareOrderData())
                mPresenter.traceEditButClick("Reverse")
            }

            override fun onTpSlClick(position: Int) {
                setTpSl(shareOrderList.getOrNull(position) ?: ShareOrderData())
                mPresenter.traceEditButClick("TP/SL")
            }

            override fun onCloseByClick(position: Int) {
                closeBy(shareOrderList.getOrNull(position) ?: ShareOrderData())
                mPresenter.traceEditButClick("Closed By")
            }

            override fun onTpSlTitleClick(position: Int) {
                toggleTpSl(position)
            }

            override fun onTpSlDeleteClick(position: Int) {
                deleteTpSl(position)
            }

            override fun onCloseClick(position: Int) {
                val orderBean = shareOrderList.getOrNull(position) ?: return
                if (UserDataUtil.isStLogin())
                    mPresenter.currentOrderId = orderBean.stOrder.ifNull()
                else
                    mPresenter.currentOrderId = orderBean.order ?: "0"
                mPresenter.currentPosition = position
                showCloseDialog(orderBean)
                mPresenter.traceEditButClick("Close")
            }

            override fun onQuickCloseClick(position: Int) {
                val orderBean = shareOrderList.getOrNull(position) ?: return
                quickClosePosition(orderBean)
            }
        })

    }

    private fun hideOtherSymbols() {
        isHideOtherSymbol = isHideOtherSymbol.not()
        mBinding.ivHideOther.setImageResource(if (isHideOtherSymbol) R.drawable.icon2_cb_square else icon2CbSquareUncheck)
        refreshAdapter(true)
    }

    private fun quickClosePosition(shareOrderData: ShareOrderData) {
        if (UserDataUtil.isStLogin())
            mPresenter.currentOrderId = shareOrderData.stOrder.ifNull()
        else
            mPresenter.currentOrderId = shareOrderData.order ?: "0"
        if ("0" == UserDataUtil.fastCloseState()) {
            showClosePositionConfirmDialog(shareOrderData)
            return
        }

        if (UserDataUtil.isStLogin()) {
            mPresenter.stTradePositionClose(shareOrderData)
        } else {
            mPresenter.tradeOrdersClose(shareOrderData, 1)
        }

    }

    private fun showClosePositionConfirmDialog(shareOrderData: ShareOrderData) {
        BottomClosePositionConfirmDialog.Builder(context as Activity)
            .setTitle(getString(R.string.close_confirmation))
            .setData(ClosePositionConfirmData(shareOrderData, shareOrderData.volume))
            .build()
            .showDialog()
    }

    private fun showCloseDialog(data: ShareOrderData) {
        BottomClosePositionDialog.Builder(requireActivity())
            .setTitle(getString(R.string.close__position))
            .setData(data)
            .setAutoFocusEditText(false)
            .moveUpToKeyboard(false)
            .moveUpEditText(true)
            .build()
            .showDialog()
    }

    /**
     * 设置止盈止损
     */
    private fun setTpSl(data: ShareOrderData) {
        BottomTakeProfitStopLossDialog.Builder(requireActivity(), data)
            .setAutoFocusEditText(false)
            .moveUpToKeyboard(false)
            .moveUpEditText(true)
            .build()
            .showDialog()
    }

    /**
     * 反向开仓
     */
    private fun reverse(data: ShareOrderData) {
        BottomReverseOrderDialog.Builder(requireActivity())
            .setMainOrderId(data.order.ifNull())
            .build()
            .showDialog()
    }

    /**
     * 互抵平仓
     */
    private fun closeBy(data: ShareOrderData) {
        if (VAUSdkUtil.shareOrderList().none { it.symbol == data.symbol && it.cmd != data.cmd }
        ) {
            // 无互抵订单存在
            ToastUtil.showToast(getString(R.string.there_is_no_order_can_close_by))
            return
        }
        openActivity(CloseByOrderActivity::class.java, Bundle().apply {
            putString(Constants.PARAM_ORDER_NUMBER, data.order.ifNull())
        })
    }

    /**
     * 取消止盈止损
     */
    private fun deleteTpSl(position: Int) {
        val orderBean = shareOrderList.getOrNull(position) ?: return
        if (UserDataUtil.isStLogin()) {
            mPresenter.stTradePositionUpdate(orderBean)
        } else {
            mPresenter.tradeOrdersUpdate(orderBean)
        }
    }

    /**
     * 止盈止损展开收起
     */
    private fun toggleTpSl(position: Int) {
        val shareOrderData = shareOrderList.getOrNull(position) ?: return
        val positionBean = shareOrderList.getOrNull(position)?.positionBean ?: return
        if (positionBean.tpSlStatus == 1) {
            positionBean.tpSlStatus = 2
        } else {
            positionBean.tpSlStatus = 1
        }
        positionMap[shareOrderData.order ?: ""] = positionBean
        mAdapter?.notifyItemChanged(position)
    }

    override fun showTokenErrorDialog(msg: String?) {
        CenterActionDialog.Builder(requireActivity())
            .setContent(msg.ifNull())
            .setSingleButton(true)
            .setOnSingleButtonListener {
                // 退出登录
                EventBus.getDefault().post(NoticeConstants.LOGOUT_ACCOUNT)
            }.build()
            .showDialog()
    }

    override fun showDealSuccessDialog(dialogTitle: String, dialogContent: String) {
    }

    override fun deletePastOrder() {
        EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_OPEN_ORDER)
        CenterActionWithIconDialog.Builder(requireActivity())
            // 平仓成功
            .setTitle(getString(R.string.close_confirmed))
            .setLottieIcon(R.raw.lottie_dialog_ok)
            .setSingleButton(true)
            .setSingleButtonText(getString(R.string.ok))
            .build()
            .showDialog()

        VAUSdkUtil.shareOrderList().removeAll {
            if (UserDataUtil.isStLogin()) {
                it?.stOrder == mPresenter.currentOrderId
            } else {
                it?.order == mPresenter.currentOrderId
            }
        }
        refreshAdapter(true)

    }

    // 跟单没有此弹窗
    override fun showCheckDelayDialog(orderBean: ShareOrderData) {
        CenterActionDialog.Builder(requireActivity())
            .setTitle(
                // 当前市场波动较大，当前价格为 %s ，是否确定进行该操作？
                getString(
                    R.string.do_you_wish_order_at_x,
                    "${if (orderBean.cmd == "0") orderBean.ask else orderBean.bid}"
                )
            )
            // 点击【确定】，将重新获取市场实时价格，因此操作造成损失将个人承担
            .setContent(getString(R.string.price_misquote_by_incurred))
            .setOnStartListener {
                WsManager.getInstance().resetConnect()
            }
            .setOnEndListener {
                mPresenter.tradeOrdersClose(orderBean, 0)
            }.build()
            .showDialog()
    }

    override fun showHintDataDialog(hintMsg: String) {
        CenterActionDialog.Builder(requireActivity())
            .setContent(hintMsg)
            .setSingleButton(true)
            .build()
            .showDialog()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun refreshAdapter(state: Boolean) {
        if (state) {
            if (isKine.not()) {
                shareOrderList = PositionDataUtil.initPositionDataFromShareData(filterSymbol, isHideOtherSymbol, positionMap)
                mAdapter?.setData(shareOrderList)
                return
            }
            //如果是KLine页的持仓列表，全量刷新需要将指定filterSymbol的产品过滤出来
            shareOrderList = PositionDataUtil.initPositionDataFromShareData(filterSymbol, isKine, positionMap)
            mAdapter?.setData(shareOrderList)

        } else {
            lifecycleScope.launch {
                withContext(Dispatchers.Default) {
                    PositionDataUtil.refreshPositionData(shareOrderList, positionMap = positionMap)
                }
                if (abKeyRefreshOpt && refreshController != null) {
                    refreshController?.refresh(recordRefreshPositionList())
                } else {
                    for (x in shareOrderList.indices) {
                        val dataBean = shareOrderList.getOrNull(x)
//                if (true == dataBean?.isRefresh) {
                        mAdapter?.notifyItemChanged(x, "vau")
                        dataBean?.isRefresh = false
//                }
                    }
                }
            }

        }
    }

    //搜集这次刷新的position
    private fun recordRefreshPositionList(): MutableList<Int> {
        refreshPositionList.clear()
        for (x in shareOrderList.indices) {
            val dataBean = shareOrderList.getOrNull(x)
            refreshPositionList.add(x)
            dataBean?.isRefresh = false
        }
        return refreshPositionList
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            NoticeConstants.Init.DATA_SUCCESS_GOODS -> {
                if (VAUSdkUtil.shareGoodList().isNotEmpty()) {
                    hideNetDialog()
                }
            }

            NoticeConstants.Init.DATA_SUCCESS_ORDER -> {
                mBinding.mSmartRefreshLayout.finishRefresh()
                refreshFinished?.invoke()
                hideNetDialog()
                refreshAdapter(true)
            }

            NoticeConstants.Init.APPLICATION_END, NoticeConstants.REFRESH_ORDER_DATA_SHARE -> {
                hideNetDialog()
                refreshAdapter(true)
            }

            // 应用在后台放置超过一分钟
            NoticeConstants.APP_IN_BACKGROUND_MORE_THAN_1M -> {
                shareOrderList.forEach {
                    it.closePrice = "-"
                }
                refreshAdapter(true)
            }

            NoticeConstants.EVENT_KLINE_SWITCH_PRODUCT -> {
                if (isKine) {
                    filterSymbol = activityViewModel.symbol
                    refreshAdapter(true)
                }
            }
        }
    }

    override fun onVisibleToUserChanged(isVisibleToUser: Boolean, invokeInResumeOrPause: Boolean) {
        super.onVisibleToUserChanged(isVisibleToUser, invokeInResumeOrPause)
        refreshAdapter(true)
        if (isVisibleToUser) {
            SDKIntervalUtil.instance.removeCallBack(this)
            SDKIntervalUtil.instance.addCallBack(this)
        } else {
            SDKIntervalUtil.instance.removeCallBack(this)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    companion object {
        fun newInstance(filterSymbol: String): OpenTradesFragment {
            val openTradesFragment = OpenTradesFragment()
            val bundle = Bundle()
            bundle.putString(PARAM_PRODUCT_NAME, filterSymbol)
            openTradesFragment.arguments = bundle
            return openTradesFragment
        }
    }

    override fun submitSuccessDialog() {
        CenterActionWithIconDialog.Builder(requireActivity())
            // 平仓成功
            .setTitle(getString(R.string.order_submitted_successfully))
            .setLottieIcon(R.raw.lottie_dialog_ok)
            .setSingleButton(true)
            .setSingleButtonText(getString(R.string.ok))
            .build()
            .showDialog()
    }

    private fun sensorsTrackDialog(buttonName: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.BUTTON_NAME, buttonName)
        SensorsDataUtil.track(
            SensorsConstant.V3510.POSITION_PAGE_CLOSE_POSITION_MENU_BTN_CLICK,
            properties
        )
    }

    private fun sensorsTrack(buttonName: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.BUTTON_NAME, buttonName)
        properties.put(SensorsConstant.Key.CURRENT_PAGE_NAME, "")
        SensorsDataUtil.track(SensorsConstant.V3510.POSITION_PAGE_CLOSE_MENU_BTN_CLICK, properties)
    }

    override fun refreshData() {
        InitHelper.initialize(EnumInitStep.ORDER)
    }
}
