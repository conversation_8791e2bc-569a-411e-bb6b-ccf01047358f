package cn.com.vau.trade.viewmodel

import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.livedata.UnPeekLiveData
import cn.com.vau.data.trade.FreeOrdersBean

class OrderThemeViewModel:BaseViewModel() {

    var isPaused = false
    var isHidden = false

    val fragmentList = ArrayList<Fragment>()
    val titleList = ArrayList<String>()
    val stockListDetailLiveData by lazy {
        UnPeekLiveData<FreeOrdersBean>()
    }

    fun stockActivityStockListDetail() {

        val paramMap = HashMap<String, Any>()
        paramMap["login"] = UserDataUtil.accountCd()
        paramMap["serverID"] = UserDataUtil.serverId()
        requestNet({
          baseService.stockActivityStockListDetail(paramMap)
        },
            onSuccess = {
                if ("V00000" != it.resultCode || (it.data?.obj?.size ?: 0) <= 0) {
                    return@requestNet
                }
                stockListDetailLiveData.value = it
            })

    }
}