package cn.com.vau.trade.perform

import android.annotation.SuppressLint
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.performance.AbsPerformance
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.common.view.share.ShareHelper
import cn.com.vau.databinding.ActivityTradesPositionDetailBinding
import cn.com.vau.trade.ext.getAmountFromVolume
import cn.com.vau.trade.viewmodel.TradesPositionDetailViewModel
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.PositionDataUtil
import cn.com.vau.util.addComma
import cn.com.vau.util.arabicReverseTextByFlag
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.copyText
import cn.com.vau.util.ifNull
import cn.com.vau.util.mathCompTo
import cn.com.vau.util.numCurrencyFormat
import cn.com.vau.util.numCurrencyFormat2
import cn.com.vau.util.numFormat2
import cn.com.vau.util.onClickWithDefaultDelegate
import cn.com.vau.util.setTextColorDiff
import cn.com.vau.util.setTextDiff
import cn.com.vau.util.toDoubleCatching
import cn.com.vau.util.widget.dialog.BottomContentDialog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Created by array on 2025/6/9 16:26
 * Desc: 持仓详情页 -- 订单信息
 */
class PositionDetailOrderInfoPerformance(
    val activity: FragmentActivity,
    val mBinding: ActivityTradesPositionDetailBinding,
    val mViewModel: TradesPositionDetailViewModel,
) : AbsPerformance() {

    private val c00c79c by lazy { ContextCompat.getColor(activity, R.color.c00c79c) }

    private val ce35728 by lazy { ContextCompat.getColor(activity, R.color.ce35728) }
    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(activity, R.attr.color_c1e1e1e_cebffffff) }

    private val volume by lazy { activity.getString(R.string.volume) }
    private val lot by lazy { activity.getString(R.string.lots) }

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        initData()
    }

    override fun onCallback() {
        updateDynamicData()
    }

    @SuppressLint("SetTextI18n")
    fun initData() {
        val orderBean = mViewModel.orderBean ?: return
        // 订单方向
        val isBuyOrder = OrderUtil.isBuyOfOrder(orderBean.cmd)
        if (isBuyOrder) {
            mBinding.tvOrderDirection.setTextDiff("B")
            mBinding.tvOrderDirection.background = ContextCompat.getDrawable(activity, R.drawable.shape_c00c79c_r2)
        } else {
            mBinding.tvOrderDirection.setTextDiff("S")
            mBinding.tvOrderDirection.background = ContextCompat.getDrawable(activity, R.drawable.shape_cf44040_r2)
        }

        //产品名称
        mBinding.tvProdName.text = orderBean.symbol.ifNull()

        // 分享
        mBinding.ivShare.onClickWithDefaultDelegate {
            ShareHelper.orderShare(activity as AppCompatActivity, orderBean.symbol, orderBean.openPrice, orderBean.closePrice, orderBean.profit.toString(), orderBean.cmd, orderBean.digits)
        }

        // 设置盈亏
        mBinding.tvPnlTitle.text = "${activity.getString(R.string.pnl)} (${UserDataUtil.currencyType()})".arabicReverseTextByFlag(" ")
        mBinding.tvPnlTitle.clickNoRepeat {
            showGlossaryXPopup(
                activity.getString(R.string.pnl), activity.getString(R.string.the_current_profit_excluding_other_charges)
            )
        }

        // 设置净盈亏
        mBinding.tvNetPnlTitle.text = "${activity.getString(R.string.net_pnl)} (${UserDataUtil.currencyType()})".arabicReverseTextByFlag(" ")
        mBinding.tvNetPnlTitle.clickNoRepeat {
            showGlossaryXPopup(
                activity.getString(R.string.net_pnl), activity.getString(R.string.the_profit_and_including_other_charges)
            )
        }

        // 手数
        if (PositionDataUtil.isAmountOpen()) {
            mBinding.tvVolumeTitle.text = "$volume (${UserDataUtil.currencyType()})".arabicReverseTextByFlag(" ")
            mBinding.tvVolume.setTextDiff(orderBean.getAmountFromVolume().addComma())
        } else {
            mBinding.tvVolumeTitle.text = "$volume ($lot)".arabicReverseTextByFlag(" ")
            mBinding.tvVolume.setTextDiff(orderBean.volume.ifNull())
        }

        // 开仓价
        mBinding.tvOpenPriceTitle.text = activity.getString(R.string.open_price)
        mBinding.tvOpenPrice.text = orderBean.openPrice

        // 现价
        mBinding.tvCurrentPriceTitle.text = activity.getString(R.string.current_price)

        //止盈止损
        mBinding.tvTpSlTitle.text = activity.getString(R.string.tp_sl).arabicReverseTextByFlag("/")
        val hasTakeProfit = orderBean.takeProfit.toDoubleCatching() > 0
        val hasStopLoss = orderBean.stopLoss.toDoubleCatching() > 0
        mBinding.tvTpPrice.setTextColor(if (hasTakeProfit) c00c79c else color_c1e1e1e_cebffffff)
        mBinding.tvTpPrice.text = if (hasTakeProfit) orderBean.takeProfit else "--"
        mBinding.tvSlPrice.setTextColor(if (hasStopLoss) ce35728 else color_c1e1e1e_cebffffff)
        mBinding.tvSlPrice.text = if (hasStopLoss) orderBean.stopLoss else "--"

        // 手续费
        mBinding.tvChargesTitle.text = "${activity.getString(R.string.charges)} (${UserDataUtil.currencyType()})".arabicReverseTextByFlag(" ")
        mBinding.tvChargesTitle.clickNoRepeat {
            showGlossaryXPopup(
                activity.getString(R.string.charges), activity.getString(R.string.the_commissions_and_all_the_account)
            )
        }
        mBinding.tvCharges.text = orderBean.commission?.numCurrencyFormat().ifNull()

        // 利息
        mBinding.tvSwapTitle.text = "${activity.getString(R.string.swap)} (${UserDataUtil.currencyType()})".arabicReverseTextByFlag(" ")
        mBinding.tvSwapTitle.clickNoRepeat {
            showGlossaryXPopup(
                activity.getString(R.string.swap), activity.getString(R.string.the_rollover_interest_either_trading_hours)
            )
        }
        mBinding.tvSwap.text = orderBean.swap?.numCurrencyFormat().ifNull()

        // 开仓时间
        mBinding.tvOpenTime.text = orderBean.openTimeStr

        // 订单号
        mBinding.tvOrderId.text = "#${orderBean.order.ifNull()}"
        // 复制订单号
        mBinding.tvOrderId.setOnClickListener {
            val orderId = orderBean.order.ifNull()
            orderId.copyText(activity.getString(R.string.number_copied))
        }

        updateDynamicData()
    }

    /**
     * 更新动态数据
     */
    @SuppressLint("SetTextI18n")
    fun updateDynamicData() {
        val orderBean = mViewModel.orderBean ?: return

        mBinding.tvPnl.setTextColorDiff(if (orderBean.profit >= 0) c00c79c else ce35728)

        mBinding.tvNetPnl.setTextColorDiff(if (orderBean.totalProfit.mathCompTo("0") != -1) c00c79c else ce35728)

        activity.lifecycleScope.launch(Dispatchers.Default) {
            val pnlUi = orderBean.profit.numCurrencyFormat2().ifNull()
            val netPnlUi = orderBean.totalProfit.numCurrencyFormat2().ifNull()
            val currentPriceUi = orderBean.closePrice.numFormat2(orderBean.digits)
            withContext(Dispatchers.Main) {
                mBinding.tvPnl.setTextDiff(pnlUi)
                mBinding.tvNetPnl.setTextDiff(netPnlUi)
                // 现价
                mBinding.tvCurrentPrice.setTextDiff(currentPriceUi)
            }
        }
    }

    private fun showGlossaryXPopup(title: String, content: String) {
        BottomContentDialog.Builder(activity)
            .setTitle(title)
            .setContent(content)
            .build()
            .showDialog()
    }
}