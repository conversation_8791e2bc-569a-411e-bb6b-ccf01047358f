package cn.com.vau.trade.st.fragment

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.TextUtils
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import cn.com.vau.R
import cn.com.vau.common.application.InitHelper
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmFragment
import cn.com.vau.common.performance.PerformManager
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.SDKIntervalUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.common.view.HintMaintenanceView
import cn.com.vau.databinding.FragmentStManualTradingBinding
import cn.com.vau.history.HistoryTrack
import cn.com.vau.history.ui.HistoryActivity
import cn.com.vau.page.StickyEvent
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.trade.fragment.order.OpenTradesFragment
import cn.com.vau.trade.fragment.order.PendingOrderFragment
import cn.com.vau.trade.interfac.RefreshInterface
import cn.com.vau.trade.perform.StTradeAccountInfoPerformance
import cn.com.vau.trade.perform.StTradeProductTitlePerformance
import cn.com.vau.trade.perform.StTradeTimeChartPerformance
import cn.com.vau.trade.viewmodel.OrderViewModel
import cn.com.vau.trade.viewmodel.StManualTradingViewModel
import cn.com.vau.util.TabType
import cn.com.vau.util.ifNull
import cn.com.vau.util.init
import cn.com.vau.util.onClickWithDefaultDelegate
import cn.com.vau.util.setVp
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * 自主交易订单
 */
class StManualTradingFragment : BaseMvvmFragment<FragmentStManualTradingBinding, StManualTradingViewModel>(), SDKIntervalCallback {

    /**
     * 下单ViewModel
     */
    private val orderViewMode: OrderViewModel by viewModels()

    private val openTradesFragment by lazy {
        OpenTradesFragment.newInstance(orderViewMode.productName).apply {
            refreshFinished = {
                finishRefresh()
            }
        }
    }

    private val pendingOrderFragment by lazy {
        PendingOrderFragment.newInstance(orderViewMode.productName).apply {
            refreshFinished = {
                finishRefresh()
            }
        }
    }

    private var mHintMaintenanceView: HintMaintenanceView? = null

    private val fragmentList by lazy {
        ArrayList<Fragment>().apply {
            add(openTradesFragment)
            add(pendingOrderFragment)
        }
    }

    private val titleList = ArrayList<String>()

    private val performManager by lazy {
        PerformManager(this)
    }

    /**
     * 产品对+账户信息
     */
    private var productTitlePerformance: StTradeProductTitlePerformance? = null

    /**
     * 资产
     */
    private var accountInfoPerformance: StTradeAccountInfoPerformance? = null

    /**
     * 分时图
     */
    private var timeChartPerformance: StTradeTimeChartPerformance? = null

    override fun onCallback() {
        if (InitHelper.isNotSuccess()) return
        context?.let {
            // 刷新页面
            showNormalAccountInfo()
            performManager.onCallback()
            mBinding.openOrderView.onCallback()
        }
    }

    private fun addPerformance() {
        accountInfoPerformance = StTradeAccountInfoPerformance(this, orderViewMode, mBinding)
        accountInfoPerformance?.run { performManager.addPerformance(this) }

        productTitlePerformance = StTradeProductTitlePerformance(this, orderViewMode, mBinding) {
            accountInfoPerformance?.setExpand(it)
//            behaviorPerformance?.setExpand(it)
        }
        productTitlePerformance?.run { performManager.addPerformance(this) }

        timeChartPerformance = StTradeTimeChartPerformance(this, mBinding, orderViewMode)
        timeChartPerformance?.run { performManager.addPerformance(this) }
    }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        setOpenOrderView()
        addPerformance()
        mBinding.mViewStubMarketMaintenance.setOnInflateListener { stub, inflated ->
            if (inflated is HintMaintenanceView) {
                mHintMaintenanceView = inflated
            }
        }

        mBinding.mViewPager2.init(fragmentList, titleList, childFragmentManager, this)
        mBinding.mTabLayout.setVp(mBinding.mViewPager2, titleList, TabType.LINE_INDICATOR)

        // 显示用户信息（登陆根据账户类型显示，未登录显示登陆按钮）
        showAccountInfo()

        mBinding.mSmartRefreshLayout.setOnRefreshListener {
            val currentFragment = fragmentList[mBinding.mViewPager2.currentItem]
            (currentFragment as? RefreshInterface)?.refreshData()
        }
    }

    override fun createObserver() {
        super.createObserver()
        orderViewMode.productDataChangeLieData.observe(this) {
            if (pendingOrderFragment.isDestroyed().not()) {
                pendingOrderFragment.refreshFilterSymbol(it.symbol)
            }
            if (openTradesFragment.isDestroyed.not()) {
                openTradesFragment.refreshFilterSymbol(it.symbol)
            }
        }
    }

    /**
     * 初始下单器
     */
    private fun setOpenOrderView() {
        orderViewMode.loadingChange.dialogLiveData.observe(this) {
            if (it) { //显示弹框
                showLoadDialog()
            } else { //关闭弹窗
                hideLoadDialog()
            }
        }
        mBinding.openOrderView.initOpenOrderView(orderViewMode)
    }

    override fun onResume() {
        super.onResume()
        // 得判断是不是真的可见了，比如首页切到其他tab，APP切到后台后，重新回到前台也会走onResume
        mViewModel.isPaused = false
        if (mViewModel.isHidden) return
        SDKIntervalUtil.instance.removeCallBack(this)
        SDKIntervalUtil.instance.addCallBack(this)
    }

    override fun onPause() {
        super.onPause()
        mViewModel.isPaused = true
        SDKIntervalUtil.instance.removeCallBack(this)
    }

    //TODO Felix 暂时这么处理，等MainActivity改成ViewPage2 就不需要这里了
    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        mViewModel.isHidden = hidden
        if (hidden) {
            SDKIntervalUtil.instance.removeCallBack(this)
        } else if (mViewModel.isPaused.not()) {
            SDKIntervalUtil.instance.removeCallBack(this)
            SDKIntervalUtil.instance.addCallBack(this)
        }
    }

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        EventBus.getDefault().register(this)
        titleList.add(getString(R.string.positions))
        titleList.add(getString(R.string.pending_orders))
    }

    override fun initListener() {
        super.initListener()
        mBinding.ivEnterHistory.onClickWithDefaultDelegate {
            openActivity(HistoryActivity::class.java)
            HistoryTrack.trackHistoryEntryIcon()
        }
    }

    // 显示用户信息（登陆根据账户类型显示，未登录显示登陆按钮）
    private fun showAccountInfo() {
        if (!UserDataUtil.isLogin()) return
        showNormalAccountInfo()
    }

    /**
     * 控制是否显示系统维护页面
     */
    private fun controlIsShowMaintenanceLayout(isShowMaintenance: Boolean) {
        if (isShowMaintenance) {
            mBinding.mViewPager2.isVisible = false
            mBinding.mViewStubMarketMaintenance.isVisible = true
            val content = if (!TextUtils.isEmpty(Constants.MAINTENANCE_MSG)) "\n" + Constants.MAINTENANCE_MSG + "\n" else "\n"
            val fullContent = getString(R.string.maintenance_dialog_content_1) + content
            mHintMaintenanceView?.setContentText(fullContent)
        } else {
            mBinding.mViewPager2.isVisible = true
            mBinding.mViewStubMarketMaintenance.isVisible = false
        }
    }

    // 非返佣账号设置公共数据账户信息(需要实时更新)
    @SuppressLint("SetTextI18n")
    private fun showNormalAccountInfo() {

        if (InitHelper.isNotSuccess()) return

        // 净值 = 余额 + 信用金 + 盈亏
        if (Constants.MARKET_MAINTAINING) {
            controlIsShowMaintenanceLayout(true)
            return
        } else {
            controlIsShowMaintenanceLayout(false)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    fun onStickyEvent(event: StickyEvent) {

        when (event.tag) {
            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_OPEN -> {
                mBinding.mViewPager2.post {
                    if (isDestroyed()) return@post
                    mBinding.mViewPager2.currentItem = titleList.indexOfFirst {
                        it == getString(R.string.positions)
                    }
                }
                EventBus.getDefault().removeStickyEvent(event)
            }

            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_PENDING -> {
                mBinding.mViewPager2.post {
                    if (isDestroyed()) return@post
                    mBinding.mViewPager2.setCurrentItem(titleList.indexOfFirst {
                        it == getString(R.string.pending_orders)
                    }, false)
                }
                EventBus.getDefault().removeStickyEvent(event)
            }

            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_HISTORY -> {
                openActivity(HistoryActivity::class.java)
                HistoryTrack.trackHistoryEntryIcon()
                EventBus.getDefault().removeStickyEvent(event)
            }

            //下单
            NoticeConstants.OpenOrder.OPEN_ORDER -> {
                val bundle = event.data as? Bundle
                bundle?.let {
                    val produceName = it.getString(Constants.PARAM_PRODUCT_NAME)
                    val cmd = it.getString(Constants.PARAM_ORDER_TYPE)
                    val productData = VAUSdkUtil.symbolList().find { shareProductData ->
                        shareProductData.symbol == produceName
                    }
                    SpManager.putTradeProductSymbol(productData?.symbol ?: "")
                    orderViewMode.setProduceData(productData)
                    orderViewMode.tradeType = cmd.ifNull(OrderViewModel.TRADE_SELL)
                }
                EventBus.getDefault().removeStickyEvent(event)
            }
        }
    }

    @SuppressLint("SetTextI18n")
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            // 切换账户 || 退出登陆
            NoticeConstants.SWITCH_ACCOUNT, NoticeConstants.AFTER_LOGOUT_RESET -> {
                // 显示用户信息（登陆根据账户类型显示，未登录显示登陆按钮）
                showAccountInfo()
            }
        }

    }

    private fun finishRefresh() {
        mBinding.mSmartRefreshLayout.finishRefresh()
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
        SDKIntervalUtil.instance.removeCallBack(this)
    }
}