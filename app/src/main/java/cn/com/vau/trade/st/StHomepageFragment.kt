package cn.com.vau.trade.st

import StHomepageContract
import android.annotation.SuppressLint
import android.graphics.Rect
import android.os.Bundle
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.*
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.text.buildSpannedString
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import cn.com.vau.R
import cn.com.vau.common.application.InitHelper
import cn.com.vau.common.application.LinkStateManager
import cn.com.vau.common.application.VauApplication
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.base.fragment.BaseFrameFragment
import cn.com.vau.common.constants.*
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.*
import cn.com.vau.common.view.AccountKYCVerifyView
import cn.com.vau.common.view.CustomViewPagerOnPageChangeListener
import cn.com.vau.common.view.HintMaintenanceView
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.common.view.popup.adapter.PlatAdapter
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.common.vm.MainViewModel
import cn.com.vau.data.account.KycVerifyLevelObj
import cn.com.vau.data.discover.StrategyMostCopied
import cn.com.vau.data.discover.StrategyRecommendAllData
import cn.com.vau.data.enums.EnumStrategyFollowState
import cn.com.vau.data.init.StShareAccountInfoData
import cn.com.vau.databinding.*
import cn.com.vau.page.StickyEvent
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.page.notice.activity.NoticeActivity
import cn.com.vau.page.user.accountManager.AccountManagerActivity
import cn.com.vau.page.user.login.LoginActivity
import cn.com.vau.signals.stsignal.activity.StStrategyDetailsActivity
import cn.com.vau.signals.stsignal.dialog.BottomGSProfitShieldDialog
import cn.com.vau.trade.activity.SearchProductActivity
import cn.com.vau.trade.fragment.deal.StDealItemFragment
import cn.com.vau.trade.fragment.deal.StDealItemOptionalFragment
import cn.com.vau.trade.model.StHomepageModel
import cn.com.vau.trade.presenter.StHomepagePresenter
import cn.com.vau.trade.st.activity.StStrategyOrdersActivity
import cn.com.vau.trade.st.adapter.StHighestReturnAdapter
import cn.com.vau.trade.view.TradeSwitchModePopup
import cn.com.vau.util.*
import cn.com.vau.util.language.LanguageHelper
import cn.com.vau.util.opt.PerfTraceUtil
import cn.com.vau.util.tracking.*
import cn.com.vau.util.widget.dialog.base.BottomListDialog
import com.bumptech.glide.request.RequestOptions
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.enums.PopupAnimation
import com.lxj.xpopup.enums.PopupPosition
import com.youth.banner.adapter.BannerImageAdapter
import com.youth.banner.holder.BannerImageHolder
import kotlinx.coroutines.*
import org.greenrobot.eventbus.*
import org.json.JSONObject
import kotlin.math.abs

/**
 * Created by roy on 2018/10/16.
 * 交易
 */
class StHomepageFragment : BaseFrameFragment<StHomepagePresenter, StHomepageModel>(),
    StHomepageContract.View, SDKIntervalCallback {

    private val mBinding: FragmentStHomeBinding by lazy { FragmentStHomeBinding.inflate(layoutInflater) }

    private val profitShieldFooter: FootRecyclerViewmoreBinding by lazy {
        FootRecyclerViewmoreBinding.inflate(layoutInflater, mBinding.layoutCopyTrading.profitShieldRecyclerView, false)
    }
    private val mostCopiedFooter: FootRecyclerViewmoreBinding by lazy {
        FootRecyclerViewmoreBinding.inflate(layoutInflater, mBinding.layoutCopyTrading.mostCopiedRecyclerView, false)
    }
    private val highReturnFooter: FootRecyclerViewmoreBinding by lazy {
        FootRecyclerViewmoreBinding.inflate(layoutInflater, mBinding.layoutCopyTrading.highestReturnRecyclerView, false)
    }
    private val lowReturnFooter: FootRecyclerViewmoreBinding by lazy {
        FootRecyclerViewmoreBinding.inflate(layoutInflater, mBinding.layoutCopyTrading.lowRiskReturnRecyclerView, false)
    }
    private val profitShieldAdapter: StHighestReturnAdapter by lazy { StHighestReturnAdapter(true) }
    private val mostCopiedAdapter: StHighestReturnAdapter by lazy { StHighestReturnAdapter() }
    private val highReturnAdapter: StHighestReturnAdapter by lazy { StHighestReturnAdapter() }
    private val lowReturnAdapter: StHighestReturnAdapter by lazy { StHighestReturnAdapter() }

    private val viewModel: MainViewModel by activityViewModels()
    private val titleList = mutableListOf<String>()
    private var fragmentList = ArrayList<Fragment>()

    val stShareAccountBean: StShareAccountInfoData by lazy { VAUSdkUtil.stShareAccountBean() }
    val currency: String by lazy { UserDataUtil.currencyType() }

    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(requireContext(), R.attr.color_c1e1e1e_cebffffff) }
    private val ce35728 by lazy { ContextCompat.getColor(requireContext(), R.color.ce35728) }
    private val c00c79c by lazy { ContextCompat.getColor(requireContext(), R.color.c00c79c) }

    private val tipsAdapter by lazy { PlatAdapter() }

    private val bottomTipPopup: BottomListDialog by lazy { BottomListDialog.Builder(requireActivity()).setAdapter(tipsAdapter).build() }

    private var mHintMaintenanceView: HintMaintenanceView? = null
    private var mAccountKYCGuideView: AccountKYCVerifyView? = null

    override fun onCallback() {
        if (InitHelper.isNotSuccess()) return
        context?.let {
            // 只刷新需要刷新的布局
            refreshCollapsePnl()
            refreshCopyTradingInfo()
            refreshNotDepositEquity()
            // 行情维护
            showMaintenance()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        mPresenter.isViewCreated = true
        lazyInitView()
        return mBinding.root
    }

    override fun initParam() {
        super.initParam()
        EventBus.getDefault().register(this)
    }

    //连接状态监听
    private val stateChangeCallback by lazy {
        object : DefaultUiLinkStateCallback(mBinding.tvConnecting, this) {
            override fun onProductListError() {
                super.onProductListError()
                uiStateProductListError()
            }

            override fun onProductListSuccess() {
                showProductList()
            }
        }
    }

    @SuppressLint("SetTextI18n", "ObsoleteSdkInt")
    override fun initView() {
        super.initView()
        if (activity != null) {
            PerfTraceUtil.stopTraceOnFirstFrame(
                mBinding.root,
                requireActivity().intent,
                PerfTraceUtil.StartTrace.Perf_v6_Start_MainFirst_TradeFirst,
                PerfTraceUtil.StartTrace.Perf_v6_Start_AppCreate_TradeFirst
            )
        }

        if (VauApplication.abOptNetParallel) {
            LinkStateManager.registerCallback(stateChangeCallback)
        }
        mBinding.ivRedDot.isVisible = SpManager.getRedPointState(false)
        mBinding.layoutSymbolTitle.tvRose.text = getString(R.string.percent_change)
        initSortIcon()

        val selectedMode = SpManager.getTradeSwitchMode()
        when (selectedMode) {
            0 -> {
                mBinding.layoutSymbolTitle.run {
                    tvSell.isVisible = false
                    tvBuy.isVisible = false
                    tvPrice.isVisible = true
                }
            }

            1 -> {
                mBinding.layoutSymbolTitle.run {
                    tvSell.isVisible = true
                    tvBuy.isVisible = true
                    tvPrice.isVisible = false
                }
            }
        }
        mBinding.mRefreshLayout.setEnableLoadMore(false)
        mBinding.mBanner.setAdapter(object : BannerImageAdapter<String>(emptyList()) {
            override fun onBindView(holder: BannerImageHolder?, data: String?, position: Int, size: Int) {
                val options = RequestOptions()
                    .placeholder(R.drawable.shape_placeholder)
                    .error(R.drawable.shape_placeholder)
                ImageLoaderUtil.loadImageWithOption(requireContext(), data, holder?.imageView, options)
            }
        }).setScrollTime(1000)
            .addBannerLifecycleObserver(this)

        if (VauApplication.abOptNetParallel) {
            if (!LinkStateManager.isStateLinkSuccess()) {
                uiStateShowConnecting()
            }
        } else {
            if (InitHelper.stepNum() == 1) {
                //正在初始化阶段  且产品列表没有完成的情况
                mBinding.tvConnecting.text = "${getString(R.string.connecting)}..."
                mBinding.tvConnecting.visibility = View.VISIBLE
            }
        }

        //系统维护布局
        mBinding.mViewStubMarketMaintenance.setOnInflateListener { _, inflated ->
            if (inflated is HintMaintenanceView) {
                mHintMaintenanceView = inflated
            }
        }

        initTabLayout()

        // Profit Shield
        mBinding.layoutCopyTrading.profitShieldRecyclerView.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        mBinding.layoutCopyTrading.profitShieldRecyclerView.adapter = profitShieldAdapter
        profitShieldAdapter.addFooterView(view = profitShieldFooter.root, orientation = LinearLayout.HORIZONTAL)

        // Most Copied
        mBinding.layoutCopyTrading.mostCopiedRecyclerView.layoutManager =
            WrapContentLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        mBinding.layoutCopyTrading.mostCopiedRecyclerView.adapter = mostCopiedAdapter
        mostCopiedAdapter.addFooterView(view = mostCopiedFooter.root, orientation = LinearLayout.HORIZONTAL)

        // Highest Annual Return
        mBinding.layoutCopyTrading.highestReturnRecyclerView.layoutManager =
            WrapContentLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        mBinding.layoutCopyTrading.highestReturnRecyclerView.adapter = highReturnAdapter
        highReturnAdapter.addFooterView(view = highReturnFooter.root, orientation = LinearLayout.HORIZONTAL)

        // Low Risk and Stable Return
        mBinding.layoutCopyTrading.lowRiskReturnRecyclerView.layoutManager =
            WrapContentLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        mBinding.layoutCopyTrading.lowRiskReturnRecyclerView.adapter = lowReturnAdapter
        lowReturnAdapter.addFooterView(view = lowReturnFooter.root, orientation = LinearLayout.HORIZONTAL)

        // High Win Rate   (跟单首页不显示 布局默认隐藏)

        // 新手引导
        initTradesGuide()

        // 神策自定义埋点(v3500)
        // App_Tab 页面浏览 -> app内五个tab页面加载完成时触发
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.TAB_NAME, "Trades") // Tab 名称
        SensorsDataUtil.track(SensorsConstant.V3500.APP_TAB_PAGE_VIEW, properties)
    }

    override fun initData() {
        super.initData()
        if (UserDataUtil.isLogin()) {
            mPresenter.isDeposited = true
            mPresenter.bannerData = null
            hideOperationBanner()
            if (SpManager.isV1V2()) {
                mPresenter.userQueryUserLevel()
            }
            mPresenter.strategyDiscoverListAllApi(UserDataUtil.stAccountId())
        }
    }

    @SuppressLint("ObsoleteSdkInt")
    private fun lazyInitView() {

        if (!mPresenter.isViewCreated || !mPresenter.isUIVisible) return

        mPresenter.isViewCreated = false
        mPresenter.isUIVisible = false

        renderLayout()
    }

    override fun initListener() {
        super.initListener()
        mBinding.ivEquityTip.setOnClickListener(this@StHomepageFragment)
        mBinding.ivEquityTip2.setOnClickListener(this@StHomepageFragment)
        mBinding.ivCopyTradingQuestion.setOnClickListener(this@StHomepageFragment)
        mBinding.ivManualTradingQuestion.setOnClickListener(this@StHomepageFragment)
        mBinding.ivProductSearch.setOnClickListener(this@StHomepageFragment)
        mBinding.ivMessage.setOnClickListener(this@StHomepageFragment)
        mBinding.ivBannerClose.setOnClickListener(this@StHomepageFragment)
        mBinding.tvCopyNow.setOnClickListener(this@StHomepageFragment)
        mBinding.tvDeposit.setOnClickListener(this@StHomepageFragment)
        mBinding.tvAccount.setOnClickListener(this@StHomepageFragment)
        mBinding.tvAccountId.setOnClickListener(this@StHomepageFragment)
        mBinding.ivArrow.setOnClickListener(this@StHomepageFragment)
        mBinding.tvCollapseAccountType.setOnClickListener(this@StHomepageFragment)
        mBinding.tvCollapseAccountId.setOnClickListener(this)
        mBinding.ivCollapseArrow.setOnClickListener(this@StHomepageFragment)
        mBinding.layoutSymbolTitle.tvSymbol.setOnClickListener(this)
        mBinding.layoutSymbolTitle.tvRose.clickNoRepeat(500) {
            TradeSortUtil.nextSort { sortMode ->
                initSortIcon(sortMode)
            }
        }

        mBinding.mRefreshLayout.setOnRefreshListener {  // 下拉刷新 (刷新多策略接口及显示,广告位接口)
            EventBus.getDefault().post(NoticeConstants.REFRESH_EVENT_IMAGE)
            mPresenter.strategyDiscoverListAllApi(UserDataUtil.stAccountId())
        }
        mBinding.layoutCopyTrading.tvProfitShield.setOnClickListener(this@StHomepageFragment)
        mBinding.layoutCopyTrading.ivProfitShield.setOnClickListener(this@StHomepageFragment)
        mBinding.layoutCopyTrading.ivArrowProfitShield.setOnClickListener(this@StHomepageFragment)
        mBinding.layoutCopyTrading.tvMostCopied.setOnClickListener(this@StHomepageFragment)
        mBinding.layoutCopyTrading.ivMostCopied.setOnClickListener(this@StHomepageFragment)
        mBinding.layoutCopyTrading.ivArrowMostCopied.setOnClickListener(this@StHomepageFragment)
        mBinding.layoutCopyTrading.tvHighestReturn.setOnClickListener(this@StHomepageFragment)
        mBinding.layoutCopyTrading.ivHighestReturn.setOnClickListener(this@StHomepageFragment)
        mBinding.layoutCopyTrading.ivArrowHighestReturn.setOnClickListener(this@StHomepageFragment)
        mBinding.layoutCopyTrading.tvLowRiskReturn.setOnClickListener(this@StHomepageFragment)
        mBinding.layoutCopyTrading.ivLowRiskReturn.setOnClickListener(this@StHomepageFragment)
        mBinding.layoutCopyTrading.ivArrowLowRiskReturn.setOnClickListener(this@StHomepageFragment)
        mBinding.mVsNoDataScroll.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                val vs = VsLayoutNoDataScrollBinding.bind(inflated)
                vs.mNoDataScrollView.setBackgroundColor(AttrResourceUtil.getColor(requireContext(), R.attr.mainLayoutBg))
                vs.mNoDataScrollView.setHintMessage(getString(R.string.something_went_wrong_try_again))
                vs.mNoDataScrollView.setBottomBtnText(getString(R.string.try_again))
                vs.mNoDataScrollView.setBottomBtnViewClickListener {
                    if (VauApplication.abOptNetParallel) {
                        InitHelper.start()
                    } else {
                        if (InitHelper.stepNum() == -1 || InitHelper.stepNum() == 0) {
                            InitHelper.start()
                        }
                    }
                }
            }
        })
        profitShieldFooter.root.setOnClickListener {
            gotoHtml()
        }
        mostCopiedFooter.root.setOnClickListener {
            gotoDiscoverWithCondition(Constants.STRATEGY_MOST_COPIED)
        }
        highReturnFooter.root.setOnClickListener {
            gotoDiscoverWithCondition(Constants.STRATEGY_HIGHEST_RETURN)
        }
        lowReturnFooter.root.setOnClickListener {
            gotoDiscoverWithCondition(Constants.STRATEGY_LOW_RISK_RETURN)
        }
        profitShieldAdapter.setOnItemChildClickListener { _, view, position ->
            val strategy = profitShieldAdapter.getItem(position)
            handleStrategySubView(-1, strategy, position)
        }
        mostCopiedAdapter.setOnItemChildClickListener { _, view, position ->
            val strategy = mostCopiedAdapter.getItem(position)
            handleStrategySubView(0, strategy, position)
        }
        highReturnAdapter.setOnItemChildClickListener { _, _, position ->
            val strategy = highReturnAdapter.getItem(position)
            handleStrategySubView(1, strategy, position)
        }
        lowReturnAdapter.setOnItemChildClickListener { _, _, position ->
            val strategy = lowReturnAdapter.getItem(position)
            handleStrategySubView(2, strategy, position)
        }
        profitShieldAdapter.setOnItemClickListener { _, _, position ->
            val strategy = profitShieldAdapter.getItem(position)
            gotoStrategyDetail(-1, strategy, position)
        }
        mostCopiedAdapter.setOnItemClickListener { _, _, position ->
            val strategy = mostCopiedAdapter.getItem(position)
            gotoStrategyDetail(0, strategy, position)
        }
        highReturnAdapter.setOnItemClickListener { _, _, position ->
            val strategy = highReturnAdapter.getItem(position)
            gotoStrategyDetail(1, strategy, position)
        }
        lowReturnAdapter.setOnItemClickListener { _, _, position ->
            val strategy = lowReturnAdapter.getItem(position)
            gotoStrategyDetail(2, strategy, position)
        }
        mBinding.mBanner.addOnPageChangeListener(object : CustomViewPagerOnPageChangeListener() {
            override fun onPageSelected(position: Int) {
                mBinding.indicator.changeIndicator(position)
            }
        })
        mBinding.mBanner.setOnBannerListener { _, bannerPosition ->
            val bannerBean = mPresenter.bannerData?.eventsList?.elementAtOrNull(bannerPosition)
            val pushBean = bannerBean?.appJumpDefModel
            VAUStartUtil.openActivity(requireActivity(), pushBean)

            // 神策自定义埋点(v3500)
            // App_交易页Banner点击 -> 点击app交易页Banner位时触发
            val properties = JSONObject()
            properties.put(SensorsConstant.Key.MKT_ID, bannerBean?.eventId.ifNull()) // 素材id
            properties.put(SensorsConstant.Key.MKT_NAME, "") // 素材名称
            properties.put(SensorsConstant.Key.MKT_RANK, bannerPosition + 1) // 素材排序
            properties.put(SensorsConstant.Key.TARGET_URL, pushBean?.urls?.def.ifNull()) // 跳转链接
            SensorsDataUtil.track(SensorsConstant.V3500.APP_TRADES_BANNER_CLICK, properties)
        }

        mBinding.appBarLayout.addOnOffsetChangedListener { appBarLayout, verticalOffset ->
            val needShow = (abs(verticalOffset) >= appBarLayout.totalScrollRange)
            if (needShow) {
                if (mBinding.clCollapsingAccountInfo.visibility == View.INVISIBLE &&
                    Constants.MARKET_MAINTAINING.not()
                ) {
                    mBinding.clCollapsingAccountInfo.visibility = View.VISIBLE
                }
            } else {
                if (mBinding.clCollapsingAccountInfo.visibility == View.VISIBLE) {
                    mBinding.clCollapsingAccountInfo.visibility = View.INVISIBLE
                }
            }
        }

        viewModel.isDeposited.observe(this) {
            //test
//            mPresenter.isDeposited = true
            mPresenter.isDeposited = it
            assetsCardUpdate()
        }
        viewModel.bannerData.observe(this) {
            mPresenter.bannerData = it
            if (mPresenter.bannerData == null) {
                hideOperationBanner()
            } else {
                showOperationBanner()
            }
        }
        viewModel.loginStatus.observe(this) {
            when (it) {
                Constants.TOKEN_ERROR -> logout()
            }
        }
    }

    private fun initTabLayout() {
        mBinding.apply {
            val shareGoodList = VAUSdkUtil.shareGoodList()

            if (fragmentList.isEmpty()) {
                fragmentList.add(StDealItemOptionalFragment())
                titleList.add(getString(R.string.watchlist))
            }

            if (fragmentList.size <= 1) {
                shareGoodList.forEachIndexed { index, (groupName) ->
                    fragmentList.add(StDealItemFragment.newInstance(index))
                    titleList.add(
                        VAUSdkUtil.getGroupNameLanguage(requireContext(), groupName.ifNull())
                    )
                }
            }

            mViewPager.init(fragmentList, titleList, childFragmentManager, this@StHomepageFragment)
            mTabLayout.setVp(mViewPager, titleList, TabType.TEXT_SCALE)
            // Crypto检测Open逻辑
            if (hasCrypto() && VAUSdkUtil.socketCurrentData != -1L) {
                val isWeekend = CalendarUtil.getInstance().isWeekend(VAUSdkUtil.socketCurrentData)
                EventBus.getDefault().post(if (isWeekend) NoticeConstants.Quotes.TRADE_SERVER_TIME_IS_WEEKEND else NoticeConstants.Quotes.TRADE_SERVER_TIME_IS_NOT_WEEKEND)
            }
        }
    }

    private fun renderLayout() {
        // 展示顶部栏
        showAccountInfo()
        // 展示资产卡片
        showAssetsCard()
    }

    // 广告位
    private fun showOperationBanner() {
        val bannerUrlList = mutableListOf<String>()
        if (mPresenter.bannerData?.eventsList?.isNotEmpty() == true) {
            mPresenter.bannerData?.eventsList?.indices?.mapNotNullTo(bannerUrlList) {
                mPresenter.bannerData?.eventsList?.elementAtOrNull(it)?.imgUrl
            }
            if (bannerUrlList.isNotEmpty()) {
                mBinding.ivBannerClose.isVisible = "true" == mPresenter.bannerData?.showClose
                mBinding.indicator.isVisible = bannerUrlList.size > 1
                mBinding.indicator.initIndicatorCount(bannerUrlList.size)
                mBinding.indicatorSplit.isVisible = true
                if (mBinding.llAccountInfo.isVisible) {
                    mBinding.llAccountInfo.run {
                        setPadding(paddingLeft, paddingTop, paddingRight, 16.dp2px())
                    }
                }
                mBinding.viewSplit.visibility = View.INVISIBLE
                mBinding.mBanner.isVisible = true
                mBinding.mBanner.setDatas(bannerUrlList)
                mBinding.mBanner.start()
            } else {
                hideOperationBanner()
            }
        } else {
            hideOperationBanner()
        }
    }

    override fun hideOperationBanner() {
        mBinding.mBanner.stop()
        mBinding.ivBannerClose.isVisible = false
        mBinding.mBanner.isVisible = false
        mBinding.indicatorSplit.isVisible = false
        if (mBinding.llAccountInfo.isVisible) {
            mBinding.llAccountInfo.run {
                setPadding(paddingLeft, paddingTop, paddingRight, 0)
            }
        }
        mBinding.viewSplit.visibility = View.VISIBLE
        mBinding.indicator.isVisible = false
    }

    private fun gotoHtml() {
        context?.let {
            NewHtmlActivity.openActivity(
                it, url = UrlConstants.HTML_GS_AGGREGATION,
                title = "Copy Trade Growth Shield",
                dataMap = mapOf(
                    "accountId" to UserDataUtil.accountCd(),
                    "crmUserId" to SpManager.getCrmUserId()
                )
            )
        }
    }

    @SuppressLint("SetTextI18n")
    private fun showAccountInfo() {
        mBinding.ivProductSearch.isVisible = !Constants.MARKET_MAINTAINING
        mBinding.tvAccountId.text = UserDataUtil.accountCd()
        mBinding.tvCollapseAccountId.text = UserDataUtil.accountCd()
        // 显示PnL
        refreshCollapsePnl()
    }

    private fun showAssetsCard() {
        mBinding.llAccountInfo.isVisible = true
        if (mPresenter.isDeposited.not()) {
            // 未入金
            mBinding.llAccountInfo.isVisible = false
            mBinding.clAssetsEmpty.isVisible = true
            // 未入金布局
            refreshNotDepositEquity()
        }
        // 显示CopyTrading和ManualTrading面板
        refreshCopyTradingInfo()
    }

    @SuppressLint("SetTextI18n")
    private fun refreshCollapsePnl() {
        if (!mBinding.clCollapsingAccountInfo.isVisible) return //不可见不刷新
        // 行情维护 || 尚未初始化完畢
        if (Constants.MARKET_MAINTAINING || InitHelper.isNotSuccess()) {
            mBinding.tvCollapsePnlValue.setTextDiff("...")
            mBinding.tvCollapsePnlValue.setTextColorDiff(color_c1e1e1e_cebffffff)
            return
        }

        lifecycleScope.launch(Dispatchers.Default) {
            val totalPnl = stShareAccountBean.followTotalHistoryProfit + stShareAccountBean.followFloatingPl
            val collapsePnlUi = totalPnl.numCurrencyFormat2()
            withContext(Dispatchers.Main) {
                mBinding.tvCollapsePnlValue.setTextDiff("${if (totalPnl > 0) "+" else ""}${collapsePnlUi}")
                mBinding.tvCollapsePnlValue.setTextColorDiff(getFollowPnlColorRes(totalPnl))
            }
        }

    }

    @SuppressLint("SetTextI18n")
    private fun refreshNotDepositEquity() {
        if (!isLayoutVisible(mBinding.clAssetsEmpty)) return //此布局在屏幕上不可见不刷新
        if (!mBinding.clAssetsEmpty.isVisible) return
        // 行情维护 || 尚未初始化完畢
        if (Constants.MARKET_MAINTAINING || InitHelper.isNotSuccess()) {
            mBinding.tvZeroEquity.setTextDiff("...")
            mBinding.tvZeroCurrency.setTextDiff("")
            return
        }
        lifecycleScope.launch(Dispatchers.Default) {
            val followEquity = stShareAccountBean.followEquity
            val manualEquity = stShareAccountBean.equity
            val zeroEquityUi = (manualEquity + followEquity).numCurrencyFormat2()
            withContext(Dispatchers.Main) {
                mBinding.tvZeroEquity.setTextDiff(zeroEquityUi)
            }
        }
        mBinding.tvZeroCurrency.setTextDiff(currency)
    }

    @SuppressLint("SetTextI18n")
    private fun refreshCopyTradingInfo() {
        if (!isLayoutVisible(mBinding.llAccountInfo)) return //此布局在屏幕上不可见不刷新
        if (!mBinding.llAccountInfo.isVisible) return
        // 行情维护 || 尚未初始化完畢
        if (Constants.MARKET_MAINTAINING || InitHelper.isNotSuccess()) {
            mBinding.tvTotalEquity.setTextDiff("...")
            mBinding.tvCopyTradingEquity.setTextDiff("...")
            mBinding.tvManualTradingEquity.setTextDiff("...")
            mBinding.tvCopyTradingFloatingPnL.setTextDiff("...")
            mBinding.tvCopyTradingFloatingPnL.setTextColorDiff(color_c1e1e1e_cebffffff)
            mBinding.tvManualTradingFloatingPnL.setTextDiff("...")
            mBinding.tvManualTradingFloatingPnL.setTextColorDiff(color_c1e1e1e_cebffffff)
            mBinding.tvCurrency.setTextDiff("")
            return
        }

        mBinding.tvCurrency.setTextDiff(currency)
        mBinding.tvManualTradingFloatingPnL.setTextColorDiff(getProfitPnlColorRes())
        // It is showing when the Copy-Trading Equity is Empty
        val isFollowEquityEmpty = stShareAccountBean.followEquity == 0.0
        mBinding.tvCopyTradingFloatingPnL.isVisible = !isFollowEquityEmpty
        mBinding.tvManualTradingFloatingPnL.isVisible = !isFollowEquityEmpty
        mBinding.clFollowEquityEmpty.isVisible = isFollowEquityEmpty

        lifecycleScope.launch(Dispatchers.Default) {
            val followEquity = stShareAccountBean.followEquity
            val manualEquity = stShareAccountBean.equity
//            val followCredit = stShareAccountBean.followCredit
            val totalEquityUi = (manualEquity + followEquity).numCurrencyFormat2()
            val stEquityUi = followEquity.numCurrencyFormat2()
            val mtEquityUi = manualEquity.numCurrencyFormat2()
            val totalPnl = stShareAccountBean.followTotalHistoryProfit + stShareAccountBean.followFloatingPl
            val stFloatingPnl = totalPnl.numCurrencyFormat2()
            val mtFloatingPnl = stShareAccountBean.profit.numCurrencyFormat2()

            withContext(Dispatchers.Main) {
                mBinding.tvTotalEquity.setTextDiff(totalEquityUi)
                mBinding.tvCopyTradingEquity.setTextDiff(stEquityUi)
                mBinding.tvManualTradingEquity.setTextDiff(mtEquityUi)
                mBinding.tvCopyTradingFloatingPnL.setTextDiff("${if (totalPnl > 0) "+" else ""}${stFloatingPnl}")
                mBinding.tvCopyTradingFloatingPnL.setTextColorDiff(getFollowPnlColorRes(totalPnl))
                mBinding.tvManualTradingFloatingPnL.setTextDiff("${if (stShareAccountBean.profit > 0) "+" else ""}${mtFloatingPnl}")
            }

        }

    }

    override fun finishRefresh() {
        mBinding.mRefreshLayout.finishRefresh()
    }

    override fun showStrategyRecommend(data: StrategyRecommendAllData?) {
        finishRefresh()
        mBinding.layoutCopyTrading.root.isVisible = data != null

        // Profit Shield
        val profitShieldAvailable = data?.profitShield?.isNotEmpty() == true
        mBinding.layoutCopyTrading.tvProfitShield.isVisible = profitShieldAvailable
        mBinding.layoutCopyTrading.ivProfitShield.isVisible = profitShieldAvailable
        mBinding.layoutCopyTrading.ivArrowProfitShield.isVisible = profitShieldAvailable
        mBinding.layoutCopyTrading.tvProfitShieldTip.isVisible = profitShieldAvailable
        if (profitShieldAvailable) {
            mBinding.layoutCopyTrading.tvProfitShieldTip.text = getProfitShieldTip()
        }
        mBinding.layoutCopyTrading.profitShieldRecyclerView.isVisible = profitShieldAvailable
        if (profitShieldAvailable) {
            profitShieldAdapter.setShowShieldIcon(true)
            profitShieldAdapter.setList(data?.profitShield ?: emptyList())
        }

        // Most Copied
        val mostCopiedAvailable = data?.mostCopied?.isNotEmpty() == true
        mBinding.layoutCopyTrading.tvMostCopied.isVisible = mostCopiedAvailable
        mBinding.layoutCopyTrading.ivMostCopied.isVisible = mostCopiedAvailable
        mBinding.layoutCopyTrading.ivArrowMostCopied.isVisible = mostCopiedAvailable
        mBinding.layoutCopyTrading.mostCopiedRecyclerView.isVisible = mostCopiedAvailable
        if (mostCopiedAvailable) {
            mostCopiedAdapter.setList(data?.mostCopied ?: emptyList())
        }

        // Highest Annual Return
        val highReturnAvailable = data?.highestReturn?.isNotEmpty() == true
        mBinding.layoutCopyTrading.tvHighestReturn.isVisible = highReturnAvailable
        mBinding.layoutCopyTrading.ivHighestReturn.isVisible = highReturnAvailable
        mBinding.layoutCopyTrading.ivArrowHighestReturn.isVisible = highReturnAvailable
        mBinding.layoutCopyTrading.highestReturnRecyclerView.isVisible = highReturnAvailable
        if (highReturnAvailable) {
            highReturnAdapter.setList(data?.highestReturn ?: emptyList())
        }

        // Low Risk and Stable Return
        val lowRiskAvailable = data?.lowRisk?.isNotEmpty() == true
        mBinding.layoutCopyTrading.tvLowRiskReturn.isVisible = lowRiskAvailable
        mBinding.layoutCopyTrading.ivLowRiskReturn.isVisible = lowRiskAvailable
        mBinding.layoutCopyTrading.ivArrowLowRiskReturn.isVisible = lowRiskAvailable
        mBinding.layoutCopyTrading.lowRiskReturnRecyclerView.isVisible = lowRiskAvailable
        if (lowRiskAvailable) {
            lowReturnAdapter.setList(data?.lowRisk ?: emptyList())
        }

        // High Win Rate   (跟单首页不显示 布局默认隐藏)
    }

    override fun kycGuideInfo(kycInfo: KycVerifyLevelObj?) {
        val visible = kycInfo?.disable == false
        UserDataUtil.setKycLevel(kycInfo?.level.ifNull(0).toString())
        mBinding.viewStubKycGuide.isVisible = visible
        if (visible) {
            mAccountKYCGuideView?.setKycVerifyState(kycInfo?.guidance)
        }
    }

    override fun onClick(view: View?) {
        super.onClick(view)
        when (view?.id) {
            R.id.ivMessage -> {
                // 埋点
                val properties = JSONObject()
                properties.put(SensorsConstant.Key.MESSAGES_STATUS, if (SpManager.getRedPointState(false)) "unread" else "read")
                SensorsDataUtil.track(SensorsConstant.V3540.HOMEPAGE_MESSAGES_ICON_CLICK, properties)

                if (!UserDataUtil.isLogin()) {
                    openActivity(LoginActivity::class.java)
                    return
                }
                openActivity(NoticeActivity::class.java)
            }

            R.id.ivEquityTip, R.id.ivEquityTip2 -> {
                val tips = arrayListOf<HintLocalData>(
                    HintLocalData(getString(R.string.equity), getString(R.string.here_is_the_total_trading_equity))
                )
                tipsAdapter.setList(tips)
                bottomTipPopup.show()
            }

            R.id.ivCopyTradingQuestion -> {
                val tips = arrayListOf<HintLocalData>(
                    HintLocalData(getString(R.string.copy_trading), getString(R.string.if_you_copy_here_copy_trading))
                )
                tipsAdapter.setList(tips)
                bottomTipPopup.show()
            }

            R.id.ivManualTradingQuestion -> {
                val tips = arrayListOf<HintLocalData>(
                    HintLocalData(getString(R.string.manual_trading), getString(R.string.if_you_trade_here_and_trading_equity))
                )
                tipsAdapter.setList(tips)
                bottomTipPopup.show()
            }

            R.id.tvLogin -> openActivity(LoginActivity::class.java)
            R.id.ivProductSearch -> {
                // 埋点
                LogEventUtil.setLogEvent(
                    BuryPointConstant.V342.GENERAL_SEARCH_BUTTON_CLICK, bundleOf(
                        "Position" to "trade",
                        "Type" to "-",
                        "Account_type" to "Copy_trading"
                    )
                )
                // 神策自定义埋点(v3500)
                // 点击搜索按钮 -> 点击搜索按钮时触发
                val properties = JSONObject()
                properties.put(SensorsConstant.Key.CURRENT_PAGE_NAME, javaClass.simpleName) // 当前页面名称
                SensorsDataUtil.track(SensorsConstant.V3500.SEARCH_BTN_CLICK, properties)

                if (!UserDataUtil.isLogin()) {
                    openActivity(LoginActivity::class.java)
                    return
                }
                openActivity(SearchProductActivity::class.java)
            }

            R.id.tvAccount, R.id.tvAccountId, R.id.ivArrow, R.id.tvCollapseAccountType, R.id.tvCollapseAccountId, R.id.ivCollapseArrow -> {
                openActivity(AccountManagerActivity::class.java)
                LogEventUtil
                    .setLogEvent(BuryPointConstant.V346.GENERAL_TRADES_ACC_MGMT_BUTTON_CLICK)
            }

            R.id.ivBannerClose -> {
                // 关闭广告位
                mPresenter.imgCloseApi(getEventIdList())
                hideOperationBanner()
                LogEventUtil
                    .setLogEvent(BuryPointConstant.V344.GENERAL_TRADES_BANNER_CLOSE_BUTTON_CLICK)
            }

            R.id.tvCopyNow -> {
                EventBus.getDefault().postSticky(
                    StickyEvent(NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_COPY_TRADE)
                )
            }

            R.id.tvDeposit -> {
                NewHtmlActivity.openActivity(requireContext(), url = UrlConstants.HTML_FUND_DEPOSIT)
            }

            R.id.tvProfitShield, R.id.ivArrowProfitShield -> {
                gotoHtml()
            }

            R.id.tvMostCopied, R.id.ivArrowMostCopied -> {          // Most Copied
                gotoDiscoverWithCondition(Constants.STRATEGY_MOST_COPIED)
            }

            R.id.tvHighestReturn, R.id.ivArrowHighestReturn -> {    // Highest Annual Return
                gotoDiscoverWithCondition(Constants.STRATEGY_HIGHEST_RETURN)
            }

            R.id.tvLowRiskReturn, R.id.ivArrowLowRiskReturn -> {    // Low Risk and Stable Return
                gotoDiscoverWithCondition(Constants.STRATEGY_LOW_RISK_RETURN)
            }

            R.id.ivProfitShield -> BottomGSProfitShieldDialog.Builder(requireActivity()).build().showDialog()
            R.id.ivMostCopied -> bottomTips(Constants.STRATEGY_MOST_COPIED)
            R.id.ivHighestReturn -> bottomTips(Constants.STRATEGY_HIGHEST_RETURN)
            R.id.ivLowRiskReturn -> bottomTips(Constants.STRATEGY_LOW_RISK_RETURN)

            R.id.tvSymbol -> {
                val lightTheme = AppUtil.isLightTheme()
                val isShowed = SpManager.getTradeQuotesGuide()
                if (isShowed.not()) {
                    mBinding.mVsGuide1.isVisible = false
                    mBinding.mVsGuide2.isVisible = false
                    // 执行跳过
                    SpManager.putTradeQuotesGuide()
                }
                XPopup.Builder(context)
                    .isDestroyOnDismiss(false)
                    .atView(view)
                    .hasShadowBg(false)
                    .isLightStatusBar(lightTheme)
                    .popupPosition(PopupPosition.Bottom)
                    .popupAnimation(PopupAnimation.ScrollAlphaFromTop)
                    .offsetX(12.dp2px())
                    .asCustom(TradeSwitchModePopup(requireContext()))
                    .show()
            }
        }
    }

    private fun gotoDiscoverWithCondition(condition: String) {
        noRepeat {
            // 跳转Discover页 Community
            EventBus.getDefault().postSticky(StickyEvent(NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_COMMUNITY, condition))
            LogEventUtil.setLogEvent(
                BuryPointConstant.V348.CT_TRADES_VIEW_MORE_BTN_CLICK, bundleOf(
                    "Category" to when (condition) {
                        Constants.STRATEGY_MOST_COPIED -> "Most_copied"
                        Constants.STRATEGY_HIGHEST_RETURN -> "Highest_annual_return"
                        Constants.STRATEGY_LOW_RISK_RETURN -> "Low_risk_stable_return"
                        else -> "High_win_rate"
                    }
                )
            )
        }
    }

    private fun gotoStrategyDetail(typeForm: Int = -1, data: StrategyMostCopied?, position: Int) {
        data?.let {
            StStrategyDetailsActivity.open(requireContext(), it.strategyId.ifNull())
            LogEventUtil.setLogEvent(
                BuryPointConstant.V348.CT_STRATEGY_PAGE_VIEW, bundleOf(
                    "Type_of_account" to "Copy Trading",
                    "Position" to "Trades",
                    "Category" to when (typeForm) {
                        0 -> "Most_copied"
                        1 -> "Highest_annual_return"
                        2 -> "Low_risk_stable_return"
                        3 -> "High_win_rate"
                        else -> ""
                    },
                    "Strategy_ID" to it.strategyId.ifNull()
                )
            )
            // 神策自定义埋点(v3500)
            mPresenter.sensorsTrack(data.strategyId.ifNull(), position, "")
            // 进策略详情页埋点
            mPresenter.sensorsCopyTradingStrategyClick(data, typeForm)
        }
    }

    private fun manageToOrder(data: StrategyMostCopied?) {
        if (data?.pendingApplyApproval == true) { //审核通过，跳转策略订单详情页 默认 tab
            openActivity(StStrategyOrdersActivity::class.java, Bundle().apply {
                putSerializable("data_strategy", StrategyOrderBaseData().apply {
                    this.type = EnumStrategyFollowState.PENDING_REVIEW
                    this.signalStrategyId = data?.strategyId
                    this.portfolioId = data?.portfolioId
                    this.followRequestId = data?.followRequestId
                })
            })
        } else { //待审核，点击跳转策略订单详情页 setting tab
            openActivity(StStrategyOrdersActivity::class.java, Bundle().apply {
                putSerializable("data_strategy", StrategyOrderBaseData().apply {
                    this.type = EnumStrategyFollowState.OPEN
                    this.signalStrategyId = data?.strategyId
                    this.portfolioId = data?.portfolioId
                    this.followRequestId = data?.followRequestId
                })
            })
        }
    }

    private fun bottomTips(type: String) {
        val tips = arrayListOf<HintLocalData>()
        when (type) {
            Constants.STRATEGY_MOST_COPIED -> {
                tips.add(HintLocalData(getString(R.string.most_copied), getString(R.string.strategies_with_the_accumulated_copiers)))
            }

            Constants.STRATEGY_HIGHEST_RETURN -> {
                tips.add(HintLocalData(getString(R.string.highest_annual_return), getString(R.string.strategies_with_the_12_months)))
            }

            Constants.STRATEGY_LOW_RISK_RETURN -> {
                tips.add(HintLocalData(getString(R.string.low_risk_and_stable_return), getString(R.string.strategies_with_risk_than_50_percent)))
                tips.add(HintLocalData(getString(R.string.risk_band), getString(R.string.the_risk_band_the_the_here_date_status)))
            }

            Constants.STRATEGY_HIGH_WIN_RATE -> {
                tips.add(HintLocalData(getString(R.string.high_win_rate), getString(R.string.strategies_with_win_3_months)))
                tips.add(HintLocalData(getString(R.string.win_rate), getString(R.string.the_percentage_of_profitable_orders)))
            }

            Constants.STRATEGY_TOP_PROVIDERS -> {
                tips.add(HintLocalData(getString(R.string.top_signal_providers), getString(R.string.the_signal_providers_cumulative_copiers)))
            }
        }
        tipsAdapter.setList(tips)
        bottomTipPopup.show()
    }

    private fun handleStrategySubView(typeForm: Int, strategy: StrategyMostCopied?, position: Int) {
        if (UserDataUtil.isStLogin()) {
            if (strategy != null) {
                if (strategy.followerStatus == true || strategy.pendingApplyApproval == true) {
                    // Manage 根据审核状态跳转相应页面
                    manageToOrder(strategy)
                } else {
                    // View 去策略详情
                    gotoStrategyDetail(typeForm, strategy, position)
                }
            }
        } else {
            // 未登录 / 非跟单 直接进策略详情
            if (strategy != null) {
                gotoStrategyDetail(typeForm, strategy, position)
            }
        }
    }

    private fun getEventIdList(): String {
        if (mPresenter.bannerData?.eventsList?.isNotEmpty() == true) {
            return mPresenter.bannerData?.eventsList?.map { it.eventId }?.joinToString(",")
                .ifNull("")
        }
        return ""
    }

    override fun tableUpdate() {
        val groupList = mutableListOf(getString(R.string.watchlist))
        VAUSdkUtil.shareGoodList().forEach { groupList.add(VAUSdkUtil.getGroupNameLanguage(requireContext(), it.groupname.ifNull())) }
        // List.toSet() == List.toSet() 可保证不同排序下元素匹配与元素个数匹配
        if (titleList.size != 1 && groupList.toSet() != titleList.toSet()) {
            fragmentList.clear()
            titleList.clear()
            mBinding.mTabLayout.removeAllViews()
        }
        initTabLayout()
    }

    override fun assetsCardUpdate() {
        if (UserDataUtil.isLogin()) {
            showAssetsCard()
        } else {
            renderLayout()
        }
    }

    private fun logout() {
        mPresenter.userGroupChange = true
        // 因切换跟单后会重启app会重新创建StTradesFragment 所以会重新走initData中的方法
        renderLayout()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onDataEvent(event: DataEvent) {
        when (event.tag) {
            // token 异常强制退出
            NoticeConstants.WS.LOGIN_ERROR_OF_TOKEN -> logout()
            NoticeConstants.SYNC_KYC_USER_LEVEL -> kycGuideInfo(event.data as? KycVerifyLevelObj)
        }
    }

    private fun uiStateShowConnecting() {
        mBinding.tvConnecting.text = "${getString(R.string.connecting)}..."
        mBinding.tvConnecting.visibility = View.VISIBLE
    }

    private fun uiStateProductListError() {
        mBinding.mVsNoDataScroll.isVisible = true
    }

    private fun showProductList() {
        mBinding.mVsNoDataScroll.isVisible = false
        if (mPresenter.userGroupChange) {
            mPresenter.userGroupChange = false
            tableUpdate()
        }
    }

    @SuppressLint("SetTextI18n")
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            // 初始化 开始/结束
            NoticeConstants.Init.APPLICATION_START -> {
                if (VauApplication.abOptNetParallel) return
                mBinding.tvConnecting.text = "${getString(R.string.connecting)}..."
                mBinding.tvConnecting.visibility = View.VISIBLE
            }

            NoticeConstants.Init.DATA_ERROR_GOODS -> {
                if (VauApplication.abOptNetParallel) return
                mBinding.mVsNoDataScroll.isVisible = true
            }

            NoticeConstants.Init.DATA_SUCCESS_GOODS -> {
                if (VauApplication.abOptNetParallel) return
                mBinding.mVsNoDataScroll.isVisible = false
                if (mPresenter.userGroupChange) {
                    mPresenter.userGroupChange = false
                    tableUpdate()
                }

                if (mBinding.tvConnecting.visibility != View.VISIBLE) {
                    return
                }
                mBinding.tvConnecting.text = getString(R.string.connected)
                lifecycleScope.launch {
                    delay(1000)
                    mBinding.tvConnecting.visibility = View.GONE
                }

            }

            // 应用在后台放置超过一分钟
            NoticeConstants.APP_IN_BACKGROUND_MORE_THAN_1M -> {
                mBinding.tvTotalEquity.text = "..."
                mBinding.tvCopyTradingEquity.text = "..."
                mBinding.tvManualTradingEquity.text = "..."
                mBinding.tvCopyTradingFloatingPnL.setTextColor(color_c1e1e1e_cebffffff)
                mBinding.tvCopyTradingFloatingPnL.text = "..."
                mBinding.tvManualTradingFloatingPnL.setTextColor(color_c1e1e1e_cebffffff)
                mBinding.tvManualTradingFloatingPnL.text = "..."
                mBinding.tvCurrency.text = ""
                mBinding.tvZeroEquity.text = "..."
            }

            NoticeConstants.SWITCH_ACCOUNT, NoticeConstants.AFTER_LOGOUT_RESET, NoticeConstants.WS.LOGIN_ERROR_CHANGE_OF_GROUP -> {
                mPresenter.userGroupChange = true
                renderLayout()  // 因切换跟单后会重启app会重新创建StTradesFragment 所以会重新走initData中的方法
                if (tag == NoticeConstants.AFTER_LOGOUT_RESET) {
                    initSortIcon()
                }
                mPresenter.userQueryUserLevel()
            }
            // 显示隐藏小红点
            NoticeConstants.WS.POINT_REMIND_MSG_SHOW -> mBinding.ivRedDot.isVisible = true

            NoticeConstants.WS.POINT_REMIND_MSG_HIDE -> mBinding.ivRedDot.isVisible = false
            // 切换Buy/Sell模式
            NoticeConstants.Quotes.TRADE_SWITCH_MODE_BUYSELL -> {
                mBinding.layoutSymbolTitle.run {
                    tvSell.isVisible = true
                    tvBuy.isVisible = true
                    tvPrice.isVisible = false
                }
            }
            // 切换Classic模式
            NoticeConstants.Quotes.TRADE_SWITCH_MODE_CLASSIC -> {
                mBinding.layoutSymbolTitle.run {
                    tvSell.isVisible = false
                    tvBuy.isVisible = false
                    tvPrice.isVisible = true
                }
            }

            // 判断当前服务器时间是否是周末 是则Crypto产品组标签显示“Open” 不是则隐藏
            NoticeConstants.Quotes.TRADE_SERVER_TIME_IS_WEEKEND -> cryptoIsShowOpenLabel(true)
            NoticeConstants.Quotes.TRADE_SERVER_TIME_IS_NOT_WEEKEND -> cryptoIsShowOpenLabel(false)
        }
        if (tag.contains(NoticeConstants.FIREBASE_JUMP_TREAD_GROUP_NAME)) {
            val groupName = tag.replace(NoticeConstants.FIREBASE_JUMP_TREAD_GROUP_NAME, "")
            if (titleList.size < 2) {
                mPresenter.willToTabGroupName = groupName
            } else {
                fcmNoticeJump(groupName)
            }
        }
    }

    private fun fcmNoticeJump(tabStr: String) {
        val groupName = VAUSdkUtil.getGroupNameLanguage(requireContext(), tabStr)
        titleList.indexOfFirst { it == groupName }.let {
            if (it == -1)
                mBinding.mViewPager.setCurrentItem(0, true)
            else
                mBinding.mViewPager.setCurrentItem(it, true)
        }
    }

    fun fcmPushTradeToTab() {
        if (mPresenter.willToTabGroupName.isNotEmpty()) {
            val groupName = mPresenter.willToTabGroupName
            mPresenter.willToTabGroupName = ""
            fcmNoticeJump(groupName)
        }
    }

    private fun initSortIcon(mode: Int? = null) {
        val sortMode = mode ?: SpManager.getTradeSortRose()
        val drawable = ContextCompat.getDrawable(
            requireContext(), AttrResourceUtil.getDrawable(
                requireContext(),
                when (sortMode) {
                    1 -> R.attr.imgDownSort
                    2 -> R.attr.imgUpSort
                    else -> R.attr.imgNotSort
                }
            )
        )
        mBinding.layoutSymbolTitle.tvRose.setCompoundDrawablesWithIntrinsicBounds(
            if (LanguageHelper.isRtlLanguage()) drawable else null,
            null,
            if (LanguageHelper.isRtlLanguage()) null else drawable,
            null
        )
    }

    private fun showMaintenance() {
        if (Constants.MARKET_MAINTAINING && mBinding.root.isVisible) {
            mBinding.mRefreshLayout.setEnableRefresh(false)
            mBinding.clSymbol.isVisible = false
            mBinding.mViewStubMarketMaintenance.isVisible = true
            mHintMaintenanceView?.setTimeText(Constants.MAINTENANCE_MSG.ifEmpty { "" })
        } else if (!Constants.MARKET_MAINTAINING && !mBinding.root.isVisible) {
            mBinding.mRefreshLayout.setEnableRefresh(true)
            mBinding.clSymbol.isVisible = true
            mBinding.mViewStubMarketMaintenance.isVisible = false
        }
    }

    @SuppressLint("SetTextI18n")
    override fun onVisibleToUserChanged(isVisibleToUser: Boolean, invokeInResumeOrPause: Boolean) {
        super.onVisibleToUserChanged(isVisibleToUser, invokeInResumeOrPause)
        if (isVisibleToUser) {
            if (viewModel.showFragmentIndex == 0) {
                // 曝光埋点
                SensorsDataUtil.track(SensorsConstant.V3610.TRADE_GENERALPAGEVIEW)
            }
            SDKIntervalUtil.instance.removeCallBack(this)
            SDKIntervalUtil.instance.addCallBack(this)
            if (mBinding.mBanner.isVisible) {
                mBinding.mBanner.start()
            }
            mPresenter.isUIVisible = true
            lazyInitView()
        } else {
            SDKIntervalUtil.instance.removeCallBack(this)
            mBinding.mBanner.stop()
        }
    }

    private fun getFollowPnlColorRes(totalPnl: Double): Int {
        return when {
            totalPnl < 0 -> ce35728
            totalPnl > 0 -> c00c79c
            else -> color_c1e1e1e_cebffffff
        }
    }

    private fun getProfitPnlColorRes(): Int {
        return when {
            stShareAccountBean.profit < 0 -> ce35728
            stShareAccountBean.profit > 0 -> c00c79c
            else -> color_c1e1e1e_cebffffff
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        if (VauApplication.abOptNetParallel) {
            LinkStateManager.unregisterCallback(stateChangeCallback)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    private fun isLayoutVisible(view: View): Boolean {
        val rect = Rect()
        return view.getLocalVisibleRect(rect)
    }

    /**
     * 新手引导
     */
    private fun initTradesGuide() {
        // ViewStub必须先设置setOnInflateListener，才能设置显示隐藏逻辑，否则里面方法不走
        mBinding.viewStubKycGuide?.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                mAccountKYCGuideView = inflated as? AccountKYCVerifyView
                mAccountKYCGuideView?.setOnVerifyCallback {
                    (activity as? AppCompatActivity)?.let {
                        KycVerifyHelper.showKycDialog(
                            it,
                            mapOf(Constants.GoldParam.CODE to Constants.GoldParam.CODE_KYC)
                        )
                    }
                    mPresenter.sensorUpgradeClick("Verify")
                }
            }
        })
        mBinding.mVsGuide1.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                val vs = VsLayoutTradesGuide1Binding.bind(inflated)
                vs.tvSkip.setOnClickListener {
                    mBinding.mVsGuide1.isVisible = false
                    // 执行跳过
                    SpManager.putTradeQuotesGuide()
                }
                vs.tvNext.setOnClickListener {
                    mBinding.mVsGuide1.isVisible = false
                    mBinding.mVsGuide2.isVisible = true
                }
            }
        })
        mBinding.mVsGuide2.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                val vs = VsLayoutTradesGuide2Binding.bind(inflated)
                vs.tvNext.setOnClickListener {
                    mBinding.mVsGuide2.isVisible = false
                    // 执行跳过
                    SpManager.putTradeQuotesGuide()
                }
            }
        })

        mBinding.mVsGuide1.isVisible = SpManager.getTradeQuotesGuide().not()
    }

    /**
     * Crypto tab 是否显示 Open 标签
     */
    private fun cryptoIsShowOpenLabel(isShow: Boolean) {
        val index = titleList.indexOfFirst { it == getString(R.string.crypto) }
        if (index != -1) {
            val tvOpen = mBinding.mTabLayout.getChildAt(index)?.findViewById<TextView>(R.id.tvOpen)
            tvOpen?.isVisible = isShow
        }
    }

    private fun hasCrypto(): Boolean {
        val index = titleList.indexOfFirst { it == getString(R.string.crypto) }
        return index != -1
    }

    private fun getProfitShieldTip(): CharSequence {
        return buildSpannedString {
            val upto = getString(R.string.up_to_100_p_a)
            append(getString(R.string.earn_x_of_cover_from_copying, upto))
            val start = indexOf(upto)
            val end = start + upto.length
            if (start != -1 && end <= length) {
                setSpan(ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.ce35728)), start, end, 0)
                setSpan(StyleSpan(android.graphics.Typeface.BOLD), start, end, 0)
            }
            append(" ")
            append(getString(R.string.tnc_apply))
        }
    }

}