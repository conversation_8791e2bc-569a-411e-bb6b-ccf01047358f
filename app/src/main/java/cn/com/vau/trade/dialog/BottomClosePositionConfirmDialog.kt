package cn.com.vau.trade.dialog

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import cn.com.vau.R
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.ws.WsManager
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.data.BaseBean
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.databinding.DialogClosePositonConfirmBinding
import cn.com.vau.trade.viewmodel.ClosePositionConfirmData
import cn.com.vau.trade.viewmodel.ClosePositionConfirmViewModel
import cn.com.vau.util.*
import cn.com.vau.util.widget.dialog.CenterActionDialog
import cn.com.vau.util.widget.dialog.CenterActionWithIconDialog
import cn.com.vau.util.widget.dialog.base.*
import org.greenrobot.eventbus.EventBus

@SuppressLint("ViewConstructor")
class BottomClosePositionConfirmDialog(
    context: FragmentActivity,
    title: CharSequence?,
    private var closePositionConfirmData: ClosePositionConfirmData?,
    private var onCloseSuccess: ((success: Boolean) -> Unit)? = null,
    private var onDismissSuccessDialog: (() -> Unit)? = null,
) : BaseMvvmBottomDialog<DialogClosePositonConfirmBinding, ClosePositionConfirmViewModel>(
    context,
    title,
    DialogClosePositonConfirmBinding::inflate
) {
    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff) }

    @SuppressLint("RestrictedApi")
    override fun initViewModel(): ClosePositionConfirmViewModel {
        return ViewModelProvider(getMyViewModelStoreOwner())[ClosePositionConfirmViewModel::class.java]
    }

    override fun setContentView() {
        super.setContentView()
        mViewModel.closePositionConfirmData = closePositionConfirmData
        mViewModel.orderData = closePositionConfirmData?.orderData
        mViewModel.orderData?.let {
            showOrderData(it, closePositionConfirmData?.closeVolume ?: "--")
        }
        initListener()
        createObserver()
    }

    @SuppressLint("SetTextI18n")
    private fun showOrderData(orderData: ShareOrderData, volume: String) {
        mContentBinding.tvPriceTitle.text = "${context.getString(R.string.price)} (${orderData.priceCurrency.ifNull()})".arabicReverseTextByFlag(" ").ifNull()
        mContentBinding.tvCurrentPriceTitle.text = "${context.getString(R.string.current_price)} (${orderData.priceCurrency.ifNull()})".arabicReverseTextByFlag(" ").ifNull()
        mContentBinding.tvVolumeTitle.text = "${context.getString(R.string.volume)} (${context.getString(R.string.lots)})"
        mContentBinding.tvSymbol.text = orderData.symbol
        mContentBinding.tvOrderNumber.text = "#${orderData.order}"
        mContentBinding.tvVolume.text = volume

        if (OrderUtil.isBuyOfOrder(orderData.cmd)) {
            mContentBinding.tvDirection.text = "B"
            mContentBinding.tvDirection.background = ContextCompat.getDrawable(context, R.drawable.shape_c00c79c_r4)
        } else {
            mContentBinding.tvDirection.text = "S"
            mContentBinding.tvDirection.background = ContextCompat.getDrawable(context, R.drawable.shape_cf44040_r4)
        }
    }

    private fun initListener() {

        mContentBinding.ivChecked.clickNoRepeat {
            mViewModel.isShowNotAgain = mViewModel.isShowNotAgain.not()
            mContentBinding.ivChecked.setImageResource(if (mViewModel.isShowNotAgain) R.drawable.icon2_cb_tick_circle_c15b374 else R.drawable.draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14)
//            tradeConfirmationPopUpDoNotShowClick()
        }
        mContentBinding.tvNext.clickNoRepeat {
            if (UserDataUtil.isStLogin()) {
                mViewModel.stTradePositionClose()
            } else {
                mViewModel.tradeOrdersClose()
            }

            if (mViewModel.isShowNotAgain) {
                mViewModel.usersetItemset(1)
            }
        }
    }

    private fun createObserver() {
        mViewModel.closePositionSuccessLiveData.observe(this) {
            onCloseSuccess?.invoke(true)
            EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_OPEN_ORDER)
            showSuccessDialog()
            dismissDialog()
        }

        mViewModel.tradeOrdersCloseHintLiveData.observe(this) {
            showHintDialog(it)
        }

        mViewModel.tradeOrdersCloseCheckDelayLiveData.observe(this) {
            showCheckDelayDialog()
        }
    }

    private fun showHintDialog(data: BaseBean?) {
        val bean = data ?: return
        CenterActionDialog.Builder(context as Activity)
            .setContent(bean.info)
            .setSingleButton(true)
            .build()
            .showDialog()
    }

    private fun showCheckDelayDialog() {
        CenterActionDialog.Builder(context as Activity)
            // 当前市场波动较大，当前价格为 %s ，是否确定进行该操作？
            .setTitle(context.getString(R.string.do_you_wish_order_at_x, mViewModel.orderData?.closePrice))
            // 点击【确定】，将重新获取市场实时价格，因此操作造成损失将个人承担
            .setContent(context.getString(R.string.price_misquote_by_incurred))
            .setOnStartListener {
                WsManager.getInstance().resetConnect()
            }
            .setOnEndListener {
                mViewModel.tradeOrdersClose(0)
            }.build().showDialog()
    }

    private fun showSuccessDialog() {
        CenterActionWithIconDialog.Builder(context as Activity)
            .setTitle(context.getString(R.string.close_confirmed))
            .setLottieIcon(R.raw.lottie_dialog_ok)
            .setSingleButton(true)
            .setSingleButtonText(context.getString(R.string.ok))
            .setOnDismissListener {
                onDismissSuccessDialog?.invoke()
            }
            .build()
            .showDialog()
    }

    override fun useSDKIntervalUtil(): Boolean {
        return true
    }

    override fun onCallback() {
        super.onCallback()
        refreshProductData()
    }

    @SuppressLint("SetTextI18n")
    private fun refreshProductData() {
        mContentBinding.tvCurrentPrice.text = mViewModel.getCurrentPrice()

        val orderData = VAUSdkUtil.shareOrderList().firstOrNull {
            it.order == mViewModel.orderData?.order
        } ?: return

        val profit = mViewModel.getProfit(orderData, mViewModel.closePositionConfirmData?.closeVolume ?: "0")

        mContentBinding.tvEstimatedPnL.text = "$profit ${UserDataUtil.currencyType()}".arabicReverseTextByFlag(" ").ifNull()
        val textColor = if (1 == profit.mathCompTo("0")) {
            ContextCompat.getColor(context, R.color.c00c79c)
        } else if (-1 == profit.mathCompTo("0")) {
            ContextCompat.getColor(context, R.color.cf44040)
        } else {
            color_c1e1e1e_cebffffff
        }
        mContentBinding.tvEstimatedPnL.setTextColor(textColor)
    }

    override fun onDismiss() {
        super.onDismiss()
    }

    class Builder(activity: Activity) :
        IBuilder<DialogClosePositonConfirmBinding, Builder>(activity) {

        // 标题
        private var title: CharSequence? = null

        // 订单数据
        private var data: ClosePositionConfirmData? = null

        private var onCloseSuccess: ((success: Boolean) -> Unit)? = null
        private var onDismissSuccessDialog: (() -> Unit)? = null

        fun setTitle(title: CharSequence?) = apply {
            this.title = title
            return this
        }

        fun setData(data: ClosePositionConfirmData?) = apply {
            this.data = data
            return this
        }

        fun setOnCloseSuccess(onCloseSuccess: ((success: Boolean) -> Unit)?) = apply {
            this.onCloseSuccess = onCloseSuccess
            return this
        }

        fun setOnDismissSuccessDialog(onDismissSuccessDialog: (() -> Unit)?) = apply {
            this.onDismissSuccessDialog = onDismissSuccessDialog
            return this
        }

        override fun build(): BottomClosePositionConfirmDialog {
            return super.build() as BottomClosePositionConfirmDialog
        }

        override fun createDialog(context: Context): IDialog<DialogClosePositonConfirmBinding> {
            return BottomClosePositionConfirmDialog(
                context as FragmentActivity,
                title,
                data,
                onCloseSuccess,
                onDismissSuccessDialog
            )
        }
    }
}