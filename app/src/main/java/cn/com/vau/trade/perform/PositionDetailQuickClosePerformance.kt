package cn.com.vau.trade.perform

import android.annotation.SuppressLint
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.ws.WsManager
import cn.com.vau.common.performance.AbsPerformance
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.databinding.ActivityTradesPositionDetailBinding
import cn.com.vau.trade.dialog.BottomClosePositionConfirmDialog
import cn.com.vau.trade.viewmodel.ClosePositionConfirmData
import cn.com.vau.trade.viewmodel.TradesPositionDetailViewModel
import cn.com.vau.util.widget.dialog.CenterActionDialog
import cn.com.vau.util.widget.dialog.CenterActionWithIconDialog
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus

/**
 * Created by array on 2025/6/9 16:26
 * Desc: 持仓详情页 -- 闪电平仓
 */
class PositionDetailQuickClosePerformance(
    val activity: FragmentActivity,
    val mBinding: ActivityTradesPositionDetailBinding,
    val mViewModel: TradesPositionDetailViewModel,
) : AbsPerformance() {

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        initListener()
    }

    override fun onCallback() {
        mViewModel.checkPositionExist()
    }

    @SuppressLint("SetTextI18n")
    fun initListener() {
        /**
         * 点击闪电平仓
         */
        mBinding.lbvQuickClose.setOnClickListener {
            quickClosePosition()
        }

        /**
         * 闪电平仓成功（跟单or非跟单）
         */
        activity.lifecycleScope.launch {
            mViewModel.closeSuccessFlow.flowWithLifecycle(activity.lifecycle, Lifecycle.State.STARTED).collect {
                mViewModel.removeCallback()
                EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_OPEN_ORDER)
                showCloseSuccessDialog()
                mViewModel.deletePastOrder(it)
            }
        }

        /**
         * 价格波动提示弹窗（非跟单）
         */
        activity.lifecycleScope.launch {
            mViewModel.closeDelayFlow.flowWithLifecycle(activity.lifecycle, Lifecycle.State.STARTED).collect {
                showDelayDialog(it)
            }
        }

        /**
         * 提示弹窗（非跟单）
         */
        activity.lifecycleScope.launch {
            mViewModel.closeHintFlow.flowWithLifecycle(activity.lifecycle, Lifecycle.State.STARTED).collect {
                showHintDataDialog(it)
            }
        }

        /**
         * 仓位是否存在检查
         */
        activity.lifecycleScope.launch {
            mViewModel.notExistFlow.flowWithLifecycle(activity.lifecycle, Lifecycle.State.STARTED).collect {
                if (it) {
                    mViewModel.removeCallback()
                    showNotExistDialog()
                }
            }
        }

        /**
         * 结束loading
         */
        activity.lifecycleScope.launch {
            mViewModel.stopLoadingFlow.flowWithLifecycle(activity.lifecycle, Lifecycle.State.STARTED).collect {
                mBinding.lbvQuickClose.stopLoading()
            }
        }
    }

    /**
     * 闪电平仓
     */
    private fun quickClosePosition() {
        val orderBean = mViewModel.orderBean ?: return
        if ("0" == UserDataUtil.fastCloseState()) {
            showClosePositionConfirmDialog(orderBean)
            return
        }

        if (UserDataUtil.isStLogin()) {
            mViewModel.stTradePositionClose(orderBean)
        } else {
            mViewModel.tradeOrdersClose(orderBean, 1)
        }

    }

    /**
     * 平仓成功弹窗
     */
    private fun showCloseSuccessDialog() {
        CenterActionWithIconDialog.Builder(activity)
            .setTitle(activity.getString(R.string.close_confirmed))
            .setLottieIcon(R.raw.lottie_dialog_ok)
            .setSingleButton(true)
            .setSingleButtonText(activity.getString(R.string.ok))
            .setDismissOnBackPressed(false)
            .setOnSingleButtonListener {
                activity.finish()
            }
            .build()
            .showDialog()
    }

    /**
     * 平仓波动较大弹窗
     * 跟单没有此弹窗
     */
    private fun showDelayDialog(orderBean: ShareOrderData) {
        CenterActionDialog.Builder(activity)
            .setTitle(
                // 当前市场波动较大，当前价格为 %s ，是否确定进行该操作？
                activity.getString(
                    R.string.do_you_wish_order_at_x,
                    "${if (orderBean.cmd == "0") orderBean.ask else orderBean.bid}"
                )
            )
            // 点击【确定】，将重新获取市场实时价格，因此操作造成损失将个人承担
            .setContent(activity.getString(R.string.price_misquote_by_incurred))
            .setOnStartListener {
                WsManager.getInstance().resetConnect()
            }
            .setOnEndListener {
                mViewModel.tradeOrdersClose(orderBean, 0)
            }.build()
            .showDialog()
    }

    /**
     * 平仓确认弹窗
     */
    private fun showClosePositionConfirmDialog(shareOrderData: ShareOrderData) {
        BottomClosePositionConfirmDialog.Builder(activity)
            .setTitle(activity.getString(R.string.close_confirmation))
            .setData(ClosePositionConfirmData(shareOrderData, shareOrderData.volume))
            .setOnCloseSuccess { success ->
                if (success) {
                    mViewModel.removeCallback()
                }
            }
            .setOnDismissSuccessDialog {
                activity.finish()
            }
            .build()
            .showDialog()
        mViewModel.stopCloseLoading()
    }

    /**
     * 提示弹窗
     */
    private fun showHintDataDialog(hintMsg: String) {
        CenterActionDialog.Builder(activity)
            .setContent(hintMsg)
            .setSingleButton(true)
            .build()
            .showDialog()
    }

    /**
     * 仓位不存在弹窗
     */
    private fun showNotExistDialog() {
        CenterActionDialog.Builder(activity)
            .setTitle(activity.getString(R.string.prompt))
            .setContent(activity.getString(R.string.the_current_position_not_exist))
            .setSingleButton(true)
            .setOnSingleButtonListener {
                activity.finish()
            }
            .build()
            .showDialog()
    }

}