package cn.com.vau.trade.view

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.text.Editable
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import cn.com.vau.MainActivity
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.common.view.CustomTextWatcher
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.databinding.OrderVolumeViewBinding
import cn.com.vau.profile.adapter.SelectAccountAdapter
import cn.com.vau.profile.adapter.SelectBean
import cn.com.vau.trade.viewmodel.OrderViewModel
import cn.com.vau.trade.viewmodel.OrderViewModel.Companion.UNIT_AMOUNT
import cn.com.vau.trade.viewmodel.OrderViewModel.Companion.UNIT_LOTS
import cn.com.vau.util.KeyboardUtil
import cn.com.vau.util.addComma
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.ifNull
import cn.com.vau.util.mathCompTo
import cn.com.vau.util.mathDiv
import cn.com.vau.util.mathMul
import cn.com.vau.util.numCurrencyFormat
import cn.com.vau.util.numCurrencyFormatUp
import cn.com.vau.util.numFormat
import cn.com.vau.util.substringCatching
import cn.com.vau.util.widget.dialog.base.BottomListDialog

@SuppressLint("NotifyDataSetChanged")
class OrderVolumeView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val mBinding by lazy {
        OrderVolumeViewBinding.inflate(LayoutInflater.from(context), this)
    }

    var mViewModel: OrderViewModel? = null
        get() {
            if (field == null) {
                throw RuntimeException("OpenOrderView 未初始化，请调用initOpenOrderView初始化")
            }
            return field
        }

    /**
     * 手数类型：金额、手
     */
    private val unitAdapter: SelectAccountAdapter<SelectBean> by lazy {
        SelectAccountAdapter<SelectBean>(isChangeSelectTextColor = false).apply {
            setNewInstance(mViewModel?.unitTypeList)
            selectTitle = mViewModel?.unitTypeList?.getOrNull(0)?.getShowItemValue()

            setOnItemClickListener { _, _, position ->
                if (mViewModel?.unit == "${position + 1}") {
                    unitTypePopup?.dismiss()
                    return@setOnItemClickListener
                }
                mViewModel?.unit = "${position + 1}"
                selectTitle = data.getOrNull(position)?.getShowItemValue()
                notifyDataSetChanged()
                if (mBinding.etVolume.isVisible.not()) {
                    mBinding.etVolume.isVisible = true
                    mBinding.tvVolumeTitle.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12f)
                }
                switchVolumeUint(selectTitle ?: context.getString(R.string.lots))
                SpManager.putOpenPositionUnit(mViewModel?.unit.ifNull(UNIT_LOTS))
                unitTypePopup?.dismiss()
            }
        }
    }

    /**
     * 下单单位切换
     */
    private val unitTypePopup: BottomListDialog? by lazy {
        mViewModel?.initUintTypeList(context)
        BottomListDialog.Builder(context as Activity)
            .setTitle(context.getString(R.string.volume_unit))
            .setAdapter(unitAdapter)
            .build()
    }

    /**
     * 手数小数位检查
     */
    private val volumeWatcher by lazy {
        object : CustomTextWatcher() {
            @SuppressLint("SetTextI18n")
            override fun afterTextChanged(edt: Editable) {

                if (mViewModel?.unit == UNIT_AMOUNT) {
                    checkAmountDigits(edt)
                    return
                }
                checkVolumeDigits(edt)
            }
        }
    }

    init {
        initListener()
    }

    /**
     * 初始化方法必须先调用
     */
    fun initOrderVolumeView(viewModel: OrderViewModel) {
        this.mViewModel = viewModel
        viewModel.initUintTypeList(context)
        createObserver()
    }

    @SuppressLint("SetTextI18n")
    private fun initListener() {
        mBinding.etVolume.addTextChangedListener(volumeWatcher)

        mBinding.etVolume.setOnFocusChangeListener { _, hasFocus ->
            mBinding.clVolume.isSelected = hasFocus
           val formatStr = mViewModel?.formatVolume(mBinding.etVolume.text.toString())
            mBinding.etVolume.setText(formatStr)
            if (hasFocus) {
                mViewModel?.isInputVolumeFromKeyBoard = true
                showTransfer(getInputPrice())
                mBinding.volSeekBar.setProgress(0)
            } else {
                resetInputView()
                mViewModel?.isInputVolumeFromKeyBoard = false
            }
            showOrNotTransferTv()
        }

        //吊起输入键盘
        mBinding.clVolume.clickNoRepeat {
            showInputView()
        }
        //加号点击
        mBinding.ivAdd.clickNoRepeat {
            addVolume()
        }
        //减号点击
        mBinding.ivSub.clickNoRepeat {
            subVolume()
        }
        //切换下单单位（手或金额）
        mBinding.tvLots.clickNoRepeat {
            mBinding.etVolume.clearFocus()
            unitTypePopup?.show()
        }
        //交易量滑杆
        mBinding.volSeekBar.setOnSeekBarChangeListener { _, progress, isStart ->
            if (isStart) {
                if (mBinding.etVolume.isVisible.not()) {
                    mBinding.etVolume.isVisible = true
                    mBinding.tvVolumeTitle.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12f)
                }
                KeyboardUtil.hideSoftInput(mBinding.etVolume)
                mBinding.etVolume.clearFocus()
                mViewModel?.isInputVolumeFromKeyBoard = false
                refreshMaxOpen()
                mViewModel?.tradePageVolumeControlClick()
            }
            if (mViewModel?.isInputVolumeFromKeyBoard?.not() == true) {
                val max = if (mViewModel?.unit == UNIT_LOTS) {
                    mViewModel?.maxOpenVolume
                } else {
                    mViewModel?.maxOpenAmount
                }
                val value = max.mathMul("${progress / 100f}")
                if (mViewModel?.unit == UNIT_LOTS) {
                    if (value.mathCompTo(mViewModel?.minVolume) != -1) {
                        val valueDiv = value.mathDiv(mViewModel?.stepVolume, 2)
                        val valueDivInt = valueDiv.substringCatching(0, valueDiv.indexOf("."), valueDiv)
                        val finalValue = valueDivInt.mathMul(mViewModel?.stepVolume).numFormat(2)
                        mBinding.etVolume.setText(finalValue)
                    } else {
                        mBinding.etVolume.setText("0.00")
                    }
                } else {
                    if (value.mathCompTo(mViewModel?.getMinAmount(getInputPrice())) != -1) {
                        mBinding.etVolume.setText(value.numCurrencyFormat())
                    } else {
                        mBinding.etVolume.setText("0".numCurrencyFormat())
                    }
                }
            }
        }
    }

    fun clearEtFocus() {
        mBinding.etVolume.clearFocus()
    }

    /**
     * 吊起输入键盘
     */
    private fun showInputView() {
        mBinding.etVolume.isVisible = true
        mBinding.tvVolumeTitle.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12f)
        KeyboardUtil.showSoftInput(mBinding.etVolume)
        mBinding.etVolume.setSelection(mBinding.etVolume.text.toString().length)
    }

    /**
     * 重置输入框
     */
    private fun resetInputView() {
        if (mBinding.etVolume.text.isNullOrEmpty()) {
            mBinding.etVolume.isVisible = false
            mBinding.tvVolumeTitle.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14f)
        }
    }

    @SuppressLint("SetTextI18n")
    private fun createObserver() {
        if (mViewModel == null) return
        if (context !is MainActivity) return

        val activity = context as MainActivity

        //最大可开变化，需要交易输入的交易量是否合法
        mViewModel?.maxOpenChangeLiveData?.observe(activity) {
            val maxOpen = if (mViewModel?.unit == UNIT_AMOUNT) it.addComma() else it.addComma(2)
            mBinding.tvMaxOpen.text = "${context.getString(R.string.max_open)} $maxOpen ${getVolumeUnit()}"
            mViewModel?.checkVolume()
        }

        // 交易量是否合法
        mViewModel?.volumeInvalidChangeLiveData?.observe(activity) {
            mBinding.tvVolumeTip.isVisible = it.first
            mBinding.tvVolumeTip.text = it.second
            setVolumeBg(it.first)
        }

        //随着行情变动需要更新交易量，并检查交易量输入是否合法
        mViewModel?.refreshProductDataLiveData?.observe(activity) {
            showTransfer(getInputPrice())
            mViewModel?.checkVolume()
        }

        //atPrice 价格改变，需要重新计算占用保证及和最大开发
        mViewModel?.atPriceChangeLiveData?.observe(activity) {
            showMargin()
            refreshMaxOpen()
        }

        //stopLimitPrice 价格改变，需要重新计算占用保证及和最大开发
        mViewModel?.stopLimitPriceChangeLiveData?.observe(activity) {
            showMargin()
            refreshMaxOpen()
        }
    }

    /**
     * 获取交易量单位（手或币种）
     */
    private fun getVolumeUnit(): String {
        if (mViewModel?.unit == UNIT_AMOUNT) {
            return UserDataUtil.currencyType()
        }
        return context.getString(R.string.lots)
    }

    /**
     * 切换或者设置产品时要重新计算最大可开，初始的交易量以及交易单位
     */
    fun showProductData() {
        resetEtVolume()
        refreshMaxOpen()
        initVolume()
        initOrderUnit()
    }

    /**
     * 设置交易量输入背景，交易量输入不合法时是红色边框背景
     */
    private fun setVolumeBg(isInvalid:Boolean) {
        if (isInvalid) {
            mBinding.clVolume.setBackgroundResource(R.drawable.draw_shape_stroke_cf44040_solid_c0a1e1e1e_c262930_r6)
            return
        }
        mBinding.clVolume.setBackgroundResource(R.drawable.select_open_order_et_bg)
    }

    /**
     * 初始化手数
     * 手数规则：没有传入默认手数时使用产品存在本地的手数，需要检查本地的手是否能被手数步长整除，能整除则用本地的手，否则使用：手数步长+（(本地的手/手数步长）取整）* 手数步长
     *
     * 如果有默认手数，则将最小手数替换为默认手数计算。
     */
    private fun initVolume() {
        if (mBinding.etVolume.text.toString().isNotEmpty() && mBinding.etVolume.text.toString().mathCompTo("0") != 0){
            return
        }
        val realShowVolume = mViewModel?.initVolume()
        mBinding.etVolume.isVisible = true
        mBinding.tvVolumeTitle.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12f)
        mBinding.etVolume.setText(realShowVolume.ifNull().numFormat(2, false))
        mBinding.etVolume.setSelection(mBinding.etVolume.text.toString().length)
        mViewModel?.inputVolume = mBinding.etVolume.text.toString()
    }

    /**
     * 点击加
     */
    private fun addVolume() {
        if (mViewModel == null) return
        if (mBinding.etVolume.isVisible.not()) {
            mBinding.etVolume.isVisible = true
            mBinding.tvVolumeTitle.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12f)
        }
        mViewModel?.isInputVolumeFromKeyBoard = true
        val volume = mViewModel?.addVolume(mBinding.etVolume.text.toString(), mViewModel?.inputPrice?:"")
        mBinding.etVolume.setText(volume)
        mBinding.etVolume.setSelection(mBinding.etVolume.text.toString().length)
        mBinding.volSeekBar.setProgress(0)
        mViewModel?.tradePagePlusMinusBtnClick("Volume", "Plus")

    }

    /**
     * 点击减
     */
    private fun subVolume() {
        if (mViewModel == null) return
        if (mBinding.etVolume.isVisible.not()) {
            mBinding.etVolume.isVisible = true
            mBinding.tvVolumeTitle.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12f)
        }
        mViewModel?.isInputVolumeFromKeyBoard = true
        val volume = mViewModel?.subVolume(mBinding.etVolume.text.toString(), mViewModel?.inputPrice?:"")
        mBinding.etVolume.setText(volume)
        mBinding.etVolume.setSelection(mBinding.etVolume.text.toString().length)
        mBinding.volSeekBar.setProgress(0)
        mViewModel?.tradePagePlusMinusBtnClick("Volume", "Minus")
    }

    /**
     * 显示最大可开手数
     * 当手数单位是手时，显示手数；是金额时显示金额
     */
    @SuppressLint("SetTextI18n")
    private fun refreshMaxOpen() {
        if (mViewModel?.unit == UNIT_LOTS) {
            mViewModel?.getMaxOpenVolume(mViewModel?.inputPrice.ifNull())
            return
        }
        mViewModel?.getMaxOpenAmount(mViewModel?.inputPrice.ifNull())
    }

    /**
     * 手数是金额时小数位检查
     */
    private fun checkAmountDigits(edt: Editable) {
        val temp = edt.toString()
        if (temp.contains(".")) {
            val posDot = temp.indexOf(".")
            //删除前进行数值校验，看是否在length内
            if (temp.length - posDot - 1 > mViewModel?.getCurrencyDigits().ifNull(2)) {
                val endIndex = posDot + 2 + mViewModel?.getCurrencyDigits().ifNull(2)
                if (endIndex <= edt.length) {
                    edt.delete(posDot + mViewModel?.getCurrencyDigits().ifNull(2) + 1, endIndex)
                }
            }
            if (posDot > 9 && posDot - 1 in 0..edt.length && posDot in 0..edt.length) {
                edt.delete(posDot - 1, posDot)
            }
        } else {
            if (temp.length > 9)
                edt.delete(temp.length - 1, temp.length)
        }
        showTransfer(mViewModel?.inputPrice.ifNull())
        mViewModel?.checkVolume()
        showMargin()
        showOrNotTransferTv()
    }

    /**
     * 手数是手时小数位检查
     */
    private fun checkVolumeDigits(edt: Editable) {
        val temp = edt.toString()
        if (temp.contains(".")) {
            val posDot = temp.indexOf(".")
            if (posDot <= 0) return
            if (temp.length - posDot - 1 > 2) {
                edt.delete(posDot + 3, posDot + 4)
            }
            if (posDot > 5 && posDot - 1 in 0..edt.length && posDot in 0..edt.length)
                edt.delete(posDot - 1, posDot)
        } else {
            if (temp.length > 5)
                edt.delete(temp.length - 1, temp.length)
        }
        showTransfer(mViewModel?.inputPrice.ifNull())
        mViewModel?.checkVolume()
        showMargin()
        showOrNotTransferTv()
    }

    /**
     * 是否显示手数和金额互换标签
     */
    private fun showOrNotTransferTv() {
        mBinding.tvTransfer.isVisible = mBinding.etVolume.hasFocus() && mBinding.etVolume.text.toString().isNotBlank()
    }

    /**
     * 手数和金额互换标签显示
     */
    @SuppressLint("SetTextI18n")
    private fun showTransfer(inputPrice:String) {
        mViewModel?.productData?.let {
            var unitStr = ""
            if (mViewModel?.unit == UNIT_LOTS) {
                mViewModel?.inputVolume = mBinding.etVolume.text.toString()
                mViewModel?.inputAmount = OrderUtil.getAmountFromVolume(it, mViewModel?.inputVolume.ifNull(), mViewModel?.tradeType.ifNull(), inputPrice)
                unitStr = "≈ ${mViewModel?.inputAmount?.addComma()} ${UserDataUtil.currencyType()}"
            } else {
                mViewModel?.inputAmount = mBinding.etVolume.text.toString()
                mViewModel?.inputVolume = OrderUtil.getVolumeFromAmount(mBinding.etVolume.text.toString(), it, mViewModel?.tradeType.ifNull(), inputPrice)
                unitStr = "≈ ${mViewModel?.inputVolume?.addComma(2)} ${context.getString(R.string.lots)}"
            }
            mBinding.tvTransfer.text = unitStr
        }
    }

    /**
     * 展示保证金和可用保证金
     */
    @SuppressLint("SetTextI18n")
    private fun showMargin() {
       mViewModel?.getMargin(mViewModel?.inputVolume.ifNull(), mViewModel?.inputPrice.ifNull())
    }

    /**
     * 初始交易单位
     */
    private fun initOrderUnit() {
        unitAdapter.selectTitle = mViewModel?.unitTypeList?.getOrNull(if (mViewModel?.unit == UNIT_LOTS) 0 else 1)?.getShowItemValue()
        unitAdapter.notifyDataSetChanged()
        if (mViewModel?.unit != UNIT_LOTS) {
            switchVolumeUint(unitAdapter.selectTitle ?: context.getString(R.string.lots))
        }
    }

    /**
     * 切换手数和金额
     */
    private fun switchVolumeUint(volumeUint: String) {
        mBinding.tvLots.text = volumeUint
        if (mViewModel == null) return
        var unitStr = ""
        mViewModel?.let {
            if (it.unit == UNIT_AMOUNT) {
                it.inputVolume = mBinding.etVolume.text.toString()
                it.inputAmount = OrderUtil.getAmountFromVolume(it.productData, it.inputVolume, it.tradeType, getInputPrice())
                unitStr = "≈ ${it.inputVolume} ${context.getString(R.string.lots)}"
            } else {
                it.inputAmount = mBinding.etVolume.text.toString()
                it.inputVolume = OrderUtil.getVolumeFromAmount(mBinding.etVolume.text.toString(), it.productData, it.tradeType, getInputPrice())
                unitStr = "≈ ${it.inputAmount} ${UserDataUtil.currencyType()}"
            }
            mBinding.tvTransfer.text = unitStr
            refreshMaxOpen()
            if (it.unit == UNIT_AMOUNT) {
                var showAmount = it.inputAmount
                if (showAmount.mathCompTo(it.getMinAmount(getInputPrice())) == -1) {
                    showAmount = it.getMinAmount(getInputPrice())
                    it.inputAmount = showAmount
                }
                if (showAmount.mathCompTo(it.maxOpenAmount) == 1) {
                    showAmount = it.maxOpenAmount
                    it.inputAmount = showAmount
                }
                mBinding.etVolume.setText(showAmount.numCurrencyFormat())
            } else {
                var showVolume = it.inputVolume
                if (showVolume.mathCompTo(it.minVolume) == -1) {
                    showVolume = it.minVolume
                    it.inputVolume = showVolume
                }
                if (showVolume.mathCompTo(it.maxOpenVolume) == 1) {
                    showVolume = it.maxOpenVolume
                    it.inputVolume = showVolume
                }
                mBinding.etVolume.setText(showVolume.numFormat(2))
            }
            it.isSwitchUnit = true
        }
    }

    @SuppressLint("SetTextI18n")
    fun resetOrderVolumeView() {
        mBinding.tvMaxOpen.text = "--"
        mBinding.etVolume.isVisible = false
        mBinding.etVolume.setText("")
        mBinding.tvVolumeTitle.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14f)
        mViewModel?.initUintTypeList(context)
        initOrderUnit()
    }

    @SuppressLint("SetTextI18n")
    private fun resetEtVolume() {
        mBinding.volSeekBar.setProgress(0)
        mBinding.tvMaxOpen.text = "--"
        mBinding.etVolume.isVisible = false
        mBinding.etVolume.setText("")
        mBinding.tvVolumeTitle.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14f)
    }

    /**
     * 获取下单价格
     */
    private fun getInputPrice(): String {
      return mViewModel?.inputPrice.ifNull()
    }
}