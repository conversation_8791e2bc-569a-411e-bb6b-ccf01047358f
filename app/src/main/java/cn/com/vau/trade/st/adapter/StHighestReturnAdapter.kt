package cn.com.vau.trade.st.adapter

import android.os.Looper
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.view.custom.WeeklyTrendBesselChart
import cn.com.vau.data.discover.StrategyMostCopied
import cn.com.vau.util.ImageLoaderUtil
import cn.com.vau.util.ScreenUtil
import cn.com.vau.util.dp2px
import cn.com.vau.util.ifNull
import cn.com.vau.util.opt.xhLoge
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import java.util.LinkedList

class StHighestReturnAdapter(private val forceTrendColor: Boolean = false) : BaseQuickAdapter<StrategyMostCopied, BaseViewHolder>(R.layout.item_strategy_return) {

    private val itemPool: PreLoadItemViewPool
    private val img_gs_shield by lazy { ContextCompat.getDrawable(context, R.drawable.img_growth_shield) }

    init {
        addChildClickViewIds(R.id.tvView)
        itemPool = PreLoadItemViewPool()
        itemPool.preLoad(this)
    }

    private var isShowShieldIcon = false
    val width: Int by lazy { ScreenUtil.screenWidth - (12 + 75).dp2px() }

    override fun convert(holder: BaseViewHolder, item: StrategyMostCopied) {

        val lp = holder.itemView.layoutParams as? RecyclerView.LayoutParams
        lp?.width = width
        val marginHorizontalBase = context.resources.getDimensionPixelSize(R.dimen.margin_horizontal_base)

        if (holder.adapterPosition == 0) {
            lp?.marginStart = marginHorizontalBase
            lp?.marginEnd = marginHorizontalBase
        } else {
            lp?.marginStart = 0
            lp?.marginEnd = marginHorizontalBase
        }

        holder.itemView.layoutParams = lp

        val chartData = item.timePointList?.mapTo(arrayListOf()) { it.value }

        val avatar = holder.getView<AppCompatImageView>(R.id.ivAvatar)
        val chart = holder.getView<WeeklyTrendBesselChart>(R.id.weekTrendChart)
        ImageLoaderUtil.loadImage(avatar, item.avatar, avatar, R.mipmap.ic_launcher)
        chart.setDisplayFull(true)
        chart.setForceTrendColor(forceTrendColor)
        chart.setData(chartData, true)

        val tvName = holder.getView<TextView>(R.id.tvName)
        tvName.text = item.nickname.ifNull()
        tvName.setCompoundDrawablesWithIntrinsicBounds(if (isShowShieldIcon) img_gs_shield else null, null, null, null)
        tvName.compoundDrawablePadding = if (isShowShieldIcon) 4.dp2px() else 0

        holder.setText(R.id.tvFollowers, "${item.copiers.ifNull()}")
            .setText(R.id.tvRoi, "${context.getString(R.string.return_another)}(${getROIMonth(item.months.ifNull())})")
            .setText(R.id.tvRoiRate, item.returnRateUI)
            .setText(R.id.tvWinRateValue, item.winRateUI)
            .setText(R.id.tvRiskValue, item.riskBandLevel.ifNull())
            .setText(R.id.tvView, getViewText(item))
    }

    private fun getROIMonth(month: Int): String {
        return if (month % 12 == 0) {
            "${month / 12}Y"
        } else {
            "${month}M"
        }
    }

    private fun getViewText(item: StrategyMostCopied): String {
        return if (UserDataUtil.isStLogin()) {
            if (item.followerStatus == true || item.pendingApplyApproval == true) {
                context.getString(R.string.manage)
            } else {
                context.getString(R.string.view)
            }
        } else {
            context.getString(R.string.view)
        }
    }

    fun setShowShieldIcon(show: Boolean) {
        isShowShieldIcon = show
    }

    class PreLoadItemViewPool {
        private val max = 3 //最大缓存数量
        private val pool = LinkedList<BaseViewHolder>()
        fun preLoad(adapter: StHighestReturnAdapter) {
            Looper.myQueue().addIdleHandler {
                //有没有方法可以不传递resId？ 有：onCreateDefViewHolder，但是我们重写了onCreateDefViewHolder
                val baseViewHolder = adapter.createBaseViewHolder(adapter.recyclerView, R.layout.item_strategy_return)
                pool.add(baseViewHolder)
                xhLoge("加入池中 ..." + " 池大小 " + pool.size)
                pool.size < max
            }
        }

        fun getViewHolder(): BaseViewHolder? {
            val item = pool.poll()
            xhLoge("从池中取出 --------------------...$item")
            return item
        }
    }
}