package cn.com.vau.util.widget

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import cn.com.vau.R

/**
 * DashLineTextView 测试 Activity
 * 用于演示自动扩展高度功能
 */
class DashLineTextViewTestActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.example_dash_line_textview)
        
        // 可以在这里添加动态测试代码
        setupDynamicTest()
    }
    
    private fun setupDynamicTest() {
        // 动态创建一个 DashLineTextView 进行测试
        val dynamicTextView = DashLineTextView(this).apply {
            text = "Dynamic test: gypsy jumping quickly"
            textSize = 20f
            setDashEnabled(true)
            setDashColor(android.graphics.Color.GREEN)
            setDashStrokeWidth(3f)
            setDashLength(8f)
            setDashGap(4f)
            setDashOffset(6f)
            setDashAlignment(DashLineTextView.DashAlignment.DESCENT)
            setAutoExpandHeight(true)
            setBackgroundColor(0xFFEEEEEE.toInt())
        }
        
        // 可以将这个动态创建的 TextView 添加到布局中进行测试
        // 这里只是演示如何动态设置属性
    }
}
