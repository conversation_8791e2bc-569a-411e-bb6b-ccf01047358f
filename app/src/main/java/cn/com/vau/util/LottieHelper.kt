package cn.com.vau.util

import android.content.Context
import android.content.res.Configuration
import androidx.annotation.RawRes
import cn.com.vau.R
import com.airbnb.lottie.*
import com.airbnb.lottie.model.LottieCompositionCache
import java.lang.reflect.Method

/**
 * lottie预加载工具类
 */
object LottieHelper {

    //首页tab的四个lottie json  浅色主题
    val lottieRawIdsForL: IntArray by lazy {
        intArrayOf(R.raw.lottie_home,R.raw.lottie_trades, R.raw.lottie_discover, R.raw.lottie_promo, R.raw.lottie_profile)
    }

    //首页tab的四个lottie json  深色主题
    val lottieRawIdsForD: IntArray by lazy {
        intArrayOf(R.raw.lottie_home_d,R.raw.lottie_trades_d, R.raw.lottie_discover_d, R.raw.lottie_promo_d, R.raw.lottie_profile_d)
    }

    fun preLoadLottieJson() {
        if (AppUtil.isLightTheme()) {
            for (lottieId in lottieRawIdsForL) {
                LottieCompositionFactory.fromRawRes(UtilApp.getApp(), lottieId)
            }
        } else {
            for (lottieId in lottieRawIdsForD) {
                LottieCompositionFactory.fromRawRes(UtilApp.getApp(), lottieId)
            }
        }

    }

    /**
     * 缓存反射字段，减少反射次数，优化性能
     */
    private var instanceMethod: Method? = null
    private var getMethod: Method? = null
    private var cacheObject: Any? = null

    private fun getLottieCacheComposition(@RawRes lottieResId: Int): LottieComposition? {
        val rawResCacheKey = rawResCacheKey(UtilApp.getApp(), lottieResId)
        try {
            val lottieCacheClass: Class<*> = LottieCompositionCache::class.java
            //单例方法
            if (instanceMethod == null) {
                instanceMethod = lottieCacheClass.getMethod("getInstance")
            }
            //获取缓存的方法
            if (getMethod == null) {
                getMethod = lottieCacheClass.getMethod("get", String::class.java)
            }
            //获取缓存对象
            if (cacheObject == null) {
                cacheObject = instanceMethod?.invoke(null)
            }
            return getMethod?.invoke(cacheObject, rawResCacheKey) as? LottieComposition
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }

    fun setAnimation(lottieAnimationView: LottieAnimationView?, @RawRes lottieResId: Int) {
        if (lottieAnimationView == null) {
            return
        }
        val lottieRawIds = if (AppUtil.isLightTheme()) {
            lottieRawIdsForL
        } else {
            lottieRawIdsForD
        }
        if (!lottieRawIds.contains(lottieResId)) {
            //如果没有被此类预加载过，则执行lottie库的加载过程
            lottieAnimationView.setAnimation(lottieResId)
            return
        }
        //尝试获取缓存
        val cacheComposition = getLottieCacheComposition(lottieResId)
        //成功获取到缓存，缓存和当前View已经设置的数据不相同
        if (cacheComposition != null) {
            lottieAnimationView.setComposition(cacheComposition)
        } else {
            //未获取到缓存，，则执行lottie库的加载过程
            lottieAnimationView.setAnimation(lottieResId)
        }
    }

    /**
     * 构建lottie 缓存的Key
     */
    private fun rawResCacheKey(context: Context, @RawRes resId: Int): String {
        return "rawRes" + (if (isNightMode(context)) "_night_" else "_day_") + resId
    }

    /**
     * It is important to include day/night in the cache key so that if it changes, the cache won't return an animation from the wrong bucket.
     */
    private fun isNightMode(context: Context): Boolean {
        val nightModeMasked = context.resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK
        return nightModeMasked == Configuration.UI_MODE_NIGHT_YES
    }
}
