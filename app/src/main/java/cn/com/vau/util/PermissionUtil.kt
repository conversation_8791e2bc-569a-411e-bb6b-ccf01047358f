package cn.com.vau.util

import android.content.Context
import android.content.pm.PackageManager
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment

object PermissionUtil {

    /**
     * 每个权限是否已授权
     * true -> 有对应权限
     * false -> 无对应权限
     */
    fun isGranted(context: Context, vararg permissions: String): Boolean {
        val permissionsToRequest = permissions.filter {
            ContextCompat.checkSelfPermission(context, it) != PackageManager.PERMISSION_GRANTED
        }
        return permissionsToRequest.isEmpty()
    }

    fun checkPermissionWithCallback(
        context: Context,
        vararg permissions: String,
        callback: ((Boolean) -> Unit)? = null
    ) {
        when {
            isGranted(context, *permissions) -> {
                // You can use the API that requires the permission.
                callback?.invoke(true)
            }

            else -> {
                val permissionsToRequest = permissions.filter {
                    ContextCompat.checkSelfPermission(context, it) != PackageManager.PERMISSION_GRANTED
                }.toTypedArray()
                requestPermissionWithCallback(context, callback, *permissionsToRequest)
            }
        }
    }

    /**
     * 新增方法：通过 Context 请求权限 (自动绑定到 Activity)
     */
    fun requestPermissionWithCallback(
        context: Context,
        callback: ((Boolean) -> Unit)? = null,
        vararg permissions: String,
    ) {
        val activity = context as? AppCompatActivity ?: ActivityManagerUtil.getInstance().activityStack.last() as AppCompatActivity
        val fragmentManager = activity.supportFragmentManager

        if (isGranted(context, *permissions)) {
            callback?.invoke(true)
            return
        }

        activity.runOnUiThread {
            activity.window.decorView.post {
                runCatching {
                    val fragment = HeadlessFragment()
                    fragmentManager.beginTransaction()
                        .add(fragment, "HeadlessFragment")
                        .commitNowAllowingStateLoss()
                    fragment.requestPermission(callback, *permissions)
                }.onFailure {
                    IntentUtil.launchAppDetailsSettings()
                }
            }
        }
    }

    class HeadlessFragment : Fragment() {
        private var callback: ((Boolean) -> Unit)? = null

        private val permissionLauncher: ActivityResultLauncher<Array<String>> = registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { results ->
            callback?.invoke(results?.all { it.value == true } == true)
            removeFragment()
        }

        fun requestPermission(callback: ((Boolean) -> Unit)? = null, vararg perms: String) {
            this.callback = callback
            runCatching {
                permissionLauncher.launch(arrayOf(*perms))
            }.onFailure {
                IntentUtil.launchAppDetailsSettings()
            }
        }

        private fun removeFragment() {
            parentFragmentManager.beginTransaction().remove(this).commitAllowingStateLoss()
        }
    }
}