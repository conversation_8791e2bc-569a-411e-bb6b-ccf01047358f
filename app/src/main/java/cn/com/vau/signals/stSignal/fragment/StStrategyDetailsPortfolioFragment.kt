package cn.com.vau.signals.stsignal.fragment

import android.os.Bundle
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.activityViewModels
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseMvvmFragment
import cn.com.vau.data.strategy.StrategyBean
import cn.com.vau.databinding.FragmentRecyclerviewBinding
import cn.com.vau.databinding.HeaderRecyclerStrategyDetailsPortfolioBinding
import cn.com.vau.signals.stsignal.adapter.StStrategyDetailsPortfolioAdapter
import cn.com.vau.signals.stsignal.viewmodel.StStrategyDetailsPortfolioViewModel
import cn.com.vau.signals.stsignal.viewmodel.StStrategyDetailsViewModel
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.widget.NoDataView
import cn.com.vau.util.widget.dialog.BottomSelectListDialog

/**
 * author：lvy
 * date：2024/03/27
 * desc：策略详情->Portfolio
 */
class StStrategyDetailsPortfolioFragment :
    BaseMvvmFragment<FragmentRecyclerviewBinding, StStrategyDetailsPortfolioViewModel>() {

    private val strategyDetailsViewModel by activityViewModels<StStrategyDetailsViewModel>()
    private var strategyId: String? = null // 策略id。上个页面传入
    private var isProfile = false // 是否是个人中心进入的

    // pnl 和 daily change 切换
    private val changeDataList by lazy {
        arrayListOf(getString(R.string.pnl), getString(R.string.daily_change))
    }
    private val arrowIcon by lazy {
        ContextCompat.getDrawable(requireContext(), R.drawable.draw_bitmap2_triangle_down_tab_ca61e1e1e_c99ffffff)
    }

    private val mAdapter by lazy {
        StStrategyDetailsPortfolioAdapter().apply {
            setEmptyView(NoDataView(requireContext()).apply {
                setHintMessage(getString(R.string.no_records_found))
            })
        }
    }
    private val headerBinding by lazy {
        HeaderRecyclerStrategyDetailsPortfolioBinding.inflate(layoutInflater, mBinding.mRecyclerView, false)
    }

    override fun initParam(savedInstanceState: Bundle?) {
        arguments?.apply {
            strategyId = getString(Constants.STRATEGY_ID)
            isProfile = getBoolean("isProfile")
        }
    }

    override fun initView() {
        mBinding.mRecyclerView.setHasFixedSize(true)
        mBinding.mRecyclerView.adapter = mAdapter

        if (isProfile) { // 个人信息页面展示
            headerBinding.tvTips.text = getString(R.string.signal_investment_notice)
        } else { // 策略详情页展示
            headerBinding.tvTips.text = getString(R.string.the_following_information_investment_performance)
        }
    }

    override fun initData() {
        mViewModel.stStrategyPositionApi(strategyId)
    }

    override fun createObserver() {
        // 策略持仓列表获取成功
        mViewModel.strategyPositionLiveData.observe(this) {
            if (!it?.symbolInfo.isNullOrEmpty()) {
                mAdapter.removeHeaderView(headerBinding.root)
                mAdapter.addHeaderView(headerBinding.root)
            }
            val index = it?.showType ?: 1
            mViewModel.portfolioShowIndex = index
            headerBinding.tvDailyChange.text = changeDataList[index]
            setDailyChangeArrow(mViewModel.mBean) // 重新设置图标，否则阿拉伯语下布局会错
            mAdapter.setShowTypeAndCurrency(index, it?.currency) // 设置展示类型和币种
            mAdapter.setList(it?.symbolInfo)
        }
        // 刷新是否是自己策略
        strategyDetailsViewModel.strategyDetailTopLiveData.observe(this) {
            mViewModel.mBean = it
            // 用户个人资料页里面的已投资页卡下不调整，只有从信号源页面进入并且是自己的策略才需要调整
            setDailyChangeArrow(mViewModel.mBean)
        }
    }

    private fun setDailyChangeArrow(bean: StrategyBean?) {
        if (bean?.owner == true && !isProfile) {
            headerBinding.tvDailyChange.setCompoundDrawablesRelativeWithIntrinsicBounds(null, null, arrowIcon, null)
        }
    }

    override fun initListener() {
        // pnl 和 daily change 切换
        headerBinding.tvDailyChange.clickNoRepeat {
            val bean = strategyDetailsViewModel.strategyDetailTopLiveData.value ?: return@clickNoRepeat
            if (bean.owner) {
                changeData()
            }
        }
    }

    /**
     * pnl 和 daily change 切换
     */
    private fun changeData() {
        BottomSelectListDialog.Builder(requireActivity())
            .setTitle(getString(R.string.select))
            .setDataList(changeDataList)
            .setSelectIndex(mViewModel.portfolioShowIndex)
            .setOnItemClickListener {
                mViewModel.portfolioShowIndex = it
                // 设置策略详情页展示内容
                mViewModel.strategyPositionSetPortfolioShowApi(strategyId)
            }.build().showDialog()
    }

    companion object {
        fun newInstance(strategyId: String?, isProfile: Boolean = false) = StStrategyDetailsPortfolioFragment().apply {
            arguments = bundleOf(
                Constants.STRATEGY_ID to strategyId,
                "isProfile" to isProfile
            )
        }
    }
}