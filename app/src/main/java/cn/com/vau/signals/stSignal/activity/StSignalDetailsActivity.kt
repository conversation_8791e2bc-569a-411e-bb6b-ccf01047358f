package cn.com.vau.signals.stsignal.activity

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.view.expandabletextview.app.StatusType
import cn.com.vau.databinding.ActivityStSignalDetailsBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.signals.stsignal.center.activity.StSignalCenterActivity
import cn.com.vau.signals.stsignal.fragment.StSignalStrategyListFragment
import cn.com.vau.signals.stsignal.viewmodel.StSignalDetailsViewModel
import cn.com.vau.util.ImageLoaderUtil
import cn.com.vau.util.TabType
import cn.com.vau.util.arabicReverseTextByFlag
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.init
import cn.com.vau.util.setVp
import cn.com.vau.util.widget.dialog.BottomContentDialog
import cn.com.vau.util.widget.dialog.CenterActionDialog
import kotlin.math.abs

/**
 * author：lvy
 * date：2024/03/25
 * desc：信号源详情页
 */
@SuppressLint("SetTextI18n")
class StSignalDetailsActivity : BaseMvvmActivity<ActivityStSignalDetailsBinding, StSignalDetailsViewModel>() {

    private var stUserId: String? = null //上个页面传入
    private val strategyListFragment by lazy { StSignalStrategyListFragment() }

    private val bottomTipsDialog by lazy {
        BottomContentDialog.Builder(this)
    }

    override fun initParam(savedInstanceState: Bundle?) {
        stUserId = intent.getStringExtra(Constants.ST_USER_ID)
    }

    override fun initView() {
        //去除边界阴影，ViewPager2在xml设置不生效，必须代码设置
        val child = mBinding.viewPager2.getChildAt(0)
        if (child is RecyclerView) {
            child.overScrollMode = View.OVER_SCROLL_NEVER
        }

        initTabLayout()

        // 加载默认图片
        ImageLoaderUtil.loadImage(this, R.mipmap.ic_launcher, mBinding.ivAvatar)
    }

    override fun initData() {
        mViewModel.stSignalDetailsApi(stUserId)

    }

    override fun createObserver() {
        //信号源详情
        mViewModel.signalDetailsLiveData.observe(this) {
            setData()
        }
    }

    override fun initListener() {
        //标题是否显示
        mBinding.appbarLayout.addOnOffsetChangedListener { appBarLayout, verticalOffset ->
            val needShow = (abs(verticalOffset) >= appBarLayout.totalScrollRange)
            mBinding.mHeaderBar.getTitleView()?.isInvisible = !needShow
        }
        //标题栏
        mBinding.mHeaderBar.setEndIconClickListener {
            openActivity(HelpCenterActivity::class.java)
        }
        //简介。第二个参数false 只触发点击 不真正触发展开和收回操作
        mBinding.tvIntro.setExpandOrContractClickListener({
            if (it == StatusType.STATUS_EXPAND) {
                showBtoPpw()
            }
        }, false)
        //底部按钮，前往信号源中心
        mBinding.tvNext.clickNoRepeat {
            startActivity(StSignalCenterActivity.createIntent(this))
        }
    }

    /**
     * 初始化tab
     */
    private fun initTabLayout() {
        val fragments = arrayListOf<Fragment>()
        fragments.add(strategyListFragment)

        val titleList = arrayListOf<String>()
        titleList.add(getString(R.string.strategies))

        mBinding.viewPager2.init(fragments, titleList, supportFragmentManager, this)
        mBinding.mTabLayout.setVp(mBinding.viewPager2, titleList, TabType.LINE_INDICATOR)
    }

    /**
     * 设置页面数据
     */
    private fun setData() {
        val bean = mViewModel.signalDetailsLiveData.value ?: return
        //信号源信息
        bean.userDto?.let {
            //头像
            ImageLoaderUtil.loadImage(this, it.avatar, mBinding.ivAvatar, R.mipmap.ic_launcher)
            //信号源昵称
            mBinding.tvNick.text = it.nickname
            mBinding.mHeaderBar.setTitleText(it.nickname)
            //国家
            if (it.country.isNullOrBlank()) {
                mBinding.tvLocation.isVisible = false
            } else {
                mBinding.tvLocation.isVisible = true
                mBinding.tvLocation.text = it.country
            }
            // 信号源id
            mBinding.tvProviderId.text = "${getString(R.string.provider_id)}：${it.stUserId}".arabicReverseTextByFlag("：")
            //简介
            if (it.description.isNullOrBlank()) {
                mBinding.tvIntro.isVisible = false
            } else {
                mBinding.tvIntro.isVisible = true
                mBinding.tvIntro.setContent(it.description)
            }
            //跟单人数
            mBinding.tvCopiers.text = "${it.totalCopiers ?: 0}"
            //关注者人数
            mBinding.tvFollowers.text = it.totalFansCount ?: "0"
            //策略总数
            mBinding.tvStrategies.text = "${bean.strategies?.size ?: 0}"
            //信号源开户至今天的总天数
            mBinding.tvDaysJoines.text = it.daysJoin ?: "0"
        }
        //策略列表
        strategyListFragment.setList(bean.strategies)
        if (UserDataUtil.isStLogin()) {
            //底部按钮，是否自己看自己
            if (bean.status == true) {
                mBinding.tvNext.isVisible = true
            } else {
                mBinding.tvNext.isVisible = false
                strategyDelistedDialog()
            }
        } else {
            mBinding.tvNext.isVisible = false
        }
    }

    /**
     * 策略已下架的弹框提示
     */
    private fun strategyDelistedDialog() {
        val bean = mViewModel.signalDetailsLiveData.value ?: return
        if (bean.strategies.isNullOrEmpty()) {
            CenterActionDialog.Builder(this)
                .setContent(getString(R.string.the_signal_provider_not_public)) //设置内容
                .setSingleButton(true) //展示一个按钮，默认两个按钮
                .setSingleButtonText(getString(R.string.ok)) //设置单个按钮文本
                //如果展示一个按钮，点击监听使用setOnSingleButtonListener
                .setOnSingleButtonListener { textView ->
                    //默认关闭
                    finish()
                }
                .build()
                .showDialog()
        }
    }

    /**
     * 说明文案底部弹框
     */
    private fun showBtoPpw() {
        val bean = mViewModel.signalDetailsLiveData.value ?: return
        val intro = bean.userDto?.description ?: return
        bottomTipsDialog.setContent(intro)
            .build()
            .showDialog()
    }

    companion object {
        fun open(context: Context, stUserId: String? = null) {
            val intent = Intent(context, StSignalDetailsActivity::class.java)
            intent.putExtra(Constants.ST_USER_ID, stUserId)
            context.startActivity(intent)
        }
    }
}