package cn.com.vau.signals.stsignal.fragment

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.webkit.CookieManager
import android.webkit.JavascriptInterface
import android.webkit.WebSettings
import android.widget.FrameLayout
import androidx.core.os.bundleOf
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.view.popup.adapter.PlatAdapter
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.data.strategy.StrategyDetailsChartData
import cn.com.vau.data.strategy.StrategyDetailsChartResBean
import cn.com.vau.databinding.FragmentStStrategyDetailsOverviewBinding
import cn.com.vau.util.AppUtil
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.GsonUtil
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.json
import cn.com.vau.util.language.LanguageHelper
import cn.com.vau.util.widget.dialog.BottomSelectListDialog
import cn.com.vau.util.widget.dialog.base.BottomListDialog
import cn.com.vau.util.widget.webview.preload.PreloadWebViewFactory
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.StringJoiner

/**
 * author：lvy
 * date：2024/03/27
 * desc：策略详情->Overview
 */
class StStrategyDetailsOverviewFragment : BaseMvvmBindingFragment<FragmentStStrategyDetailsOverviewBinding>() {
    private var strategyId: String? = null // 策略id。上个页面传入

    private val bottomTipsDialog by lazy {
        BottomListDialog.Builder(requireActivity())
    }

    override fun initParam(savedInstanceState: Bundle?) {
        arguments?.apply {
            strategyId = getString(Constants.STRATEGY_ID)
        }
    }

    private val mWebView by lazy { PreloadWebViewFactory.getInstance().acquire(requireContext()) }

    override fun initView() {
        initWebView()
    }

    override fun initData() {
        val joiner = StringJoiner("&").apply {
            add("theme=${SpManager.getStyleState(0)}")
            add("token=${UserDataUtil.stToken()}")
            add("accountId=${UserDataUtil.stAccountId()}")
            add("strategyId=$strategyId")
            add("lang=${LanguageHelper.getHtmlLang()}")
            add("appVersion=${AppUtil.getVersionName()}")
            add("xtoken=${UserDataUtil.xToken()}")
            add("returnRateCycle=${SpManager.getReturnRateCycle()}")
            add("crmUserId=${SpManager.getCrmUserId()}")
        }

        val htmlUrl = "${HttpUrl.BaseHtmlUrl + HttpUrl.htmlUrlPrefix}/strategyChart?$joiner"
//        val htmlUrl = "http://************:8084/h5/feature/strategyChart?$joiner"
        mWebView?.loadUrl(htmlUrl)
    }

    @SuppressLint("SetJavaScriptEnabled", "JavascriptInterface")
    private fun initWebView() {
        val params = FrameLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
        mBinding.root.addView(mWebView, params)

        // 代码中设置webView背景色，解决夜间模式闪白屏现象
        mWebView?.setBackgroundColor(AttrResourceUtil.getColor(requireContext(), R.attr.mainLayoutBg))

        // 设置webView的一些属性
        val webSettings = mWebView?.settings

        // 自适应屏幕
        webSettings?.layoutAlgorithm = WebSettings.LayoutAlgorithm.SINGLE_COLUMN
        webSettings?.loadWithOverviewMode = true
        // 自适应可不设置，但是华为P10有问题
        webSettings?.textZoom = 100

        // 允许与js进行交互
        webSettings?.javaScriptEnabled = true
        webSettings?.databaseEnabled = true
        webSettings?.useWideViewPort = true // 关键点
        mWebView?.addJavascriptInterface(StStrategyDetailsOverviewInterface(), "vfx_android")

        // 从Android5.0开始，WebView默认不支持同时加载Https和Http混合模式。  url是https,内容http
        webSettings?.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
        webSettings?.allowFileAccess = true
        webSettings?.allowFileAccessFromFileURLs = true
        webSettings?.allowUniversalAccessFromFileURLs = true
        val cookieManager: CookieManager = CookieManager.getInstance()
        cookieManager.setAcceptThirdPartyCookies(mWebView, true)
        mWebView?.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
    }

    override fun onDestroy() {
        PreloadWebViewFactory.getInstance().release(mWebView)
        super.onDestroy()
    }

    /**
     * 说明文案底部弹框
     */
    private fun showBtoPpw(
        list: MutableList<HintLocalData>?,
        title: String? = null,
    ) {
        val adapter =
            PlatAdapter().apply {
                setList(list)
            }
        bottomTipsDialog.setTitle(title)
            .setAdapter(adapter)
            .build()
            .showDialog()
    }

    /**
     * h5图表选择年份交互
     */
    private fun chartSelectedYear(
        code: Int,
        yearList: List<String>,
        selectedPosition: Int,
    ) {
        BottomSelectListDialog
            .Builder(requireActivity())
            .setTitle(getString(R.string.signal_filter_detail_return))
            .setDataList(yearList)
            .setSelectIndex(selectedPosition)
            .setOnItemClickListener {
                val selectedYear = yearList.elementAtOrNull(it)
                val data = StrategyDetailsChartData(selectedYear = selectedYear)
                val res = StrategyDetailsChartResBean(code, data)
                val json = res.json
                mWebView?.evaluateJavascript("window.vfx_android.selectYearEvent($json)") {}
                // 保存上次选择的回报率
                SpManager.putReturnRateCycle(it)
            }.build()
            .showDialog()
    }

    inner class StStrategyDetailsOverviewInterface {
        @JavascriptInterface
        fun webPopupEvent(json: String) {
            val res = GsonUtil.fromJson(json, StrategyDetailsChartResBean::class.java)
            when (res?.code) {
                // Monthly Risk Band
                1 ->
                    showBtoPpw(
                        arrayListOf(
                            HintLocalData(
                                getString(R.string.monthly_risk_band),
                                getString(R.string.the_risk_band_represents),
                            ),
                        ),
                    )
                // Max Drawdown
                2 ->
                    showBtoPpw(
                        arrayListOf(
                            HintLocalData(
                                getString(R.string.max_drawdown),
                                getString(R.string.the_most_significant_this_trader),
                            ),
                        ),
                    )
                // Active Copiers
                3 ->
                    showBtoPpw(
                        arrayListOf(
                            HintLocalData(
                                getString(R.string.copiers_last_7_days),
                                getString(R.string.change_in_the_last_7_days),
                            ),
                        ),
                    )
                // Trades Stats
                4 ->
                    showBtoPpw(
                        arrayListOf(
                            HintLocalData(
                                getString(R.string.trades_stats),
                                getString(R.string.shows_the_total_the_profitable_orders),
                            ),
                        ),
                    )
                // others
                5 ->
                    showBtoPpw(
                        arrayListOf(
                            HintLocalData(
                                getString(R.string.active_since),
                                getString(R.string.when_the_copy_was_created),
                            ),
                            HintLocalData(
                                getString(R.string.settlement_frequency),
                                getString(R.string.the_profit_sharing_amount_settlement_cycle),
                            ),
                            HintLocalData(
                                getString(R.string.avg_holding_time_hours),
                                getString(R.string.average_time_spent_per_order),
                            ),
                            HintLocalData(
                                getString(R.string.trades_per_week),
                                getString(R.string.average_number_of_trades_per_week),
                            ),
                            HintLocalData(
                                getString(R.string.profitable_weeks),
                                getString(R.string.percentage_of_profitable_weeks_since_trading),
                            ),
                        ),
                        getString(R.string.others),
                    )
                // Copy Asset Under Management
                8 ->
                    showBtoPpw(
                        arrayListOf(
                            HintLocalData(
                                getString(R.string.copy_asset_under_management),
                                getString(R.string.the_sum_of_equities_this_strategy),
                            ),
                        ),
                    )
                // 回报率，358版本web端改成9了，之前的6和7已删除
                9 -> {
                    // 年份list
                    val years = res.data?.yearList
                    if (!years.isNullOrEmpty()) {
                        // 默认选中的下标，确保 selectedYear 不为 null
                        val selectedYear = res.data.selectedYear
                        var selectedPos = if (selectedYear != null) years.indexOfFirst { it == selectedYear } else 0
                        // 如果 selectedPos 为 -1，则返回 0，否则返回 selectedPos
                        selectedPos = selectedPos.takeIf { it != -1 } ?: 0
                        // int数组转string
                        val yearList = years.map { it.toString() }
                        chartSelectedYear(res.code, yearList, selectedPos)
                    }
                }
                // 回报率
                10 ->
                    showBtoPpw(
                        arrayListOf(
                            HintLocalData(
                                getString(R.string.strategy_return_statistics),
                                getString(R.string.returns_of_0_the_statistics),
                            ),
                        ),
                    )
                // 吐司
                11 ->
                    if (!res.message.isNullOrEmpty()) {
                        lifecycleScope.launch(Dispatchers.Main) {
                            ToastUtil.showToast(res.message)
                        }
                    }
            }
        }
    }

    companion object {
        fun newInstance(strategyId: String?) =
            StStrategyDetailsOverviewFragment().apply {
                arguments = bundleOf(Constants.STRATEGY_ID to strategyId)
            }
    }
}
