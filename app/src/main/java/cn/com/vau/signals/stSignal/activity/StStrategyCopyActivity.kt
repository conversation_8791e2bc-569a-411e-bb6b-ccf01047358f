package cn.com.vau.signals.stsignal.activity

import android.annotation.SuppressLint
import android.content.*
import android.os.Bundle
import android.text.Spanned
import android.text.SpannedString
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.View
import androidx.core.content.ContextCompat
import androidx.core.text.buildSpannedString
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import cn.com.vau.*
import cn.com.vau.common.base.mvvm.BaseMvvmActivity
import cn.com.vau.common.constants.*
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.*
import cn.com.vau.common.view.*
import cn.com.vau.common.view.popup.adapter.PlatAdapter
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.data.init.StShareAccountInfoData
import cn.com.vau.databinding.ActivityStStrategyCopyBinding
import cn.com.vau.page.StickyEvent
import cn.com.vau.page.html.HtmlActivity
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.signals.stsignal.dialog.BottomGSTipDialog
import cn.com.vau.signals.stsignal.viewmodel.StStrategyCopyViewModel
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import cn.com.vau.util.widget.dialog.*
import cn.com.vau.util.widget.dialog.base.BottomListDialog
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject

@SuppressLint("SetTextI18n")
class StStrategyCopyActivity : BaseMvvmActivity<ActivityStStrategyCopyBinding, StStrategyCopyViewModel>() {

    override fun initViewModels(): StStrategyCopyViewModel = getActivityViewModel(StStrategyCopyViewModel::class.java)

    override fun getLayoutId(): Int = R.layout.activity_st_strategy_copy

    private var strategyId: String? = null  // 策略id 上个页面传入
    private var copyModeStr: String? = null  // 跟随模式 下单成功时记录
    private val accountId by lazy { UserDataUtil.stAccountId() }
    private val currencyType by lazy { UserDataUtil.currencyType() }
    private val copyModeList by lazy {
        mutableListOf(
            getString(R.string.equivalent_used_margin), getString(R.string.fixed_lots), getString(R.string.fixed_multiples)
        )
    }
    private val verifyHandler by lazy { VerifyCompHandler() }
    private val tipsAdapter by lazy { PlatAdapter() }

    private var slValue: String = ""
    private var tpValue: String = ""
    private var isExpand = false    // Risk Management 是否已经展开
    private var tpEnabled = false   // Take Profit 开关
    private var slippageProtectionStatus = true    // Slippage Protection 开关
    private var lotRoundUpStatus = true            // Lot Round-Up 开关
    private var copyOpenedStatus = true            // Copy Opened Trades 开关
    private var formulaLotRoundUpStatus = true     // 等比例占用保证金模式下 Lot Round-Up 开关状态
    private var formulaCopyOpenedStatus = true     // 等比例占用保证金模式下 Copy Opened Trades 开关状态
    private var isNeedAuditing = false     // 下单成功后是否需要审核
    private var slippageProtectionVisible = false   // 0滑点开关是否展示

    private var copyModeSelectedIndex = 0
    private var minLots: Double = 0.01                  // 最小固定手数
    private var minMultiples: Double = 0.1              // 最小固定手数倍数
    private var minUsedMarginMultiples: Double = 1.0    // 最小保证金倍数
    private var minFollowAmount: Double = 0.00  // 最小跟单金额
    private var maxAvailableAmount: Double = 0.00 //最大可用金额
    private var availableCredit: Float = 0f    // 可用信用金
    private var followLimit: String = ""       // 每日跟单限制

    private val stShareAccountBean by lazy { copy(VAUSdkUtil.stShareAccountBean()) }

    private fun copy(bean: StShareAccountInfoData): StShareAccountInfoData {
        return StShareAccountInfoData().apply {
            equity = bean.equity
            margin = bean.margin
            credit = bean.credit
            profit = bean.profit
            balance = bean.balance
        }
    }

    override fun initParam() {
        strategyId = intent.extras?.getString(Constants.STRATEGY_ID, "").ifNull()
        copyModeStr = SpManager.getStrategyOrderCopyMode("")
    }

    override fun initView() {
        super.initView()
        val detailsStr = getString(R.string.details)
        val linkColor = ContextCompat.getColor(this, R.color.ce35728)
        mBinding.layoutStrategyCopyMode.tvLeverageDiffTip.text = "${getString(R.string.discrepancies_between_copiers_different_order_sizes)} $detailsStr"
        mBinding.layoutStrategyCopyMode.tvLeverageDiffTip.set(detailsStr, linkColor, false) {
            BottomContentDialog.Builder(this)
                .setTitle(getString(R.string.equivalent_used_margin))
                .setContent(getLeverageDiffTip())
                .build()
                .showDialog()
        }
        // 底部滑动按钮开关状态在设置CopyMode的时候就会同步设置上
        // 止盈开关默认关闭
        mBinding.layoutStrategyRiskManagement.sbTakeProfit.setState(tpEnabled)
        mBinding.layoutStrategyRiskManagement.tvSLTitle.text = "${getString(R.string.majuscule_sl)}:"
        mBinding.layoutStrategyRiskManagement.tvTPTitle.text = "${getString(R.string.majuscule_tp)}:"
        mBinding.layoutStrategyRiskManagement.tvTPValue.text = if (tpValue.isEmpty()) "--" else "${tpValue}%"
        mBinding.layoutStrategyRiskManagement.etStopLoss.hint = "5-95"
        mBinding.layoutStrategyRiskManagement.etTakeProfit.hint = "5-95"
        mViewModel.setTakeProfitEnabl(tpEnabled)
        mBinding.layoutStrategyCopyMode.etInvestmentAmount.setCurrencyType(currencyType)
        mBinding.layoutStrategyCopyMode.tvInvestmentCurrency.text = currencyType
//        maxAvailableAmount = stShareAccountBean.freeMargin.coerceAtLeast(0.00)
        mBinding.layoutStrategyCopyMode.dropListView.setData(copyModeList, getCopyModeSelectedIndex(copyModeStr), getString(R.string.copy_mode)).onSelected {
            mViewModel.setCopyMode(copyModeList.elementAtOrNull(it).ifNull())
        }
        mBinding.layoutStrategyRiskManagement.etStopLoss.setRangeAndDecimalPlaces(5, 95)
        mBinding.layoutStrategyRiskManagement.etTakeProfit.setRangeAndDecimalPlaces(5, 95)
        mBinding.layoutStrategyCopyMode.tvUsedCreditCurrency.text = currencyType
        mBinding.layoutStrategyCopyMode.tvUsedBalanceCurrency.text = currencyType
        // Available Balance
        var availableBalance =
            // 有持仓情况
            if (VAUSdkUtil.shareOrderList().isNotEmpty()) {
                CreditBalanceUtil.getAvailableBalance(stShareAccountBean.equity, stShareAccountBean.margin, stShareAccountBean.credit, stShareAccountBean.profit)
            }
            // 无持仓情况
            else {
                stShareAccountBean.balance.coerceAtLeast(0.0)
            }
        val availableBalanceText = availableBalance.numCurrencyFormat(currencyType, false)
        mBinding.layoutStrategyCopyMode.tvAvailableBalance.text = "${getString(R.string.available_balance)}: $availableBalanceText $currencyType"
        availableBalance = availableBalanceText.toDoubleCatching()
        // Available Credit
        availableCredit = CreditBalanceUtil.getAvailableCredit(availableBalance, stShareAccountBean.equity, stShareAccountBean.credit)
        val availableCreditText = availableCredit.numCurrencyFormat(currencyType, false)
        mBinding.layoutStrategyCopyMode.tvAvailableCredit.text = "${getString(R.string.available_credit)}: $availableCreditText $currencyType"
        availableCredit = availableCreditText.toFloatCatching()
        // Available Investment
        val availableInvestment = availableBalance.mathAdd(availableCredit)
        maxAvailableAmount = availableInvestment.coerceAtLeast(0.00)
        mBinding.layoutStrategyCopyMode.tvAvailableInvestment.text = "${getString(R.string.available_investment)}: ${maxAvailableAmount.numCurrencyFormat(currencyType)} $currencyType"
        // calc Used Credit / Used Balance
        calcUsedCreditAndBalance(stShareAccountBean.equity, stShareAccountBean.credit)
        observ()
    }

    override fun initData() {
        super.initData()
        mBinding.layoutStrategyRiskManagement.seekTakeProfit.setMinMax(5, 95, 35, "5%", "95%", "35%")
        mBinding.layoutStrategyRiskManagement.seekStopLoss.setMinMax(5, 95, slValue.ifEmpty { "65" }.toIntCatching(), "-5%", "-95%", "")
        calcStopLossAmount()

        mViewModel.strategyCopySubmitLoad(strategyId.ifNull(), accountId)
    }

    override fun initListener() {
        super.initListener()
        mBinding.layoutStrategyCopyMode.etLots.onFocusChangeListener = onFocusChangeListener
        mBinding.layoutStrategyCopyMode.etInvestmentAmount.onFocusChangeListener = onFocusChangeListener
        mBinding.layoutStrategyCopyMode.etMultiplesPerOrder.onFocusChangeListener = onFocusChangeListener
        mBinding.layoutStrategyCopyMode.etUsedMarginMultiples.onFocusChangeListener = onFocusChangeListener
        mBinding.layoutStrategyRiskManagement.etStopLoss.onFocusChangeListener = onFocusChangeListener
        mBinding.layoutStrategyRiskManagement.etTakeProfit.onFocusChangeListener = onFocusChangeListener
        mBinding.layoutStrategyRiskManagement.ivRiskManagementArrow.setOnClickListener(this)
        mBinding.layoutStrategyHeader.tvProfitSharing.setOnClickListener(this)
        mBinding.layoutStrategyHeader.tvSettlement.setOnClickListener(this)
        mBinding.layoutStrategyCopyMode.tvCopyMode.setOnClickListener(this)
        mBinding.layoutStrategyCopyMode.ivCopyModeTip.setOnClickListener(this)
        mBinding.layoutStrategyCopyMode.tvAvailableInvestment.setOnClickListener(this)
        mBinding.layoutStrategyCopyMode.tvUsedCredit.setOnClickListener(this)
        mBinding.layoutStrategyCopyMode.tvUsedBalance.setOnClickListener(this)
        mBinding.layoutStrategyCopyMode.tvGSUserBalanceTip.setOnClickListener(this)
        mBinding.layoutStrategyRiskManagement.tvGSUserRiskTip.setOnClickListener(this)
        mBinding.layoutStrategyRiskManagement.tvRiskManagement.setOnClickListener(this)
        mBinding.layoutStrategyRiskManagement.ivRiskManagementTip.setOnClickListener(this)
        mBinding.layoutStrategySwitch.tvSlippageProtection.setOnClickListener(this)
        mBinding.layoutStrategySwitch.tvLotRoundUp.setOnClickListener(this)
        mBinding.layoutStrategySwitch.tvCopyOpenTrade.setOnClickListener(this)
        mBinding.layoutStrategySwitch.ivSlippageProtectionIcon.setOnClickListener(this)
        mBinding.layoutStrategySwitch.ivLotRoundUpIcon.setOnClickListener(this)
        mBinding.layoutStrategySwitch.ivCopyOpenTradeIcon.setOnClickListener(this)
        // 监听键盘弹出和收起    0：收起
        KeyboardUtil.registerSoftInputChangedListener(this) {
            if (it == 0) {
                checkSLandTP()
            }
        }
        mBinding.layoutStrategyCopyMode.etInvestmentAmount.doAfterTextChanged {
            val investAmount = it.ifNull().toString()
            if (investAmount.mathCompTo(maxAvailableAmount.numCurrencyFormat(isRound = true)) == 1) {
                val maxInput = maxAvailableAmount.numCurrencyFormat(currencyType, true)
                mBinding.layoutStrategyCopyMode.etInvestmentAmount.setText(maxInput)
                mBinding.layoutStrategyCopyMode.etInvestmentAmount.setSelection(maxInput.length)
            }
            calcStopLossAmount()
            calcTakeProfitAmount()
            calcUsedCreditAndBalance(stShareAccountBean.equity, stShareAccountBean.credit)
        }
        mBinding.layoutStrategyRiskManagement.etStopLoss.doAfterTextChanged {
            slValue = it.ifNull().toString()
            mBinding.layoutStrategyRiskManagement.tvSLValue.text = "${slValue}%"
            val progress = slValue.toIntCatching()
            mBinding.layoutStrategyRiskManagement.seekStopLoss.setProgress(progress)
            calcStopLossAmount()
        }
        mBinding.layoutStrategyRiskManagement.etTakeProfit.doAfterTextChanged {
            tpValue = it.ifNull().toString()
            mBinding.layoutStrategyRiskManagement.tvTPValue.text = if (tpValue.isEmpty()) "--" else "${tpValue}%"
            val progress = tpValue.toIntCatching()
            mBinding.layoutStrategyRiskManagement.seekTakeProfit.setProgress(progress)
            calcTakeProfitAmount()
        }
        mBinding.layoutStrategyCopyMode.etMultiplesPerOrder.doAfterTextChanged {
            var input = it.ifNull().toString()
            if (input.startsWith(".")) {
                input = "0$input"
                mBinding.layoutStrategyCopyMode.etMultiplesPerOrder.setText(input)
                mBinding.layoutStrategyCopyMode.etMultiplesPerOrder.setSelection(input.length)
            }
            val num = input.toFloatCatching()
            val perOrder = if (num == 0f) "1" else num.toString()
            mBinding.layoutStrategyCopyMode.tvMultiplesPerOrderTip.text = getString(R.string.the_position_opened_x_the_strategy, perOrder)
        }
        mBinding.layoutStrategyCopyMode.etUsedMarginMultiples.doAfterTextChanged {
            var input = it.ifNull().toString()
            if (input.startsWith(".") || input.startsWith("0")) {
                input = ""
                mBinding.layoutStrategyCopyMode.etUsedMarginMultiples.setText(input)
            }
            val times = input.toFloatCatching().toString()
            mBinding.layoutStrategyCopyMode.tvUsedMarginMultiplesTip.text = getString(R.string.the_margin_percentage_x_strategys_default, times)
        }
        mBinding.layoutStrategyRiskManagement.seekStopLoss.setProgressCallBack {
            mViewModel.setSL(it.toString())
        }
        mBinding.layoutStrategyRiskManagement.seekTakeProfit.setProgressCallBack {
            mViewModel.setTP(it.toString())
        }
        mBinding.layoutStrategyRiskManagement.sbTakeProfit.setStateChangeListener { isChecked ->
            mViewModel.setTakeProfitEnabl(isChecked)
        }
        mBinding.layoutStrategySwitch.sbSlippageProtection.setOnClickListener {
            slippageProtectionStatus = !slippageProtectionStatus
            mBinding.layoutStrategySwitch.sbSlippageProtection.setState(slippageProtectionStatus)
            if (slippageProtectionStatus) {
                copyOpenedStatus = false
                if (copyModeSelectedIndex == 0) {
                    formulaCopyOpenedStatus = copyOpenedStatus
                }
                mBinding.layoutStrategySwitch.sbCopyOpenTrade.setState(copyOpenedStatus)
            }
        }
        mBinding.layoutStrategySwitch.sbLotRoundUp.setOnClickListener {
            if (copyModeSelectedIndex == 0) {
                lotRoundUpStatus = !lotRoundUpStatus
                formulaLotRoundUpStatus = lotRoundUpStatus
                mBinding.layoutStrategySwitch.sbLotRoundUp.setState(lotRoundUpStatus)
            } else {
                ToastUtil.showToast(getString(R.string.the_feature_is_used_margin_mode))
            }
        }
        mBinding.layoutStrategySwitch.sbCopyOpenTrade.setOnClickListener {
            if (copyModeSelectedIndex == 0) {
                // 判断这次点击是否要打开开关 && 0滑点开关展示 && 0滑点开关开启
                if (!copyOpenedStatus && slippageProtectionVisible && slippageProtectionStatus) {
                    ToastUtil.showToast(getString(R.string.this_function_is_is_enabled))
                    return@setOnClickListener
                }
                copyOpenedStatus = !copyOpenedStatus
                formulaCopyOpenedStatus = copyOpenedStatus
                mBinding.layoutStrategySwitch.sbCopyOpenTrade.setState(copyOpenedStatus)
            } else {
                ToastUtil.showToast(getString(R.string.the_feature_is_used_margin_mode))
            }
        }
    }

    private fun setVerifyComponment() {
        val views = mutableListOf<VerifyComponent>()
        views.add(mBinding.layoutStrategyCopyMode.etInvestmentAmount)
        views.add(mBinding.layoutStrategyRiskManagement.etStopLoss)
        if (copyModeSelectedIndex == 1) {
            views.add(mBinding.layoutStrategyCopyMode.etLots)
        } else if (copyModeSelectedIndex == 2) {
            views.add(mBinding.layoutStrategyCopyMode.etMultiplesPerOrder)
        } else {
            views.add(mBinding.layoutStrategyCopyMode.etUsedMarginMultiples)
        }
        if (tpEnabled) {
            views.add(mBinding.layoutStrategyRiskManagement.etTakeProfit)
        }

        verifyHandler.add(views).to(mBinding.tvNext).submit {
            if (checkParameters()) {
                val investmentAmount = mBinding.layoutStrategyCopyMode.etInvestmentAmount.text.toString()
                val lots = mBinding.layoutStrategyCopyMode.etLots.text.toString()
                val multiples = mBinding.layoutStrategyCopyMode.etMultiplesPerOrder.text.toString()
                val usedMarginMultiples = mBinding.layoutStrategyCopyMode.etUsedMarginMultiples.text.toString()

                val params = hashMapOf(
                    "strategyId" to strategyId.ifNull(),
                    "accountId" to accountId,
                    "copyMode" to when (copyModeSelectedIndex) {
                        1 -> Constants.STRATEGY_COPY_MODE_FIXED_VOLUME
                        2 -> Constants.STRATEGY_COPY_MODE_FIXED_MAGNIFICATION
                        else -> Constants.STRATEGY_COPY_MODE_FORMULA
                    },
                    "copyModeValue" to when (copyModeSelectedIndex) {
                        1 -> lots.toDoubleCatching().toString()
                        2 -> multiples.toDoubleCatching().toString()
                        else -> usedMarginMultiples.toDoubleCatching().toString()
                    },
                    "followAmount" to investmentAmount.toDoubleCatching().toString(),
                    "stopLossPercentage" to slValue,
                    "takeProfitPercentage" to if (tpEnabled) tpValue else "",
                    "minVolRoundup" to lotRoundUpStatus.toString(),
                    "copyExistingPositions" to copyOpenedStatus.toString()
                )
                if (slippageProtectionVisible) {
                    params["slippageProtection"] = slippageProtectionStatus.toString()
                }
                // 判断是否是GS活动策略 && 是GS地区用户
                checkGSActiveSignUp {
                    mViewModel.strategyCopySubmit(params)
                }
                // 神策埋点，按钮点击事件
                sensorsTrack(true)
                mViewModel.sensorsCopyTradingSubmitClick()
            }
        }
    }

    private fun observ() {
        // 加载数据接口
        mViewModel.strategyCopyLoadLiveData.observe(this) {
            val isKYCProcess = SpManager.isV1V2()
            if (it.code == "100011" && isKYCProcess) {
                KycVerifyHelper.checkKycVerify(
                    this,
                    it.data.nextLevel.toIntCatching(),
                    it.data.titleString.ifNull(),
                    it.data.descriptionString.ifNull()
                )
                return@observe
            }
            if (it.code != "200") {
                ToastUtil.showToast(it.msg)
                return@observe
            }
            if (it.data?.offLine == true) {
                CenterActionDialog.Builder(this)
                    .setContent(getString(R.string.this_strategy_has_signal_provider))
                    .setSingleButtonText(getString(R.string.ok))
                    .setSingleButton(true)
                    .setOnSingleButtonListener {
                        finish()
                    }
                    .build()
                    .showDialog()
                return@observe
            }
            val data = it.data
            mViewModel.selfLeverage = data?.accountLeverage.ifNull()
            mViewModel.strategyLeverage = data?.strategyLeverage.ifNull()
            mViewModel.profitShieldStrategy = data?.isProfitShieldStrategy == true
            mBinding.layoutStrategyCopyMode.tvLeverageSelf.text = getString(R.string.your_leverage_ratio_x, mViewModel.selfLeverage)
            mBinding.layoutStrategyCopyMode.tvLeverageStrategy.text = getString(R.string.strategy_leverage_ratio_x, mViewModel.strategyLeverage)
            mBinding.layoutStrategyCopyMode.tvGSUserBalanceTip.text =
                getString(R.string.to_qualify_for_the_x_other_currencies, "${data?.eligibleFundLevel.ifNull()} USD")
            mBinding.layoutStrategyCopyMode.tvGSUserBalanceTip.isVisible = mViewModel.profitShieldStrategy
            val copyModeIndex = getCopyModeSelectedIndex(copyModeStr)
            mViewModel.setCopyMode(copyModeList.elementAtOrNull(copyModeIndex).ifNull())    // 再次触发

            ImageLoaderUtil.loadImage(this, data?.avatar.ifNull(), mBinding.layoutStrategyHeader.ivAvatar, R.mipmap.ic_launcher)
            slValue = data?.stopLossDefaultPercentage.ifNull("65")
            mBinding.layoutStrategyRiskManagement.tvSLValue.text = "${slValue}%"
            mBinding.layoutStrategyRiskManagement.etStopLoss.setText(slValue)
            mBinding.layoutStrategyRiskManagement.seekStopLoss.setMinMax(5, 95, slValue.ifEmpty { "65" }.toIntCatching(), "-5%", "-95%", "")
            mBinding.layoutStrategyRiskManagement.tvGSUserRiskTip.text =
                getString(R.string.to_qualify_for_the_above_x, "${data?.lossCoverPercentage.toFloatCatching().times(100).toInt()}%")
            mBinding.layoutStrategyRiskManagement.tvGSUserRiskTip.isVisible =
                mViewModel.profitShieldStrategy && mBinding.layoutStrategyRiskManagement.groupSLandTP.isVisible
            mBinding.layoutStrategyHeader.tvNick.text = data?.strategyName.ifNull()
            mBinding.layoutStrategyHeader.tvIdKey.text = "${getString(R.string.strategy_id)}："
            mBinding.layoutStrategyHeader.tvStrategyId.text = data?.strategyNo.ifNull()
            mBinding.layoutStrategyHeader.tvRoiRate.text = "${data?.returnRate.ifNull().toString().percent()}%"
            mBinding.layoutStrategyHeader.tvRoiRate.setTextColor(getReturnRateColor(data?.returnRate.ifNull()))
            mBinding.layoutStrategyHeader.tvProfitSharingRate.text = "${data?.profitSharePercentage.ifNull().toString().percent(0)}%"
            mBinding.layoutStrategyHeader.tvSettlementValue.text = getSettlement(data?.settlementCycle.ifNull())
            // 固定手数：0.01 和 策略门槛之间的最大值
            minLots = 0.01.coerceAtLeast(data?.minFollowVolume.ifNull())
            mBinding.layoutStrategyCopyMode.etLots.setText("$minLots")
            mBinding.layoutStrategyCopyMode.etLots.hint = "$minLots-100"
            mBinding.layoutStrategyCopyMode.etLots.setRangeAndDecimalPlaces(minLots, 100)
            // 固定倍数：1 和 策略门槛之间的最大值
            minMultiples = 0.1.coerceAtLeast(data?.minFollowMultiplier.ifNull())
            mBinding.layoutStrategyCopyMode.etMultiplesPerOrder.setText(minMultiples.formatProductPrice(1, false))
            mBinding.layoutStrategyCopyMode.etMultiplesPerOrder.hint = "$minMultiples-50.0"
            mBinding.layoutStrategyCopyMode.etMultiplesPerOrder.setRangeAndDecimalPlaces(minMultiples, 50.0, 1)
            // 等比例占用保证金：1 和 策略门槛之间的最大值
            minUsedMarginMultiples = 1.0.coerceAtLeast(data?.minLotsRatioPerOrder.ifNull())
            val savedUsedMarginMultiples = SpManager.getFormulaModeValueByUser()
            val usedMarginMultiplesText =
                if (savedUsedMarginMultiples.isNotEmpty()) {
                    minUsedMarginMultiples.coerceAtLeast(savedUsedMarginMultiples.toDoubleCatching())
                } else {
                    minUsedMarginMultiples
                }
            mBinding.layoutStrategyCopyMode.etUsedMarginMultiples.setText(usedMarginMultiplesText.formatProductPrice(1, false))
            mBinding.layoutStrategyCopyMode.etUsedMarginMultiples.hint = "$minUsedMarginMultiples-50.0"
            mBinding.layoutStrategyCopyMode.etUsedMarginMultiples.setRangeAndDecimalPlaces(minUsedMarginMultiples, 50.0, 1)
            // 根据用户跟单账户的币种、策略要求的最低跟单金额、当前的汇率，由后台计算出来的最低跟单金额，进行自动预填
            minFollowAmount = data?.minFollowAmount.ifNull()
            mBinding.layoutStrategyCopyMode.etInvestmentAmount.setText(minFollowAmount.numCurrencyFormat(currencyType))
            mBinding.layoutStrategyCopyMode.etInvestmentAmount.setHint("${getString(R.string.min_dot)}${minFollowAmount.numCurrencyFormat(currencyType)}")
            followLimit = data?.followLimit.ifNull()
            mBinding.tvLimitTips.text = getString(R.string.a_strategy_can_current_count_x, followLimit)
            // 0滑点开关
            slippageProtectionVisible = data?.slippageProtection.ifNull()
            mBinding.layoutStrategySwitch.groupSlippageProtection.isVisible = slippageProtectionVisible
            if (slippageProtectionVisible) {
                slippageProtectionStatus = SpManager.getStrategyOrderSlippageProtectionStatus()
                mBinding.layoutStrategySwitch.sbSlippageProtection.setState(slippageProtectionStatus)
                // 如果0滑点开关打开，则关闭跟单开关（业务逻辑）
                if (slippageProtectionStatus) {
                    copyOpenedStatus = false
                    mBinding.layoutStrategySwitch.sbCopyOpenTrade.setState(copyOpenedStatus)
                }
            }
            sensorsTrack() // 神策埋点，页面浏览事件
        }

        // 下单提交接口
        mViewModel.strategyCopySubmitLiveData.observe(this) {
            val isKYCProcess = SpManager.isV1V2()
            if (it.code == "100011" && isKYCProcess) {
                KycVerifyHelper.checkKycVerify(
                    this,
                    it.data.nextLevel.toIntCatching(),
                    it.data.titleString.ifNull(),
                    it.data.descriptionString.ifNull()
                )
                return@observe
            }
            if (it.code != "200") {
                ToastUtil.showToast(it.msg)
                // 埋点
                mViewModel.sensorsCopyTradingEventSubmitResult(false)
                return@observe
            }
            // 记录成功提交时的跟随模式
            SpManager.putStrategyOrderCopyMode(
                when (copyModeSelectedIndex) {
                    1 -> Constants.STRATEGY_COPY_MODE_FIXED_VOLUME
                    2 -> Constants.STRATEGY_COPY_MODE_FIXED_MAGNIFICATION
                    else -> Constants.STRATEGY_COPY_MODE_FORMULA
                }
            )
            // 记录成功提交时的0滑点状态
            SpManager.putStrategyOrderSlippageProtectionStatus(slippageProtectionStatus)
            // 记录成功提交时"等比例占用保证金"模式的倍数
            if (copyModeSelectedIndex == 0) {
                val usedMarginMultiples = mBinding.layoutStrategyCopyMode.etUsedMarginMultiples.text.toString()
                SpManager.putFormulaModeValueByUser(usedMarginMultiples.toFloatCatching().toString())
            }

            isNeedAuditing = it.data?.reviewDeadline?.isNotEmpty() == true  // 如果有自动审核时间，代表需要审核
            val timestamp = if (isNeedAuditing) it.data?.reviewDeadline else null
            CenterActionWithIconDialog.Builder(this)
                .setLottieIcon(R.raw.lottie_dialog_ok)
                .setTitle(if (isNeedAuditing) getString(R.string.submit_successful) else getString(R.string.copy_successful))
                .setContent(
                    if (isNeedAuditing) getString(R.string.signal_provider_will_order_by_x, getDateStr(timestamp))
                    else getString(R.string.the_copied_position_is_being_established_please_check_later)
                )
                .setSingleButton(true)
                .setSingleButtonText(getString(R.string.ok))
                .setOnSingleButtonListener {
                    EventBus.getDefault().postSticky(
                        StickyEvent(
                            if (isNeedAuditing)
                                NoticeConstants.MAIN_SHOW_ORDERS_ITEM_STRATEGY_PENDING_REVIEW
                            else
                                NoticeConstants.MAIN_SHOW_ORDERS_ITEM_STRATEGY_OPEN
                        )
                    )
                    // 关闭除主页的所有Activity
                    ActivityManagerUtil.getInstance().finishOtherActivities(MainActivity::class.java)
                }
                .build()
                .showDialog()
            // 埋点
            mViewModel.sensorsCopyTradingEventSubmitResult(true)
        }

        // 选择Copy Mode
        mViewModel.currentMode.observe(this) {
            when (it) {
                getString(R.string.equivalent_used_margin) -> {
                    copyModeSelectedIndex = 0
                    mBinding.layoutStrategyCopyMode.groupUsedMarginMultiples.isVisible = true
                    mBinding.layoutStrategyCopyMode.groupLotsPerOrder.isVisible = false
                    mBinding.layoutStrategyCopyMode.groupMultiplesPerOrder.isVisible = false
                    mBinding.layoutStrategyCopyMode.groupLeverageTip.isVisible = mViewModel.selfLeverage != mViewModel.strategyLeverage
                    // 产品要求切换回此模式时，恢复之前在此模式下选中的开关状态
                    lotRoundUpStatus = formulaLotRoundUpStatus
                    copyOpenedStatus = if (slippageProtectionVisible && slippageProtectionStatus) false else formulaCopyOpenedStatus
                    mBinding.layoutStrategySwitch.sbLotRoundUp.setState(lotRoundUpStatus)
                    mBinding.layoutStrategySwitch.sbCopyOpenTrade.setState(copyOpenedStatus)
                    setVerifyComponment()
                }

                getString(R.string.fixed_lots) -> {
                    copyModeSelectedIndex = 1
                    mBinding.layoutStrategyCopyMode.groupUsedMarginMultiples.isVisible = false
                    mBinding.layoutStrategyCopyMode.groupLotsPerOrder.isVisible = true
                    mBinding.layoutStrategyCopyMode.groupMultiplesPerOrder.isVisible = false
                    mBinding.layoutStrategyCopyMode.groupLeverageTip.isVisible = false
                    lotRoundUpStatus = false
                    copyOpenedStatus = false
                    mBinding.layoutStrategySwitch.sbLotRoundUp.setState(lotRoundUpStatus)
                    mBinding.layoutStrategySwitch.sbCopyOpenTrade.setState(copyOpenedStatus)
                    setVerifyComponment()
                }

                getString(R.string.fixed_multiples) -> {
                    copyModeSelectedIndex = 2
                    mBinding.layoutStrategyCopyMode.groupUsedMarginMultiples.isVisible = false
                    mBinding.layoutStrategyCopyMode.groupLotsPerOrder.isVisible = false
                    mBinding.layoutStrategyCopyMode.groupMultiplesPerOrder.isVisible = true
                    mBinding.layoutStrategyCopyMode.groupLeverageTip.isVisible = false
                    lotRoundUpStatus = false
                    copyOpenedStatus = false
                    mBinding.layoutStrategySwitch.sbLotRoundUp.setState(lotRoundUpStatus)
                    mBinding.layoutStrategySwitch.sbCopyOpenTrade.setState(copyOpenedStatus)
                    setVerifyComponment()
                }
            }
        }

        mViewModel.stoplossValue.observe(this) {
            mBinding.layoutStrategyRiskManagement.etStopLoss.clearFocus()
            mBinding.layoutStrategyRiskManagement.etStopLoss.setText(it)
        }

        mViewModel.takeProfitValue.observe(this) {
            mBinding.layoutStrategyRiskManagement.etTakeProfit.clearFocus()
            mBinding.layoutStrategyRiskManagement.etTakeProfit.setText(it)
        }

        // 开启TP 滑动按钮
        mViewModel.takeProfitEnable.observe(this) {
            tpEnabled = it
            setVerifyComponment()
            mBinding.layoutStrategyRiskManagement.clTakeProfitMask.alpha = if (it) 1f else 0.25f
            mBinding.layoutStrategyRiskManagement.etTakeProfit.isEnabled = it
            mBinding.layoutStrategyRiskManagement.seekTakeProfit.setEnable(it)
            tpValue = if (it) {
                tpValue.ifEmpty { "35" }
            } else {
                ""
            }
            mBinding.layoutStrategyRiskManagement.etTakeProfit.setText(tpValue)
        }
    }

    private fun calcUsedCreditAndBalance(equity: Double, credit: Double) {
        val investmentText = mBinding.layoutStrategyCopyMode.etInvestmentAmount.text.toString().ifNull("0")
        val maxInput = maxAvailableAmount.numCurrencyFormat(currencyType, true)
        val investment = if (investmentText.mathCompTo(maxInput) == 1) maxInput else investmentText
        var usedBalance = CreditBalanceUtil.getUsedBalance(investment, equity, credit)
        val usedBalanceText = usedBalance.numCurrencyFormat(currencyType, false)
        usedBalance = usedBalanceText.toFloatCatching()
        mBinding.layoutStrategyCopyMode.tvUsedBalanceAmount.text = usedBalanceText
        val usedCredit = investment.mathSub(usedBalance.toString()).toDoubleCatching()
        mBinding.layoutStrategyCopyMode.tvUsedCreditAmount.text = usedCredit.coerceAtLeast(0.0).numCurrencyFormat(currencyType, false)
        // 已用信用金 > 可用信息金 时，强制设置为可用信用金，并反计算已用余额
        if (usedCredit > availableCredit) {
            mBinding.layoutStrategyCopyMode.tvUsedCreditAmount.text = availableCredit.coerceAtLeast(0f).numCurrencyFormat(currencyType, false)
            val usedBalanceNew = investmentText.mathSub(availableCredit.toString())
            mBinding.layoutStrategyCopyMode.tvUsedBalanceAmount.text = usedBalanceNew.numCurrencyFormat(currencyType, false)
        }
    }

    private fun calcStopLossAmount() {
        val investAmount = mBinding.layoutStrategyCopyMode.etInvestmentAmount.text.toString().ifNull("0")
        val amount = investAmount.mathMul(slValue.ifEmpty { "0" }).mathDiv("100", 2)
        mBinding.layoutStrategyRiskManagement.tvEstimatedLoss.text = "${getString(R.string.estimated_loss)}: ${amount.numCurrencyFormat(currencyType)} $currencyType"
    }

    private fun calcTakeProfitAmount() {
        val investAmount = mBinding.layoutStrategyCopyMode.etInvestmentAmount.text.toString().ifNull("0")
        val amount = investAmount.mathMul(tpValue.ifEmpty { "0" }).mathDiv("100", 2)
        mBinding.layoutStrategyRiskManagement.tvEstimatedProfit.text = "${getString(R.string.estimated_profit)}: ${amount.numCurrencyFormat(currencyType)} $currencyType"
    }

    private fun getDateStr(longStr: String?): String {
        val timestamp = kotlin.runCatching { longStr?.toLong() }.getOrNull()
        timestamp?.let {
            return TimeUtil.millis2String(timestamp, "dd/MM/yyyy")
        }
        return ""
    }

    private fun getCopyModeSelectedIndex(copyMode: String?): Int {
        return copyMode?.let {
            when (it) {
                Constants.STRATEGY_COPY_MODE_FIXED_VOLUME -> 1
                Constants.STRATEGY_COPY_MODE_FIXED_MAGNIFICATION -> 2
                else -> 0
            }
        } ?: 0
    }

    private fun checkSLandTP() {
        val slvalue = mBinding.layoutStrategyRiskManagement.etStopLoss.text.toString().toIntCatching()
        if (slvalue < 5) {
            mBinding.layoutStrategyRiskManagement.etStopLoss.setText("5")
        }
        if (tpEnabled) {
            val tpvalue = mBinding.layoutStrategyRiskManagement.etTakeProfit.text.toString().toIntCatching()
            if (tpvalue < 5) {
                mBinding.layoutStrategyRiskManagement.etTakeProfit.setText("5")
            }
        }
        window?.decorView?.clearFocus()
    }

    private fun getReturnRateColor(value: Double) = when {
        value >= 0 -> ContextCompat.getColor(this, R.color.c00c79c)
        else -> ContextCompat.getColor(this, R.color.ce35728)
    }

    private fun checkParameters(): Boolean {
        val investmentAmount = mBinding.layoutStrategyCopyMode.etInvestmentAmount.text.toString()
        val lots = mBinding.layoutStrategyCopyMode.etLots.text.toString()
        val multiples = mBinding.layoutStrategyCopyMode.etMultiplesPerOrder.text.toString()
        val usedMarginMultiples = mBinding.layoutStrategyCopyMode.etUsedMarginMultiples.text.toString()
        val slScale = slValue.toIntCatching()
        val tpScale = tpValue.toIntCatching()

        if (investmentAmount.toDoubleCatching() < minFollowAmount) {
            ToastUtil.showToast(getString(R.string.the_minimum_investment_x, "${minFollowAmount.numCurrencyFormat(currencyType)} $currencyType"))
            return false
        }
        if (investmentAmount.toDoubleCatching() > maxAvailableAmount) {
            ToastUtil.showToast(getString(R.string.free_margin_is_not_enough))
            return false
        }
        if (copyModeSelectedIndex == 1) {
            if (lots.toDoubleCatching() < minLots) {
                ToastUtil.showToast(getString(R.string.please_enter_a_valid_value))
                return false
            }
        } else if (copyModeSelectedIndex == 2) {
            if (multiples.toDoubleCatching() < minMultiples) {
                ToastUtil.showToast(getString(R.string.please_enter_a_valid_value))
                return false
            }
        } else if (copyModeSelectedIndex == 0) {
            if (usedMarginMultiples.toDoubleCatching() < minUsedMarginMultiples) {
                mBinding.layoutStrategyCopyMode.etUsedMarginMultiples.setText("")
                ToastUtil.showToast(getString(R.string.please_enter_a_valid_value))
                return false
            }
        }
        if (slScale < 5 || slScale > 95) {
            ToastUtil.showToast(getString(R.string.stop_loss_should_be_5_and_95))
            return false
        }
        if (tpEnabled) {
            if (tpScale < 5 || tpScale > 95) {
                ToastUtil.showToast(getString(R.string.take_profit_should_be_5_and_95))
                return false
            }
        }

        return true
    }

    private fun getSettlement(settlement: Int): String {
        return when (settlement) {
            1 -> getString(R.string.daily)
            2 -> getString(R.string.weekly)
            3 -> getString(R.string.monthly)
            else -> ""
        }
    }

    private fun getLeverageDiffTip(): SpannedString {
        return buildSpannedString {
            append(getString(R.string.your_leverage_ratio_x, mViewModel.selfLeverage) + "\n")
            append(getString(R.string.strategy_leverage_ratio_x, mViewModel.strategyLeverage) + "\n")
            append("\n")
            append(getString(R.string.discrepancies_between_copiers_different_order_sizes) + "\n")
            append("\n")
            append(getString(R.string.leverage_ratio_and_order_size) + "\n")
            append("\n")
            append(getString(R.string.leverage_diff_tip1) + "\n")
            append("\n")
            append(getString(R.string.leverage_diff_tip2) + "\n")
            append(getString(R.string.leverage_diff_tip3) + "\n")
            append("\n")
            append(getString(R.string.leverage_diff_tip4) + "\n")
            append(getString(R.string.leverage_diff_tip5) + "\n")
            append("\n")
            append(getString(R.string.margin_calculation_mode_for_each_symbols))
        }
    }

    private fun checkGSActiveSignUp(next: (() -> Unit)? = null) {
        if (mViewModel.profitShieldStrategy && UserDataUtil.isGSAccount()) {
            val dialog = CenterActionDialog.Builder(this)
                .setStartText(getString(R.string.cancel))
                .setEndText(getString(R.string.submit))
                .setOnStartListener {
                    // 埋点
                    mViewModel.sensorsCopyTradingEventSubmitClick(false)
                }
                .setOnEndListener {
                    next?.invoke()
                    // 埋点
                    mViewModel.sensorsCopyTradingEventSubmitClick(true)
                }
                .build()
            val contentTextView = dialog.getContentViewBinding().tvDetail
            val text = buildSpannedString {
                append(getString(R.string.by_proceeding_to_you_the_program))
                val tncApply = getString(R.string.tnc_apply)
                val start = length
                append(tncApply)
                val end = length
                setSpan(object : ClickableSpan() {
                    override fun onClick(widget: View) {
                        // 点击事件处理逻辑
                        NewHtmlActivity.openActivity(
                            this@StStrategyCopyActivity,
                            url = "https://webh5.vantagemarketapp.com/app-web/202505-copy-trade-growth-shield-tc-tnc/",
                            title = getString(R.string.copy_trade_growth_and_conditions)
                        )
                    }

                    override fun updateDrawState(ds: TextPaint) {
                        super.updateDrawState(ds)
                        ds.isUnderlineText = true
                        ds.color = ContextCompat.getColor(this@StStrategyCopyActivity, R.color.ce35728)
                    }
                }, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                contentTextView.movementMethod = LinkMovementMethod.getInstance()
                contentTextView.highlightColor = ContextCompat.getColor(this@StStrategyCopyActivity, R.color.transparent)
            }
            (dialog as CenterActionDialog).setContent(text)
            dialog.showDialog()
        } else {
            next?.invoke()
        }
    }

    private val onFocusChangeListener = View.OnFocusChangeListener { v, hasFocus ->
        v?.setBackgroundResource(
            if (hasFocus)
                R.drawable.draw_shape_stroke_c1e1e1e_cebffffff_solid_c0a1e1e1e_c0affffff_r10
            else
                R.drawable.draw_shape_c0a1e1e1e_c0affffff_r10
        )
    }

    override fun onClick(view: View?) {
        super.onClick(view)
        when (view?.id) {

            R.id.tvProfitSharing -> {   // 跳转分润示例页
                openActivity(HtmlActivity::class.java, Bundle().apply {
                    putInt("tradeType", 24)
                })
            }

            R.id.tvSettlement -> {
                tipsAdapter.setList(
                    arrayListOf(
                        HintLocalData(getString(R.string.the_profit_sharing_amount_settlement_cycle))
                    )
                )
                BottomListDialog.Builder(this)
                    .setTitle(getString(R.string.settlement_frequency))
                    .setAdapter(tipsAdapter)
                    .build()
                    .showDialog()
            }

            R.id.tvCopyMode, R.id.ivCopyModeTip -> {
                tipsAdapter.setList(
                    arrayListOf(
                        HintLocalData(getString(R.string.equivalent_used_margin), getString(R.string.about_copy_mode_equivalent_used_margin)),
                        HintLocalData(getString(R.string.fixed_lots), getString(R.string.about_copy_mode_fixed_lots)),
                        HintLocalData(getString(R.string.fixed_multiples), getString(R.string.about_copy_mode_fixed_multiples))
                    )
                )
                BottomListDialog.Builder(this)
                    .setTitle(getString(R.string.copy_mode))
                    .setAdapter(tipsAdapter)
                    .build()
                    .showDialog()
            }

            R.id.tvAvailableInvestment -> {
                tipsAdapter.setList(
                    arrayListOf(
                        HintLocalData(getString(R.string.the_sum_of_level_requirement))
                    )
                )
                BottomListDialog.Builder(this)
                    .setTitle(getString(R.string.available_investment))
                    .setAdapter(tipsAdapter)
                    .build()
                    .showDialog()
            }

            R.id.tvUsedCredit -> {
                tipsAdapter.setList(
                    arrayListOf(
                        HintLocalData(getString(R.string.the_use_of_the_equity_credit))
                    )
                )
                BottomListDialog.Builder(this)
                    .setTitle(getString(R.string.used_credit))
                    .setAdapter(tipsAdapter)
                    .build()
                    .showDialog()
            }

            R.id.tvUsedBalance -> {
                tipsAdapter.setList(
                    arrayListOf(
                        HintLocalData(getString(R.string.based_on_the_the_the_if_the_the_strategy))
                    )
                )
                BottomListDialog.Builder(this)
                    .setTitle(getString(R.string.used_balance))
                    .setAdapter(tipsAdapter)
                    .build()
                    .showDialog()
            }

            R.id.tvGSUserBalanceTip -> {
                BottomGSTipDialog.Builder(this, BottomGSTipDialog.DialogFrom.BALANCE)
                    .setTitle(getString(R.string.growth_shield_fund_eligible_funds))
                    .build()
                    .showDialog()
            }

            R.id.tvGSUserRiskTip -> {
                BottomGSTipDialog.Builder(this, BottomGSTipDialog.DialogFrom.STOPLOSS)
                    .setTitle(getString(R.string.growth_shield_can_for_example))
                    .build()
                    .showDialog()
            }

            R.id.tvRiskManagement, R.id.ivRiskManagementTip -> {
                tipsAdapter.setList(
                    arrayListOf(
                        HintLocalData(getString(R.string.stop_loss), getString(R.string.stop_loss_tips)),
                        HintLocalData(getString(R.string.take_profit), getString(R.string.set_the_take_when_the_stop_copying))
                    )
                )
                BottomListDialog.Builder(this)
                    .setTitle(getString(R.string.risk_management))
                    .setAdapter(tipsAdapter)
                    .build()
                    .showDialog()
            }

            R.id.tvSlippageProtection, R.id.ivSlippageProtectionIcon -> {
                tipsAdapter.setList(arrayListOf(
                    HintLocalData(getString(R.string.with_the_slippage_copiers_reducing_minor_type_variations))
                ))
                BottomListDialog.Builder(this)
                    .setTitle(getString(R.string.slippage_protection))
                    .setAdapter(tipsAdapter)
                    .build()
                    .showDialog()
            }

            R.id.tvLotRoundUp, R.id.ivLotRoundUpIcon -> {
                tipsAdapter.setList(
                    arrayListOf(
                        HintLocalData(getString(R.string.lot_round_up_upon_you_and_selected_strategy))
                    )
                )
                BottomListDialog.Builder(this)
                    .setTitle(getString(R.string.lot_round_up))
                    .setAdapter(tipsAdapter)
                    .build()
                    .showDialog()
            }

            R.id.tvCopyOpenTrade, R.id.ivCopyOpenTradeIcon -> {
                tipsAdapter.setList(
                    arrayListOf(
                        HintLocalData(getString(R.string.copy_opened_trades_tips))
                    )
                )
                BottomListDialog.Builder(this)
                    .setTitle(getString(R.string.copy_opened_trades))
                    .setAdapter(tipsAdapter)
                    .build()
                    .showDialog()
            }

            R.id.ivRiskManagementArrow -> {
                isExpand = !isExpand
                mBinding.layoutStrategyRiskManagement.groupSLandTP.isVisible = !isExpand
                mBinding.layoutStrategyRiskManagement.tvGSUserRiskTip.isVisible =
                    mViewModel.profitShieldStrategy && mBinding.layoutStrategyRiskManagement.groupSLandTP.isVisible
                mBinding.layoutStrategyRiskManagement.clSLandTP.isVisible = isExpand
                mBinding.layoutStrategyRiskManagement.ivRiskManagementArrow.rotation = if (isExpand) 180f else 0f
            }
        }
    }

    /**
     * 神策自定义埋点(v3500)
     */
    private fun sensorsTrack(isClickBtnTrack: Boolean = false) {
        val bean = mViewModel.strategyCopyLoadLiveData.value?.data ?: return
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.STRATEGY_TITLE, bean.strategyName.ifNull()) // 策略标题
        properties.put(SensorsConstant.Key.PROVIDER_NAME, "") // 信号源名称
        properties.put(SensorsConstant.Key.PROVIDER_ID, "") // 信号源id
        properties.put(SensorsConstant.Key.RETURN_RATIO, bean.returnRate.ifNull()) // 收益率
        properties.put(SensorsConstant.Key.RETURN_TYPE, "3M") // 收益类型
        properties.put(SensorsConstant.Key.PROFIT_SHARE, bean.profitSharePercentage.ifNull()) // 分润比例
        properties.put(
            SensorsConstant.Key.SETTLEMENT, when (bean.settlementCycle) {
                1 -> "Daily"
                2 -> "Weekly"
                3 -> "Monthly"
                else -> ""
            }
        ) // 结算周期
        if (isClickBtnTrack) {
            properties.put(
                SensorsConstant.Key.COPY_MODE, when (copyModeSelectedIndex) {
                    1 -> "Fixed Lots"
                    2 -> "Fixed Multiples"
                    else -> "Equivalent Used Margin"
                }
            ) // 跟随模式
            properties.put(
                SensorsConstant.Key.INVESTMENT_AMOUNT,
                mBinding.layoutStrategyCopyMode.etInvestmentAmount.text.toString()
            ) // 投资金额
            properties.put(SensorsConstant.Key.INVESTMENT_CURRENCY, currencyType) // 投资币种
            properties.put(SensorsConstant.Key.STOP_LOSS, slValue.mathDiv("100", 2).ifNull()) // 止损百分比
            properties.put(
                SensorsConstant.Key.TAKE_PROFIT, if (tpEnabled) {
                    tpValue.mathDiv("100", 2).ifNull()
                } else {
                    ""
                }
            ) // 止盈百分比
            properties.put(
                SensorsConstant.Key.IS_LOT_ROUNDUP, if (lotRoundUpStatus) 1 else 0
            ) // 是否使用提升助手
            properties.put(
                SensorsConstant.Key.IS_COPY_OPENED, if (copyOpenedStatus) 1 else 0
            ) // 是否复制持仓订单
            properties.put(SensorsConstant.Key.BUTTON_NAME, "Submit") // 按钮名称
            // 跟单详情页点击 -> 跟单详情页按钮点击时触发
            SensorsDataUtil.track(SensorsConstant.V3500.COPY_DETAIL_PAGE_CLICK, properties)
        } else {
            // 跟单详情页浏览 -> 跟单详情页页面加载完成时触发
            SensorsDataUtil.track(SensorsConstant.V3500.COPY_DETAIL_PAGE_VIEW, properties)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        KeyboardUtil.unregisterSoftInputChangedListener(this.window)
    }

    companion object {
        fun open(context: Context?, strategyId: String? = null, copyMode: String? = null) {
            val intent = Intent(context, StStrategyCopyActivity::class.java)
            intent.putExtra(Constants.STRATEGY_ID, strategyId)
            intent.putExtra(Constants.STRATEGY_ORDER_COPY_MODE, copyMode)
            context?.startActivity(intent)
        }
    }
}