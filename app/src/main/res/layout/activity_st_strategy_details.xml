<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:ignore="ContentDescription,SmallSp,UseCompoundDrawables,HardcodedText">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_endIcon="@drawable/bitmap1_share"
        app:hb_endIcon1="?attr/icon1Faq" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="0px"
        android:layout_weight="1">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appbarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/transparent"
            android:orientation="vertical"
            app:elevation="0px">

            <!-- 需要被推走的布局 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginVertical="@dimen/margin_top_title"
                app:layout_scrollFlags="scroll">

                <com.google.android.material.imageview.ShapeableImageView
                    android:id="@+id/ivAvatar"
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:scaleType="centerCrop"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:shapeAppearanceOverlay="@style/roundImageStyle10"
                    tools:src="@mipmap/ic_launcher" />

                <TextView
                    android:id="@+id/tvNick"
                    style="@style/gilroy_600"
                    android:layout_width="0px"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="9dp"
                    android:layout_marginBottom="4dp"
                    android:ellipsize="end"
                    android:gravity="start"
                    android:maxLines="1"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="18dp"
                    app:layout_constraintBottom_toTopOf="@+id/llProvider"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/ivAvatar"
                    app:layout_constraintTop_toTopOf="@+id/ivAvatar"
                    app:layout_constraintVertical_chainStyle="packed"
                    tools:text="Signal Provider Name" />

                <LinearLayout
                    android:id="@+id/llProvider"
                    android:layout_width="0px"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    app:layout_constraintBottom_toTopOf="@+id/tvStrategyId"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/tvNick"
                    app:layout_constraintTop_toBottomOf="@+id/tvNick">

                    <TextView
                        android:id="@+id/tvProviderDesc"
                        style="@style/gilroy_400"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/provider"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="12dp" />

                    <TextView
                        android:id="@+id/tvProvider"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:drawablePadding="4dp"
                        android:ellipsize="end"
                        android:gravity="center_vertical|start"
                        android:maxLines="1"
                        android:textAlignment="viewStart"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="12dp"
                        app:drawableEndCompat="@drawable/draw_bitmap2_arrow_end7x7_c1e1e1e_cebffffff"
                        app:layout_constraintBottom_toBottomOf="@+id/tvProviderDesc"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/tvProviderDesc"
                        app:layout_constraintTop_toTopOf="@+id/tvProviderDesc"
                        tools:text="ameAlpineFX Name ameameAlpineFX Name ameameAlpineFX Name ame" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvStrategyId"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textDirection="ltr"
                    android:textSize="12dp"
                    app:layout_constraintBottom_toBottomOf="@+id/ivAvatar"
                    app:layout_constraintStart_toStartOf="@+id/tvNick"
                    app:layout_constraintTop_toBottomOf="@+id/llProvider"
                    tools:text="Strategy ID：1234567" />

                <cn.com.vau.common.view.expandabletextview.ExpandableTextView
                    android:id="@+id/tvIntro"
                    style="@style/gilroy_400"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.2"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    android:visibility="gone"
                    app:ep_end_color="?attr/color_c731e1e1e_c61ffffff"
                    app:ep_expand_text="@string/view_more"
                    app:ep_max_line="2"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/ivAvatar"
                    tools:text="@string/copiers"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/tvRoiKey"
                    style="@style/gilroy_400"
                    android:layout_width="0px"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:ellipsize="end"
                    android:gravity="start"
                    android:maxLines="1"
                    android:text="@string/return_3m"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    app:layout_constraintEnd_toStartOf="@+id/tvCopiersKey"
                    app:layout_constraintHorizontal_chainStyle="spread_inside"
                    app:layout_constraintHorizontal_weight="1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvIntro" />

                <TextView
                    android:id="@+id/tvRoi"
                    style="@style/gilroy_600"
                    android:layout_width="0px"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:ellipsize="end"
                    android:gravity="start"
                    android:maxLines="1"
                    android:textAlignment="viewStart"
                    android:textColor="@color/c00c79c"
                    android:textDirection="ltr"
                    android:textSize="18dp"
                    app:layout_constraintEnd_toEndOf="@+id/tvRoiKey"
                    app:layout_constraintStart_toStartOf="@+id/tvRoiKey"
                    app:layout_constraintTop_toBottomOf="@+id/tvRoiKey"
                    tools:text="120.07%" />

                <TextView
                    android:id="@+id/tvCopiersKey"
                    style="@style/gilroy_400"
                    android:layout_width="0px"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="1"
                    android:text="@string/copiers"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    app:layout_constraintEnd_toStartOf="@+id/flRisk"
                    app:layout_constraintHorizontal_weight="1"
                    app:layout_constraintStart_toEndOf="@+id/tvRoiKey"
                    app:layout_constraintTop_toTopOf="@+id/tvRoiKey" />

                <TextView
                    android:id="@+id/tvCopiers"
                    style="@style/gilroy_600"
                    android:layout_width="0px"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="1"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="18dp"
                    app:layout_constraintEnd_toEndOf="@+id/tvCopiersKey"
                    app:layout_constraintStart_toStartOf="@+id/tvCopiersKey"
                    app:layout_constraintTop_toTopOf="@+id/tvRoi"
                    tools:text="100" />

                <FrameLayout
                    android:id="@+id/flRisk"
                    android:layout_width="0px"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal"
                    app:layout_constraintEnd_toStartOf="@+id/flProfitSharing"
                    app:layout_constraintHorizontal_weight="1"
                    app:layout_constraintStart_toEndOf="@+id/tvCopiersKey"
                    app:layout_constraintTop_toTopOf="@+id/tvRoiKey">

                    <TextView
                        android:id="@+id/tvRiskKey"
                        style="@style/gilroy_400"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:drawablePadding="3dp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text="@string/risk"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="12dp"
                        app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                        tools:text="RiskRiskRiskRisk" />

                </FrameLayout>

                <TextView
                    android:id="@+id/tvRisk"
                    style="@style/gilroy_600"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/c00c79c"
                    android:textSize="18dp"
                    app:layout_constraintEnd_toEndOf="@+id/flRisk"
                    app:layout_constraintStart_toStartOf="@+id/flRisk"
                    app:layout_constraintTop_toTopOf="@+id/tvRoi"
                    tools:text="2" />

                <FrameLayout
                    android:id="@+id/flProfitSharing"
                    android:layout_width="0px"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_weight="1"
                    app:layout_constraintStart_toEndOf="@+id/flRisk"
                    app:layout_constraintTop_toTopOf="@+id/tvRoiKey">

                    <TextView
                        android:id="@+id/tvProfitSharingKey"
                        style="@style/gilroy_400"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end|center_vertical"
                        android:drawablePadding="3dp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text="@string/profit_sharing"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="12dp"
                        app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff" />
                </FrameLayout>

                <TextView
                    android:id="@+id/tvProfitSharing"
                    style="@style/gilroy_600"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="18dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tvRoi"
                    tools:text="20%" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- 需要被卡住的布局 -->
            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <cn.com.vau.common.view.tablayout.DslTabLayout
                    android:id="@+id/mTabLayout"
                    android:layout_width="match_parent"
                    android:layout_height="33dp" />

                <View
                    style="@style/TabLayoutBottomLineStyle"
                    android:layout_gravity="bottom" />
            </FrameLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <!-- 底部可滑动布局 -->
        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager2"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="10dp"
        android:paddingBottom="@dimen/padding_vertical_base"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivCollect"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:layout_marginStart="12dp"
            android:contentDescription="@string/app_name"
            android:paddingHorizontal="@dimen/padding_horizontal_base"
            android:paddingVertical="8dp"
            app:layout_constraintBottom_toTopOf="@+id/tvCollectCount"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            tools:src="@drawable/draw_bitmap2_favorite12x12_c1e1e1e_cebffffff" />

        <TextView
            android:id="@+id/tvCollectCount"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/margin_horizontal_base"
            android:paddingBottom="0dp"
            android:text="0"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/ivCollect"
            app:layout_constraintStart_toStartOf="@+id/ivCollect"
            app:layout_constraintTop_toBottomOf="@+id/ivCollect" />

        <TextView
            android:id="@+id/tvNext"
            style="@style/main_bottom_button_theme"
            android:layout_width="0dp"
            android:layout_marginHorizontal="@dimen/margin_horizontal_base"
            android:layout_marginVertical="0dp"
            android:text="@string/copy"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/ivCollect"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>