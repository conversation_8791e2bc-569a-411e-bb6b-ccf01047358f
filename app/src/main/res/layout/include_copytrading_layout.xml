<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Profit Shield -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clProfitShield"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvProfitShield"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_horizontal_base"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="Growth Shield"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="16dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@id/ivProfitShield"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivProfitShield"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_marginEnd="5dp"
            android:padding="5dp"
            android:src="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
            app:layout_constraintBottom_toBottomOf="@id/tvProfitShield"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tvProfitShield"
            app:layout_constraintTop_toTopOf="@id/tvProfitShield" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivArrowProfitShield"
            android:layout_width="26dp"
            android:layout_height="30dp"
            android:layout_marginEnd="7dp"
            android:padding="10dp"
            android:src="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
            app:layout_constraintBottom_toBottomOf="@id/tvProfitShield"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvProfitShield" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvProfitShieldTip"
            style="@style/gilroy_400"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/margin_horizontal_base"
            android:layout_marginTop="4dp"
            android:lineSpacingExtra="@dimen/line_spacing_extra"
            android:textAlignment="viewStart"
            android:gravity="start"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="12dp"
            app:layout_constraintTop_toBottomOf="@id/tvProfitShield"
            tools:text="Earn up to 100% p.a. of investment. Cover up to 20% loss from copying. TnC apply" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <cn.com.vau.common.view.NestedScrollableHost
        android:id="@+id/profitShieldHost"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/clProfitShield">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/profitShieldRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_top_card_title"
            android:layout_marginBottom="@dimen/margin_top_title_card"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvProfitShield"
            tools:itemCount="1"
            tools:listitem="@layout/item_strategy_return" />

    </cn.com.vau.common.view.NestedScrollableHost>


    <!-- Most Copied -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clMostCopied"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toStartOf="@id/ivArrowMostCopied"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/profitShieldHost">

        <TextView
            android:id="@+id/tvMostCopied"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_horizontal_base"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/most_copied"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="16dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@id/ivMostCopied"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="gone" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivMostCopied"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_marginEnd="5dp"
            android:padding="5dp"
            android:src="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
            app:layout_constraintBottom_toBottomOf="@id/tvMostCopied"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tvMostCopied"
            app:layout_constraintTop_toTopOf="@id/tvMostCopied"
            tools:visibility="gone" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivArrowMostCopied"
        android:layout_width="26dp"
        android:layout_height="30dp"
        android:layout_marginEnd="7dp"
        android:padding="10dp"
        android:src="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@id/clMostCopied"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/clMostCopied"
        tools:visibility="gone" />

    <cn.com.vau.common.view.NestedScrollableHost
        android:id="@+id/mostCopiedHost"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/clMostCopied">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/mostCopiedRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_top_card_title"
            android:layout_marginBottom="@dimen/margin_top_title_card"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvMostCopied"
            tools:itemCount="1"
            tools:listitem="@layout/item_strategy_return"
            tools:visibility="gone" />

    </cn.com.vau.common.view.NestedScrollableHost>

    <!-- Highest Annual Return -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clHighestReturn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toStartOf="@id/ivArrowHighestReturn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/mostCopiedHost">

        <TextView
            android:id="@+id/tvHighestReturn"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_horizontal_base"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/highest_annual_return"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="16dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@id/ivHighestReturn"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivHighestReturn"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_marginEnd="5dp"
            android:padding="5dp"
            android:src="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
            app:layout_constraintBottom_toBottomOf="@id/tvHighestReturn"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tvHighestReturn"
            app:layout_constraintTop_toTopOf="@id/tvHighestReturn" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivArrowHighestReturn"
        android:layout_width="26dp"
        android:layout_height="30dp"
        android:layout_marginEnd="7dp"
        android:padding="10dp"
        android:src="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@id/clHighestReturn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/clHighestReturn" />

    <cn.com.vau.common.view.NestedScrollableHost
        android:id="@+id/highestReturnHost"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/clHighestReturn">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/highestReturnRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_top_card_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvHighestReturn"
            tools:itemCount="1"
            tools:listitem="@layout/item_strategy_return" />

    </cn.com.vau.common.view.NestedScrollableHost>

    <!-- Low Risk and Stable Return -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clLowRiskReturn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_top_title_card"
        app:layout_constraintEnd_toStartOf="@id/ivArrowLowRiskReturn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/highestReturnHost">

        <TextView
            android:id="@+id/tvLowRiskReturn"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_horizontal_base"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/low_risk_and_stable_return"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="16dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@id/ivLowRiskReturn"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivLowRiskReturn"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_marginEnd="5dp"
            android:padding="5dp"
            android:src="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
            app:layout_constraintBottom_toBottomOf="@id/tvLowRiskReturn"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tvLowRiskReturn"
            app:layout_constraintTop_toTopOf="@id/tvLowRiskReturn" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivArrowLowRiskReturn"
        android:layout_width="26dp"
        android:layout_height="30dp"
        android:layout_marginEnd="7dp"
        android:padding="10dp"
        android:src="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@id/clLowRiskReturn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/clLowRiskReturn" />

    <cn.com.vau.common.view.NestedScrollableHost
        android:id="@+id/lowRiskReturnHost"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/clLowRiskReturn">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/lowRiskReturnRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_top_card_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvLowRiskReturn"
            tools:itemCount="1"
            tools:listitem="@layout/item_strategy_return" />

    </cn.com.vau.common.view.NestedScrollableHost>

    <!-- High Win Rate -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clHighWinRate"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_top_title_card"
        app:layout_constraintEnd_toStartOf="@id/ivArrowHighWinRate"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/lowRiskReturnHost">

        <TextView
            android:id="@+id/tvHighWinRate"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_horizontal_base"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/high_win_rate"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="16dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@id/ivHighWinRate"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivHighWinRate"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_marginEnd="5dp"
            android:padding="5dp"
            android:src="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
            app:layout_constraintBottom_toBottomOf="@id/tvHighWinRate"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tvHighWinRate"
            app:layout_constraintTop_toTopOf="@id/tvHighWinRate" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivArrowHighWinRate"
        android:layout_width="26dp"
        android:layout_height="30dp"
        android:layout_marginEnd="7dp"
        android:padding="10dp"
        android:src="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@id/clHighWinRate"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/clHighWinRate" />

    <cn.com.vau.common.view.NestedScrollableHost
        android:id="@+id/highWinRateHost"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/clHighWinRate">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/highWinRateRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_top_card_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvHighWinRate"
            tools:itemCount="1"
            tools:listitem="@layout/item_strategy_return" />

    </cn.com.vau.common.view.NestedScrollableHost>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupHighWinRate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="clHighWinRate, ivArrowHighWinRate, highWinRateHost"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>