<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ctlAccountInfo"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:background="@drawable/draw_main_card"
    tools:layout_margin="12dp"
    tools:padding="12dp"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clEquity"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:padding="4dp"
        app:layout_constraintEnd_toStartOf="@id/clFloatingPnL"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="@drawable/draw_shape_c0a1e1e1e_c1fffffff_r4">

        <cn.com.vau.util.widget.DashLineTextView
            android:id="@+id/tvEquityTitle"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:background="@color/chart_green"
            android:paddingBottom="4dp"
            android:gravity="start"
            android:maxLines="1"
            android:text="@string/equity"
            android:textAlignment="viewStart"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="12dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="SpUsage" />

        <TextView
            android:id="@+id/tvEquity"
            style="@style/gilroy_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:gravity="start"
            android:maxLines="1"
            android:text="..."
            android:textAlignment="viewStart"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="ltr"
            android:textSize="12dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvEquityTitle"
            tools:ignore="SpUsage,HardcodedText"
            tools:text="1210.25" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clFloatingPnL"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="4dp"
        android:padding="4dp"
        app:layout_constraintEnd_toStartOf="@id/clMarginLevel"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/clEquity"
        app:layout_constraintTop_toTopOf="parent">

        <cn.com.vau.util.widget.DashLineTextView
            android:id="@+id/tvFloatingPnLTitle"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="start"
            android:maxLines="1"
            android:text="@string/floating_pnl"
            android:textAlignment="viewStart"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="12dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="SpUsage" />

        <TextView
            android:id="@+id/tvFloatingPnL"
            style="@style/gilroy_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:gravity="start"
            android:maxLines="1"
            android:text="..."
            android:textAlignment="viewStart"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="ltr"
            android:textSize="12dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvFloatingPnLTitle"
            app:layout_goneMarginStart="0dp"
            tools:ignore="SpUsage,HardcodedText"
            tools:text="-13.42" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clMarginLevel"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:padding="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/clFloatingPnL"
        app:layout_constraintTop_toTopOf="parent">

        <cn.com.vau.util.widget.DashLineTextView
            android:id="@+id/tvMarginLevelTitle"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:text="@string/margin_level"
            android:textAlignment="viewEnd"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="12dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="SpUsage" />

        <ImageView
            android:id="@+id/ivRisk"
            android:layout_width="14dp"
            android:layout_height="12dp"
            android:contentDescription="@string/app_name"
            android:src="@drawable/img_low_risk"
            app:layout_constraintBottom_toBottomOf="@+id/tvMarginLevel"
            app:layout_constraintEnd_toStartOf="@+id/tvMarginLevel"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvMarginLevel" />

        <TextView
            android:id="@+id/tvMarginLevel"
            style="@style/gilroy_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:text="..."
            android:textAlignment="viewEnd"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="ltr"
            android:textSize="12dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/ivRisk"
            app:layout_constraintTop_toBottomOf="@+id/tvMarginLevelTitle"
            tools:ignore="SpUsage,HardcodedText"
            tools:text="59,1555.211111111111111113%" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrierLine1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="clEquity,clFloatingPnL,clMarginLevel" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clCredit"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:padding="4dp"
        app:layout_constraintEnd_toStartOf="@id/clMarginAndFreeMargin"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/barrierLine1"
        tools:background="@drawable/draw_shape_c0a1e1e1e_c1fffffff_r4">

        <cn.com.vau.util.widget.DashLineTextView
            android:id="@+id/tvCreditTitle"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="start"
            android:maxLines="1"
            android:text="@string/credit"
            android:textAlignment="viewStart"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="12dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="SpUsage" />

        <TextView
            android:id="@+id/tvCredit"
            style="@style/gilroy_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:gravity="start"
            android:maxLines="1"
            android:text="..."
            android:textAlignment="viewStart"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="ltr"
            android:textSize="12dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvCreditTitle"
            tools:ignore="SpUsage,HardcodedText"
            tools:text="1000" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clMarginAndFreeMargin"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="4dp"
        android:layout_marginTop="8dp"
        android:padding="4dp"
        app:layout_constraintEnd_toStartOf="@id/clBalance"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/clCredit"
        app:layout_constraintTop_toBottomOf="@+id/barrierLine1">

        <cn.com.vau.util.widget.DashLineTextView
            android:id="@+id/tvMarginAndFreeMarginTitle"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="start"
            android:maxLines="1"
            android:textAlignment="viewStart"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="12dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="SpUsage"
            tools:text="@string/margin" />

        <TextView
            android:id="@+id/tvMarginAndFreeMargin"
            style="@style/gilroy_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:gravity="start"
            android:singleLine="true"
            android:text="..."
            android:textAlignment="viewStart"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="ltr"
            android:textSize="12dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvMarginAndFreeMarginTitle"
            tools:ignore="SpUsage,HardcodedText"
            tools:text="1000" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clBalance"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:padding="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/clMarginAndFreeMargin"
        app:layout_constraintTop_toBottomOf="@+id/barrierLine1">

        <cn.com.vau.util.widget.DashLineTextView
            android:id="@+id/tvBalanceTitle"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:text="@string/balance"
            android:textAlignment="viewEnd"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="12dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@+id/ivReset"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="SpUsage" />

        <ImageView
            android:id="@+id/ivReset"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:contentDescription="@string/app_name"
            android:padding="4dp"
            android:src="@drawable/icon2_balance_reset"
            app:layout_constraintBottom_toBottomOf="@+id/tvBalanceTitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tvBalanceTitle"
            app:layout_constraintTop_toTopOf="@+id/tvBalanceTitle" />

        <TextView
            android:id="@+id/tvBalance"
            style="@style/gilroy_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:text="..."
            android:textAlignment="viewEnd"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="ltr"
            android:textSize="12dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvBalanceTitle"
            tools:ignore="SpUsage,HardcodedText"
            tools:text="1000" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</merge>
