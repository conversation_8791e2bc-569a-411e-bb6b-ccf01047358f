<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nsvContent"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tvProdName"
                style="@style/gilroy_700"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textDirection="ltr"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="SpUsage"
                tools:text="VAU-TEST" />

            <TextView
                android:id="@+id/tvOrderId"
                style="@style/gilroy_400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/margin_horizontal_base"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintBottom_toBottomOf="@+id/tvProdName"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tvProdName"
                tools:ignore="SpUsage"
                tools:text="#4535356565" />

            <TextView
                android:id="@+id/tvOrderDirection"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="18dp"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:layout_marginTop="4dp"
                android:background="@drawable/shape_c1f00c79c_r4"
                android:gravity="center_vertical"
                android:paddingHorizontal="4dp"
                android:textColor="@color/c00c79c"
                android:textSize="12dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvProdName"
                tools:ignore="Smalldp,SpUsage"
                tools:text="Buy" />

            <TextView
                android:id="@+id/tvOrderType"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="18dp"
                android:layout_marginStart="4dp"
                android:layout_marginTop="4dp"
                android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r4"
                android:gravity="center_vertical"
                android:paddingHorizontal="4dp"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tvOrderDirection"
                app:layout_constraintStart_toEndOf="@+id/tvOrderDirection"
                tools:ignore="Smalldp,SpUsage"
                tools:text="Limit" />

            <TextView
                android:id="@+id/tvCurrentPriceTitle"
                style="@style/gilroy_400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_marginTop="16dp"
                android:text="@string/current_price"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textDirection="ltr"
                android:textSize="12dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvOrderDirection"
                tools:ignore="SpUsage" />

            <TextView
                android:id="@+id/tvCurrentPrice"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp"
                app:layout_constraintBottom_toBottomOf="@+id/tvCurrentPriceTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tvCurrentPriceTitle"
                tools:ignore="SpUsage"
                tools:text="1.3456" />

            <TextView
                android:id="@+id/tvVolTitle"
                style="@style/gilroy_400"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:layout_marginTop="6dp"
                android:layout_marginEnd="4dp"
                android:ellipsize="end"
                android:gravity="start"
                android:maxLines="2"
                android:textAlignment="viewStart"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvCurrentPriceTitle"
                tools:ignore="SpUsage"
                tools:text="@string/volume" />

            <TextView
                android:id="@+id/tvVolume"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp"
                app:layout_constraintBottom_toBottomOf="@+id/tvVolTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tvVolTitle"
                tools:ignore="SpUsage"
                tools:text="0.20" />

            <TextView
                android:id="@+id/tvMarginTitle"
                style="@style/gilroy_400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:layout_marginTop="8dp"
                android:drawablePadding="4dp"
                android:text="@string/margin"
                android:textDirection="ltr"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvVolTitle" />

            <TextView
                android:id="@+id/tvMargin"
                style="@style/gilroy_400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp"
                app:layout_constraintBottom_toBottomOf="@+id/tvMarginTitle"
                app:layout_constraintEnd_toStartOf="@+id/tvFreeMargin"
                app:layout_constraintTop_toTopOf="@+id/tvMarginTitle"
                tools:text="2.00 USD/" />

            <TextView
                android:id="@+id/tvFreeMargin"
                style="@style/gilroy_400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp"
                app:layout_constraintBottom_toBottomOf="@+id/tvMarginTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tvMarginTitle"
                tools:text="500.00 USD" />

            <cn.com.vau.trade.view.PriceInputView
                android:id="@+id/viewAtPrice"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="12dp"
                android:layout_marginTop="20dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvMarginTitle" />

            <cn.com.vau.trade.view.PriceInputView
                android:id="@+id/viewLimitPrice"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/viewAtPrice" />

            <View
                android:id="@+id/viewLine"
                android:layout_width="0dp"
                android:layout_height="0.5dp"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/viewLimitPrice" />

            <cn.com.vau.trade.view.TakeProfitStopLossItemView
                android:id="@+id/viewTakeProfit"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/viewLine"
                tools:ignore="SpUsage"
                tools:style="@style/gilroy_600"
                tools:text="@string/take_profit" />

            <cn.com.vau.trade.view.TakeProfitStopLossItemView
                android:id="@+id/viewStopLoss"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/viewTakeProfit"
                tools:ignore="SpUsage"
                tools:style="@style/gilroy_600"
                tools:text="@string/take_profit" />

            <View
                android:id="@+id/vSoftInputHeight"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/viewStopLoss" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/tvNext"
        style="@style/main_bottom_button_theme"
        android:layout_width="match_parent"
        android:layout_height="@dimen/height_button_main"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginBottom="0dp"
        android:text="@string/confirm"
        tools:ignore="SpUsage"
        tools:style="@style/gilroy_600" />
</LinearLayout>