<?xml version="1.0" encoding="utf-8"?><!--
    【跟单/非跟单】
    订单持仓 /
    K线底部持仓 /
     -->
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:id="@+id/ctlParent"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tvOrderType"
        android:layout_width="15dp"
        android:layout_height="15dp"
        style="@style/gilroy_500"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:background="@drawable/shape_c00c79c_r2_8"
        android:gravity="center_horizontal"
        android:textColor="@color/white"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvProdName"
        app:layout_constraintBottom_toBottomOf="@+id/tvProdName"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintEnd_toStartOf="@id/tvProdName"
        app:layout_constraintHorizontal_bias="0"
        tool:ignore="Smalldp,SpUsage"
        tool:text="B" />

    <TextView
        android:id="@+id/tvProdName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/gilroy_700"
        android:layout_marginStart="4dp"
        android:layout_marginTop="16dp"
        android:ellipsize="end"
        android:singleLine="true"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="16dp"
        app:layout_constraintStart_toEndOf="@id/tvOrderType"
        app:layout_constraintEnd_toStartOf="@id/ivKLine"
        app:layout_constrainedWidth="true"
        app:layout_constraintTop_toTopOf="parent"
        tool:ignore="SpUsage"
        tool:text="VAU-TEST1" />

    <ImageView
        android:id="@+id/ivKLine"
        android:layout_width="22dp"
        android:layout_height="20dp"
        android:contentDescription="@string/app_name"
        android:paddingStart="4dp"
        android:paddingEnd="2dp"
        android:paddingVertical="2dp"
        android:src="@drawable/draw_bitmap2_view_chart_ca61e1e1e_c99ffffff"
        app:layout_constraintBottom_toBottomOf="@+id/tvProdName"
        app:layout_constraintStart_toEndOf="@+id/tvProdName"
        app:layout_constraintEnd_toStartOf="@id/ivShare"
        app:layout_constraintTop_toTopOf="@+id/tvProdName" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivShare"
        android:layout_width="32dp"
        android:layout_height="24dp"
        android:paddingVertical="4dp"
        android:paddingStart="4dp"
        android:paddingEnd="12dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tvProdName"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvProdName"
        app:srcCompat="?attr/icon2SharePosition"
        tool:visibility="visible" />

    <TextView
        android:id="@+id/tvPnlTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        style="@style/gilroy_400"
        android:layout_marginTop="16dp"
        android:maxLines="2"
        android:ellipsize="end"
        android:textAlignment="viewEnd"
        android:gravity="center_vertical|end"
        android:text="@string/pnl"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textDirection="ltr"
        android:textSize="12dp"
        android:layout_marginEnd="12dp"
        app:layout_constraintStart_toStartOf="@id/guideline_v66"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvProdName"
        tool:ignore="SpUsage"
        tool:text="PnL (USD)PnL (USD)PnL (USD)" />

    <!-- 浮动盈亏 -->
    <TextView
        android:id="@+id/tvPnl"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        style="@style/gilroy_600"
        android:textColor="@color/cf44040"
        android:textDirection="ltr"
        android:gravity="end"
        android:textAlignment="viewEnd"
        android:textSize="18dp"
        android:maxLines="2"
        android:ellipsize="end"
        app:layout_constraintStart_toStartOf="@id/tvPnlTitle"
        app:layout_constraintEnd_toEndOf="@id/tvPnlTitle"
        app:layout_constraintTop_toBottomOf="@id/volumeBarrier"
        tool:ignore="SpUsage"
        tool:text="-0.12345678" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/volumeBarrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="tvVolTitle,tvOpenPriceTitle,tvPnlTitle"/>

    <TextView
        android:id="@+id/tvVolTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        style="@style/gilroy_400"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="16dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:gravity="center_vertical|start"
        android:text="@string/volume"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toStartOf="@+id/guideline_v33"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvProdName"
        tool:ignore="SpUsage"
        tool:text="volume" />

    <TextView
        android:id="@+id/tvVolume"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        style="@style/gilroy_500"
        android:layout_marginTop="4dp"
        android:textAlignment="viewStart"
        android:gravity="start"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="14dp"
        android:maxLines="2"
        android:ellipsize="end"
        app:layout_constraintStart_toStartOf="@id/tvVolTitle"
        app:layout_constraintEnd_toEndOf="@id/tvVolTitle"
        app:layout_constraintTop_toBottomOf="@+id/volumeBarrier"
        tool:ignore="SpUsage"
        tool:text="0.20" />

    <TextView
        android:id="@+id/tvOpenPriceTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        style="@style/gilroy_400"
        android:layout_marginTop="16dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:gravity="center_vertical|start"
        android:text="@string/entry_price"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="@id/guideline_v66"
        app:layout_constraintStart_toStartOf="@id/guideline_v33"
        app:layout_constraintTop_toBottomOf="@+id/tvProdName"
        tool:ignore="SpUsage"
        tool:text="open_price" />

    <!-- 开单价 -->
    <TextView
        android:id="@+id/tvOpenPrice"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        style="@style/gilroy_500"
        android:layout_marginTop="4dp"
        android:maxLines="2"
        android:ellipsize="end"
        android:gravity="center_vertical|start"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="@+id/tvOpenPriceTitle"
        app:layout_constraintStart_toStartOf="@+id/tvOpenPriceTitle"
        app:layout_constraintTop_toBottomOf="@id/volumeBarrier"
        tool:ignore="SpUsage"
        tool:text="1.2345" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/openPriceBarrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="tvOpenPrice,tvVolume,tvPnl"/>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/ptSlGP"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="tvTpSlTitle,ivPtSlStatue"
        android:visibility="gone"
        tool:visibility="visible"/>

    <TextView
        android:id="@+id/tvTpSlTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/tp_sl"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="@id/tvVolTitle"
        app:layout_constraintTop_toBottomOf="@id/openPriceBarrier"
        style="@style/gilroy_400" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivPtSlStatue"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:paddingStart="8dp"
        app:srcCompat="@drawable/draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff"
        app:layout_constraintStart_toEndOf="@id/tvTpSlTitle"
        app:layout_constraintTop_toTopOf="@id/tvTpSlTitle"
        app:layout_constraintBottom_toBottomOf="@id/tvTpSlTitle"
        tool:ignore="RtlSymmetry" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clSetTpSl"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="12dp"
        android:paddingBottom="12dp"
        android:visibility="gone"
        android:background="@drawable/draw_shape_stroke_c1f1e1e1e_c1fffffff_r4"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tvTpSlTitle"
        app:layout_constraintTop_toBottomOf="@id/tvTpSlTitle"
        tool:visibility="gone">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivTpSlDelete"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:padding="6dp"
            android:src="?attr/icon2Delete"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="6dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toStartOf="@id/ivTpSlEdit"/>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivTpSlEdit"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:padding="6dp"
            android:src="?attr/icon2EditTpSl"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="8dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <TextView
            android:id="@+id/tvTpSlVolumeTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/gilroy_400"
            android:layout_marginTop="12dp"
            android:layout_marginStart="12dp"
            android:text="@string/volume"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="12dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ivTpSlDelete" />

        <TextView
            android:id="@+id/tvTpSlVolume"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="12dp"
            android:text="@string/all"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="12dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBaseline_toBaselineOf="@id/tvTpSlVolumeTitle"
            style="@style/gilroy_500" />

        <TextView
            android:id="@+id/tvTpTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/gilroy_400"
            android:layout_marginTop="12dp"
            android:text="@string/majuscule_tp"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="12dp"
            app:layout_constraintStart_toStartOf="@id/tvTpSlVolumeTitle"
            app:layout_constraintTop_toBottomOf="@id/tvTpSlVolumeTitle" />

        <TextView
            android:id="@+id/tvTp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="12dp"
            app:layout_constraintEnd_toEndOf="@id/tvTpSlVolume"
            app:layout_constraintBaseline_toBaselineOf="@id/tvTpTitle"
            style="@style/gilroy_500"
            tool:text="≤100,000.00 "/>

        <TextView
            android:id="@+id/tvSlTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/gilroy_400"
            android:layout_marginTop="12dp"
            android:text="@string/majuscule_sl"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="12dp"
            app:layout_constraintStart_toStartOf="@id/tvTpTitle"
            app:layout_constraintTop_toBottomOf="@id/tvTpTitle" />

        <TextView
            android:id="@+id/tvSl"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="12dp"
            app:layout_constraintEnd_toEndOf="@id/tvTpSlVolume"
            app:layout_constraintBaseline_toBaselineOf="@id/tvSlTitle"
            style="@style/gilroy_500"
            tool:text="≤100,000.00 "/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivReverse"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="4dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:gravity="center"
        android:maxLines="2"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:minHeight="32dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        android:paddingHorizontal="8dp"
        app:srcCompat="?attr/icon2ReservePosition"
        app:layout_constraintBottom_toTopOf="@+id/offView"
        app:layout_constraintEnd_toStartOf="@+id/tvTpSl"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/clSetTpSl"
        style="@style/gilroy_500" />

    <TextView
        android:id="@+id/tvTpSl"
        android:layout_width="0dp"
        android:layout_height="32dp"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="4dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:gravity="center"
        android:paddingHorizontal="4dp"
        android:maxLines="2"
        android:ellipsize="end"
        android:minHeight="32dp"
        android:text="@string/tp_sl"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toStartOf="@+id/tvCloseBy"
        app:layout_constraintStart_toEndOf="@id/ivReverse"
        app:layout_constraintTop_toTopOf="@+id/ivReverse"
        style="@style/gilroy_500" />

    <TextView
        android:id="@+id/tvCloseBy"
        android:layout_width="0dp"
        android:layout_height="32dp"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="4dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:maxLines="2"
        android:ellipsize="end"
        android:minHeight="32dp"
        android:text="@string/close_by"
        android:gravity="center"
        android:paddingHorizontal="4dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toStartOf="@+id/tvClose"
        app:layout_constraintStart_toEndOf="@id/tvTpSl"
        app:layout_constraintTop_toTopOf="@+id/ivReverse"
        style="@style/gilroy_500" />

    <TextView
        android:id="@+id/tvClose"
        android:layout_width="0dp"
        android:layout_height="32dp"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="4dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:gravity="center"
        android:paddingHorizontal="4dp"
        android:maxLines="2"
        android:ellipsize="end"
        android:minHeight="32dp"
        android:text="@string/partially_close"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintTop_toTopOf="@+id/ivReverse"
        app:layout_constraintEnd_toStartOf="@id/tvQuickClose"
        app:layout_constraintStart_toEndOf="@+id/tvCloseBy"
        style="@style/gilroy_500" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvQuickClose"
        android:layout_width="0dp"
        android:layout_height="32dp"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:background="@drawable/shape_c1fe35728_r100"
        android:gravity="center"
        android:textAlignment="center"
        android:paddingEnd="4dp"
        android:paddingStart="16dp"
        android:maxLines="2"
        android:ellipsize="end"
        android:minHeight="32dp"
        android:text="@string/close__position"
        android:textColor="@color/ce35728"
        android:textSize="12dp"
        app:drawableStartCompat="@drawable/icon2_quick_close"
        app:layout_constraintTop_toTopOf="@+id/ivReverse"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvClose"
        style="@style/gilroy_500"
        tool:ignore="RtlSymmetry" />

    <View
        android:id="@+id/offView"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        app:layout_constraintBottom_toBottomOf="parent"
        tool:background="?attr/color_c1f1e1e1e_c1fffffff" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_v33"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_begin="136dp" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_v50"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_v66"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.66" />

</androidx.constraintlayout.widget.ConstraintLayout>
