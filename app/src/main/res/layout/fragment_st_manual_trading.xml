<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.trade.view.TradeProductTitleView
        android:id="@+id/mProductTitleView"
        android:layout_width="0dp"
        android:layout_height="32dp"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/mSmartRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mProductTitleView"
        app:srlEnableLoadMore="false">

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <com.google.android.material.appbar.FixedFlingAndDragAppBarLayout
                android:id="@+id/mAppBarLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/transparent"
                app:elevation="0dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clAppBarContent"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    app:layout_scrollFlags="scroll">

                    <cn.com.vau.trade.view.TradeAccountInfoView
                        android:id="@+id/mAccountInfoView"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                        android:background="@drawable/draw_main_card"
                        android:padding="12dp"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:visibility="visible" />

                    <cn.com.vau.trade.view.OpenOrderView
                        android:id="@+id/openOrderView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintTop_toBottomOf="@id/mAccountInfoView" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:tabMode="fixed">

                    <cn.com.vau.common.view.tablayout.DslTabLayout
                        android:id="@+id/mTabLayout"
                        android:layout_width="match_parent"
                        android:layout_height="33dp"
                        android:layout_marginTop="8dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivEnterHistory"
                        android:layout_width="wrap_content"
                        android:layout_height="0dp"
                        android:paddingHorizontal="14dp"
                        android:paddingTop="10dp"
                        android:src="?attr/icon2HistoryEnter"
                        app:layout_constraintBottom_toBottomOf="@id/mTabLayout"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:background="?attr/color_c1f1e1e1e_c1fffffff"
                        app:layout_constraintTop_toBottomOf="@id/mTabLayout" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.appbar.FixedFlingAndDragAppBarLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                app:layout_behavior="@string/appbar_scrolling_view_behavior">

                <cn.com.vau.common.view.MultiNestedScrollableHost
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.viewpager2.widget.ViewPager2
                        android:id="@+id/mViewPager2"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />

                </cn.com.vau.common.view.MultiNestedScrollableHost>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!--        <include-->
            <!--            android:id="@+id/layoutMarketMaintenance"-->
            <!--            layout="@layout/layout_market_maintenance"-->
            <!--            android:layout_width="match_parent"-->
            <!--            android:layout_height="wrap_content"-->
            <!--            android:visibility="gone"-->
            <!--            app:layout_behavior="@string/appbar_scrolling_view_behavior" />-->

            <ViewStub
                android:id="@+id/mViewStubMarketMaintenance"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout="@layout/layout_market_maintenance_new"
                app:layout_behavior="@string/appbar_scrolling_view_behavior" />

        </androidx.coordinatorlayout.widget.CoordinatorLayout>

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <cn.com.vau.trade.view.TimeSharingChartView
        android:id="@+id/mTimeChartView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="?attr/mainLayoutBg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
