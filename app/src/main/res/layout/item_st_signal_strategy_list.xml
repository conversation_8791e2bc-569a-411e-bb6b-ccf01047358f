<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="@dimen/padding_card_base"
    tools:ignore="ContentDescription,HardcodedText">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivAvatar"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearanceOverlay="@style/roundImageStyle10"
        tools:src="@mipmap/ic_launcher" />

    <TextView
        android:id="@+id/tvNick"
        style="@style/gilroy_600"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginStart="9dp"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="8dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintEnd_toStartOf="@+id/tvAction"
        app:layout_constraintStart_toEndOf="@+id/ivAvatar"
        app:layout_constraintTop_toTopOf="@+id/ivAvatar"
        tools:text="Signal Provider Name" />

    <TextView
        android:id="@+id/tvStrategyId"
        style="@style/gilroy_400"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginBottom="4dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textDirection="ltr"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivAvatar"
        app:layout_constraintStart_toStartOf="@+id/tvNick"
        tools:text="Strategy ID：1234567" />

    <TextView
        android:id="@+id/tvAction"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
        android:gravity="center"
        android:minWidth="80dp"
        android:paddingHorizontal="3dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivAvatar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/ivAvatar"
        tools:text="View" />

    <cn.com.vau.common.view.expandabletextview.ExpandableTextView
        android:id="@+id/tvIntro"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:lineSpacingMultiplier="1.2"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:ep_end_color="?attr/color_c731e1e1e_c61ffffff"
        app:ep_expand_text="@string/view_more"
        app:ep_max_line="2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivAvatar"
        tools:text="@string/copiers" />

    <TextView
        android:id="@+id/tvRoiKey"
        style="@style/gilroy_400"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="1"
        android:text="@string/return_3m"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toStartOf="@+id/tvCopiersKey"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvIntro" />

    <TextView
        android:id="@+id/tvRoi"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:gravity="start"
        android:textAlignment="viewStart"
        android:textColor="@color/c00c79c"
        android:textDirection="ltr"
        android:textSize="18dp"
        app:layout_constraintEnd_toEndOf="@+id/tvRoiKey"
        app:layout_constraintStart_toStartOf="@+id/tvRoiKey"
        app:layout_constraintTop_toBottomOf="@+id/tvRoiKey"
        tools:text="-120.07%" />

    <TextView
        android:id="@+id/tvCopiersKey"
        style="@style/gilroy_400"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:text="@string/copiers"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toStartOf="@+id/flRisk"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/tvRoiKey"
        app:layout_constraintTop_toTopOf="@+id/tvRoiKey" />

    <TextView
        android:id="@+id/tvCopiers"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="18dp"
        app:layout_constraintEnd_toEndOf="@+id/tvCopiersKey"
        app:layout_constraintStart_toStartOf="@+id/tvCopiersKey"
        app:layout_constraintTop_toTopOf="@+id/tvRoi"
        tools:text="100" />

    <FrameLayout
        android:id="@+id/flRisk"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toStartOf="@+id/flProfitSharing"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/tvCopiersKey"
        app:layout_constraintTop_toTopOf="@+id/tvRoiKey">

        <TextView
            android:id="@+id/tvRiskKey"
            style="@style/gilroy_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:drawablePadding="3dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/risk"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="12dp"
            app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff" />
    </FrameLayout>

    <TextView
        android:id="@+id/tvRisk"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/c00c79c"
        android:textSize="18dp"
        app:layout_constraintEnd_toEndOf="@+id/flRisk"
        app:layout_constraintStart_toStartOf="@+id/flRisk"
        app:layout_constraintTop_toTopOf="@+id/tvRoi"
        tools:text="2" />

    <FrameLayout
        android:id="@+id/flProfitSharing"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/flRisk"
        app:layout_constraintTop_toTopOf="@+id/tvRoiKey">

        <TextView
            android:id="@+id/tvProfitSharingKey"
            style="@style/gilroy_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:drawablePadding="3dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@string/profit_sharing"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="12dp"
            app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff" />
    </FrameLayout>

    <TextView
        android:id="@+id/tvProfitSharing"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="18dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvRoi"
        tools:text="20%" />
</androidx.constraintlayout.widget.ConstraintLayout>