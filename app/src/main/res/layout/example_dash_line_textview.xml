<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="示例对比："
        android:textSize="16sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp" />

    <!-- 普通 TextView 作为对比 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Normal TextView with descenders: gypsy"
        android:textSize="18sp"
        android:layout_marginBottom="8dp"
        android:background="#FFEEEEEE" />

    <!-- 不自动扩展高度的 DashLineTextView -->
    <cn.com.vau.util.widget.DashLineTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Without auto expand: gypsy"
        android:textSize="18sp"
        android:layout_marginBottom="8dp"
        android:background="#FFEEEEEE"
        app:dlDashEnabled="true"
        app:dlDashColor="#FF0000"
        app:dlDashStrokeWidth="2dp"
        app:dlDashLength="6dp"
        app:dlDashGap="4dp"
        app:dlDashOffset="4dp"
        app:dlDashAlignment="descent"
        app:dlAutoExpandHeight="false" />

    <!-- 自动扩展高度的 DashLineTextView -->
    <cn.com.vau.util.widget.DashLineTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="With auto expand: gypsy"
        android:textSize="18sp"
        android:layout_marginBottom="8dp"
        android:background="#FFEEEEEE"
        app:dlDashEnabled="true"
        app:dlDashColor="#FF0000"
        app:dlDashStrokeWidth="2dp"
        app:dlDashLength="6dp"
        app:dlDashGap="4dp"
        app:dlDashOffset="4dp"
        app:dlDashAlignment="descent"
        app:dlAutoExpandHeight="true" />

    <!-- 更大偏移量的示例 -->
    <cn.com.vau.util.widget.DashLineTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Large offset: gypsy jumping"
        android:textSize="18sp"
        android:layout_marginBottom="8dp"
        android:background="#FFEEEEEE"
        app:dlDashEnabled="true"
        app:dlDashColor="#FF0000"
        app:dlDashStrokeWidth="2dp"
        app:dlDashLength="6dp"
        app:dlDashGap="4dp"
        app:dlDashOffset="8dp"
        app:dlDashAlignment="descent"
        app:dlAutoExpandHeight="true" />

    <!-- BASELINE 对齐模式 -->
    <cn.com.vau.util.widget.DashLineTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Baseline alignment: gypsy"
        android:textSize="18sp"
        android:layout_marginBottom="8dp"
        android:background="#FFEEEEEE"
        app:dlDashEnabled="true"
        app:dlDashColor="#0000FF"
        app:dlDashStrokeWidth="2dp"
        app:dlDashLength="6dp"
        app:dlDashGap="4dp"
        app:dlDashOffset="4dp"
        app:dlDashAlignment="baseline"
        app:dlAutoExpandHeight="true" />

</LinearLayout>
