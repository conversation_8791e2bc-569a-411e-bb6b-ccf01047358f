<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clTitle"
        android:layout_width="match_parent"
        android:layout_height="@dimen/height_title_bar"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:id="@+id/ivLeft"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:paddingHorizontal="@dimen/padding_horizontal_base"
            android:src="@drawable/draw_bitmap2_arrow_start16x16_c1e1e1e_cebffffff"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvProductName"
            style="@style/gilroy_700"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="ltr"
            android:textSize="20dp"
            android:drawableEnd="@drawable/draw_bitmap2_triangle_down_tab_c1e1e1e_cebffffff"
            android:drawablePadding="4dp"
            app:layout_constraintStart_toEndOf="@id/ivLeft"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintBottom_toTopOf="@id/tvSellPrice"
            tools:text="TSLA" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvSellPrice"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="12dp"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="@+id/tvProductName"
            app:layout_constraintTop_toBottomOf="@+id/tvProductName"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="38960.85 +1.29%" />

        <ImageView
            android:id="@+id/ivAlert"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:paddingHorizontal="12dp"
            android:src="?attr/icon1AlertPrice"
            app:layout_constraintEnd_toStartOf="@id/ivOptional"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <ImageView
            android:id="@+id/ivOptional"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:paddingHorizontal="8dp"
            android:src="@drawable/draw_bitmap1_favorite_c1e1e1e_cebffffff"
            app:layout_constraintEnd_toStartOf="@id/ivShare"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/ivShare"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:paddingHorizontal="8dp"
            android:layout_marginEnd="4dp"
            android:src="@drawable/bitmap1_share2"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <cn.com.vau.common.view.tablayout.DslTabLayout
        android:id="@+id/mTabLayout"
        android:layout_width="match_parent"
        android:layout_height="33dp"
        app:layout_constraintTop_toBottomOf="@id/clTitle"/>

    <View
        android:id="@+id/overView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintTop_toTopOf="@id/mTabLayout"
        app:layout_constraintStart_toStartOf="@id/mTabLayout"
        app:layout_constraintEnd_toEndOf="@id/mTabLayout"
        app:layout_constraintBottom_toBottomOf="@id/mTabLayout"/>

    <View
        android:id="@+id/viewLine"
        style="@style/TabLayoutBottomLineStyle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/mTabLayout" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/mViewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:overScrollMode="never"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/viewLine" />

    <FrameLayout
        android:id="@+id/flLandKLineContainer"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        android:background="?attr/mainLayoutBg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
