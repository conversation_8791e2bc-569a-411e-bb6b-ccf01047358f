{"v": "5.12.2", "fr": 25, "ip": 13, "op": 50, "w": 800, "h": 800, "nm": "Home_Light", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 2, "ty": 4, "nm": "Home", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [400, 689.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-64.113, -56.285, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 13, "s": [3426, 3080, 100]}, {"t": 16, "s": [3426, 3426, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\ntry {\n    amp = $bm_div(effect('Elastic: 缩放')(1), 200);\n    freq = $bm_div(effect('Elastic: 缩放')(2), 30);\n    decay = $bm_div(effect('Elastic: 缩放')(3), 10);\n    $bm_rt = n = 0;\n    if (numKeys > 0) {\n        $bm_rt = n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    if (n == 0) {\n        $bm_rt = t = 0;\n    } else {\n        $bm_rt = t = $bm_sub(time, key(n).time);\n    }\n    if (n > 0) {\n        v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n        $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n    } else {\n        $bm_rt = value;\n    }\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic: 缩放", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 60, "ix": 3}}]}, {"ty": 5, "nm": "Elastic: 路径 - 路径 1", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 60, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 13, "s": [{"i": [[0, 0], [-0.84, -0.882], [0, 0], [0, -0.481], [0, 0], [1.174, 0], [0, 0], [0, 0], [0.587, 0], [0, 0], [0, -0.585], [0, 0], [0, 0], [0, 1.17], [0, 0], [-0.392, 0.412]], "o": [[0.84, -0.882], [0, 0], [0.392, 0.412], [0, 0], [0, 1.17], [0, 0], [0, 0], [0, -0.585], [0, 0], [-0.587, 0], [0, 0], [0, 0], [-1.174, 0], [0, 0], [0, -0.481], [0, 0]], "v": [[-65.687, -72.631], [-62.571, -72.631], [-56.176, -66.15], [-55.609, -64.766], [-55.609, -58.404], [-57.735, -56.285], [-61.987, -56.285], [-61.971, -59.212], [-63.034, -60.271], [-65.16, -60.271], [-66.223, -59.212], [-66.239, -56.285], [-70.491, -56.285], [-72.617, -58.404], [-72.617, -64.766], [-72.049, -66.15]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 16, "s": [{"i": [[0, 0], [-0.84, -0.882], [0, 0], [0, -0.481], [0, 0], [1.174, 0], [0, 0], [0, 0], [0.587, 0], [0, 0], [0, -0.585], [0, 0], [0, 0], [0, 1.17], [0, 0], [-0.392, 0.412]], "o": [[0.84, -0.882], [0, 0], [0.392, 0.412], [0, 0], [0, 1.17], [0, 0], [0, 0], [0, -0.585], [0, 0], [-0.587, 0], [0, 0], [0, 0], [-1.174, 0], [0, 0], [0, -0.481], [0, 0]], "v": [[-65.687, -72.631], [-62.571, -72.631], [-56.176, -66.15], [-55.609, -64.766], [-55.609, -58.404], [-57.735, -56.285], [-61.987, -56.285], [-61.948, -62.362], [-63.011, -63.421], [-65.137, -63.421], [-66.2, -62.362], [-66.239, -56.285], [-70.491, -56.285], [-72.617, -58.404], [-72.617, -64.766], [-72.049, -66.15]], "c": true}]}, {"t": 26, "s": [{"i": [[0, 0], [-0.84, -0.882], [0, 0], [0, -0.481], [0, 0], [1.174, 0], [0, 0], [0, 0], [0.587, 0], [0, 0], [0, -0.585], [0, 0], [0, 0], [0, 1.17], [0, 0], [-0.392, 0.412]], "o": [[0.84, -0.882], [0, 0], [0.392, 0.412], [0, 0], [0, 1.17], [0, 0], [0, 0], [0, -0.585], [0, 0], [-0.587, 0], [0, 0], [0, 0], [-1.174, 0], [0, 0], [0, -0.481], [0, 0]], "v": [[-65.687, -72.631], [-62.571, -72.631], [-56.176, -66.15], [-55.609, -64.766], [-55.609, -58.404], [-57.735, -56.285], [-61.987, -56.285], [-61.987, -60.523], [-63.05, -61.582], [-65.176, -61.582], [-66.239, -60.523], [-66.239, -56.285], [-70.491, -56.285], [-72.617, -58.404], [-72.617, -64.766], [-72.049, -66.15]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.117647058824, 0.117647058824, 0.117647058824, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0.045, 0.028], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 13, "op": 51, "st": 0, "ct": 1, "bm": 0}], "markers": [{"tm": 61, "cm": "1", "dr": 0}, {"tm": 78, "cm": "2", "dr": 0}, {"tm": 103, "cm": "3", "dr": 0}], "props": {}}