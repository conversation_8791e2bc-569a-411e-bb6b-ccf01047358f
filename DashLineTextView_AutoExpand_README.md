# DashLineTextView 自动扩展高度功能

## 问题描述

在使用 `DashLineTextView` 时，当 TextView 的高度设置为 `wrap_content` 时，虚线可能会与带下降字符的英文字母（如 g, j, p, q, y）重合，影响显示效果。

## 解决方案

新增了 `autoExpandHeight` 属性，当启用此功能时，TextView 会根据 `dashOffset` 的值自动增加高度，确保虚线有足够的空间显示而不与文字重合。

## 新增属性

### XML 属性
- `app:dlAutoExpandHeight="true|false"` - 是否自动扩展高度以容纳虚线（默认：true）

### 代码方法
- `setAutoExpandHeight(boolean)` - 设置是否自动扩展高度
- `isAutoExpandHeight()` - 获取当前自动扩展高度设置

## 工作原理

1. **测量阶段**：在 `onMeasure()` 方法中，如果启用了自动扩展高度功能，会计算需要的额外高度
2. **高度计算**：根据不同的对齐模式和 `dashOffset` 值计算所需的额外空间
3. **动态调整**：自动调整 TextView 的测量高度，确保虚线完全显示

## 不同对齐模式的处理

### DESCENT 模式
```kotlin
// 额外高度 = dashOffset + 线条宽度的一半
additionalHeight = dashOffset + dashStrokeWidth / 2
```

### BOTTOM 模式
```kotlin
// 额外高度 = dashOffset + 线条宽度的一半
additionalHeight = dashOffset + dashStrokeWidth / 2
```

### BASELINE 模式
```kotlin
// 计算现有的下方空间，只在不足时添加额外高度
val spaceBelow = fontMetrics.descent + fontMetrics.leading
val requiredSpace = dashOffset + dashStrokeWidth / 2
additionalHeight = max(0f, requiredSpace - spaceBelow)
```

## 使用示例

### XML 布局
```xml
<cn.com.vau.util.widget.DashLineTextView
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:text="Text with descenders: gypsy"
    android:textSize="18sp"
    app:dlDashEnabled="true"
    app:dlDashColor="#FF0000"
    app:dlDashStrokeWidth="2dp"
    app:dlDashLength="6dp"
    app:dlDashGap="4dp"
    app:dlDashOffset="4dp"
    app:dlDashAlignment="descent"
    app:dlAutoExpandHeight="true" />
```

### 代码设置
```kotlin
val dashTextView = DashLineTextView(context).apply {
    text = "Text with descenders: gypsy"
    setDashEnabled(true)
    setDashColor(Color.RED)
    setDashOffset(4f.dp2px())
    setDashAlignment(DashLineTextView.DashAlignment.DESCENT)
    setAutoExpandHeight(true) // 启用自动扩展高度
}
```

## 注意事项

1. **性能影响**：启用自动扩展高度会在每次测量时进行额外计算，但影响很小
2. **布局兼容性**：自动扩展高度只在 `wrap_content` 高度模式下有效
3. **偏移量为负值**：当 `dashOffset` 为负值或零时，不会增加额外高度
4. **重新布局**：当通过代码修改 `dashOffset` 时，如果启用了自动扩展高度，会自动触发重新布局

## 测试建议

建议使用包含下降字符的文本进行测试，如：
- "gypsy jumping quickly"
- "Typography with descenders"
- "Programming language"

这些文本包含 g, j, p, q, y 等带下降部分的字符，可以很好地测试虚线与文字的间距效果。
